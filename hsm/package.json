{"name": "be-playground", "version": "1.0.0", "type": "commonjs", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/sonnguyen2000-github/be-playground.git"}, "author": "", "license": "ISC", "bugs": {"url": "https://github.com/sonnguyen2000-github/be-playground/issues"}, "homepage": "https://github.com/sonnguyen2000-github/be-playground#readme", "dependencies": {"axios": "^1.7.7", "html-pdf-node": "^1.0.8", "jsrsasign": "^11.1.0", "jsrsasign-util": "^1.0.5", "lodash": "^4.17.21", "moment": "^2.30.1"}, "devDependencies": {"typescript": "^5.5.4"}}
const { configDotenv } = require("dotenv");
configDotenv();
const admin = require("firebase-admin");
const { applicationDefault } = require("firebase-admin/app");
// const serviceAccount = require("./cineself-affaa-cdc55264c465.json");
const serviceAccount = require("./ximage---ai-image-generator-55a48c7e97b2.json");
let app = admin.initializeApp({
  // credential: applicationDefault(),
  credential: admin.credential.cert(serviceAccount),
});

// const message = {
//   token:
//     "epsuW_SoQACF7GOIMDxpSU:APA91bGF2GefG5Yb7Jdb1YV31cdQfKbxHw1VLdz22QBhejlrPvdTsRlVQV1AbTc6Tm66diz5XruVWoFjqs-R_mUc6_ZAK1SdYP8XRwIOGJFQ7Ud0dY9Vnec", // or use `topic: 'news'`
//   //   topic: "news",
//   notification: {
//     title: "Hello from gcloud!",
//     body: "This was sent using a service account",
//   },
//   data: {
//     pn_type: "live_audition_invite",
//     audition_id: "8xvhUe1mQ1",
//   },
// };

// admin
//   .messaging()
//   .send(message)
//   .then((response) => {
//     console.log("✅ Successfully sent message:", response);
//   })
//   .catch((error) => {
//     console.error("❌ Error sending message:", error);
//   });

// app
//   .auth()
//   .verifyIdToken(
//     "eyJhbGciOiJSUzI1NiIsImtpZCI6IjQ3YWU0OWM0YzlkM2ViODVhNTI1NDA3MmMzMGQyZThlNzY2MWVmZTEiLCJ0eXAiOiJKV1QifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Udb2vBq7kD5XWd-EkCKP_toQphAcvEb7wabHYBAPCBoQbHaxguusBAqOSuji7j-Gbk5uBC1B3L45ojyASBbHhnuTdC2oJic9f7_0vklDASwjuD-whmh00DbNsUY3vhbB-n7y-ZeKUYkLu70_Pkym__VsJ0i_-2zJ22DtTS-eslWn2zxTxuGlabdrqE3MbAUQMXu-2n52cXGdtn-KCXFnmyVzTxaeWK3_LWLpN7lnOtEICcG0_KM5M9d_yJMCRIqnFTbq81SpKhBB6RV6bkuYzF4FauD0R4IsZpfDsrkD_Of2gty4nK89yjWoMpEAE9RSMqAlmPOe4AC9J-haOwRghA"
//   )
//   .then(console.log)
//   .catch(console.error)
//   .finally(() => {
//     process.exit(0);
//   });

let token =
  "dKA7xNHwQdKI3KLelwQEP7:APA91bF2teTHy0bWRBhb7irfCErI2yH94QGDQabU3kdl9IPeIx_JtjbgcIcLWHOYIuA58LGVtoTzemQ19nblLuqngiam1zONPF_GnpIQn9BWiVDpI_Duxcw";

let data = {
  key1: "value1",
  key2: "value2",
  key3: "value3",
};
app
  .messaging()
  .send({
    token: token, // or use `topic: 'news'`
    data: data,
    notification: { title: "Hello", body: "This is notification body" },
    android: {
      notification: {
        title: "Hello",
        body: "This is notification body",
        // clickAction: "FLUTTER_NOTIFICATION_CLICK",
      },
      data: data,
    },
  })
  .then((response) => {
    console.log("✅ Successfully sent message:", response);
  })
  .catch((error) => {
    console.error("❌ Error sending message:", error);
  })
  .finally(() => {
    process.exit(0);
  });

// app
//   .auth()
//   .createCustomToken("HFLzxzdoLaRl4Il8fuHrUN3LX6d2")
//   .then((token) => {
//     app
//       .auth()
//       .verifyIdToken(
//         "eyJhbGciOiJSUzI1NiIsImtpZCI6IjQ3YWU0OWM0YzlkM2ViODVhNTI1NDA3MmMzMGQyZThlNzY2MWVmZTEiLCJ0eXAiOiJKV1QifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Udb2vBq7kD5XWd-EkCKP_toQphAcvEb7wabHYBAPCBoQbHaxguusBAqOSuji7j-Gbk5uBC1B3L45ojyASBbHhnuTdC2oJic9f7_0vklDASwjuD-whmh00DbNsUY3vhbB-n7y-ZeKUYkLu70_Pkym__VsJ0i_-2zJ22DtTS-eslWn2zxTxuGlabdrqE3MbAUQMXu-2n52cXGdtn-KCXFnmyVzTxaeWK3_LWLpN7lnOtEICcG0_KM5M9d_yJMCRIqnFTbq81SpKhBB6RV6bkuYzF4FauD0R4IsZpfDsrkD_Of2gty4nK89yjWoMpEAE9RSMqAlmPOe4AC9J-haOwRghA"
//       )
//       .then(console.log)
//       .finally(() => {
//         process.exit(0);
//       });
//   });

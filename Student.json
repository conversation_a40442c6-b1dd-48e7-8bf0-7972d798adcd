{"skeleton": {"hash": "ZaLOtqn9kpk", "spine": "4.0.64", "x": -898.32, "y": -101.66, "width": 1839.65, "height": 3160.12, "images": "./images/", "audio": "D:/winstonsmgx/OneDrive/UMEE/UMEE/SPINE PROJECT"}, "bones": [{"name": "root"}, {"name": "BODY", "parent": "root", "length": 2035.28, "rotation": 90.43, "x": 34.93, "y": 441.97}, {"name": "L ARM", "parent": "BODY", "length": 254.15, "rotation": 147.96, "x": 1170.91, "y": 618.42}, {"name": "L ARM1", "parent": "L ARM", "length": 314.17, "rotation": 15.54, "x": 254.15}, {"name": "L ARM2", "parent": "L ARM1", "length": 99.47, "rotation": -33.27, "x": 218.38, "y": -30.57}, {"name": "L ARM3", "parent": "L ARM1", "length": 99.35, "rotation": 42.21, "x": 217.21, "y": 22.09}, {"name": "R ARM", "parent": "BODY", "length": 258.81, "rotation": -143.15, "x": 1149.57, "y": -562.93}, {"name": "R ARM 1", "parent": "R ARM", "length": 317.84, "rotation": -16, "x": 258.81}, {"name": "R ARM 3", "parent": "R ARM 1", "length": 100.99, "rotation": 24.82, "x": 215.48, "y": 27.65}, {"name": "R ARM 2", "parent": "R ARM 1", "length": 118.9, "rotation": -37.16, "x": 203, "y": -16.98}, {"name": "TIE 1", "parent": "BODY", "length": 673.77, "rotation": -154.34, "x": 1111.13, "y": 5.89}, {"name": "TIE 2", "parent": "BODY", "length": 489.09, "rotation": 148.71, "x": 1112.56, "y": 12.16}, {"name": "L LEG", "parent": "BODY", "length": 349.77, "rotation": 169.64, "x": 137.9, "y": 298.04}, {"name": "L LEG 2", "parent": "L LEG", "length": 230.49, "rotation": 14.25, "x": 349.77}, {"name": "L LEG 3", "parent": "L LEG 2", "length": 109.35, "rotation": -88.91, "x": 230.49}, {"name": "L LEG2", "parent": "root", "x": -334.68, "y": 11.46, "color": "ff3f00ff"}, {"name": "R LEG", "parent": "BODY", "length": 350.76, "rotation": -162.89, "x": 134.56, "y": -289.31}, {"name": "R LEG 2", "parent": "R LEG", "length": 228.56, "rotation": -20.98, "x": 350.76}, {"name": "R LEG 3", "parent": "R LEG 2", "length": 116.75, "rotation": 92.91, "x": 228.56}, {"name": "r leg", "parent": "root", "x": 399.53, "y": 13.67, "color": "ff3f00ff"}, {"name": "R EYE", "parent": "BODY", "rotation": -90.43, "x": 1691.79, "y": -379.19}, {"name": "L EYE", "parent": "BODY", "rotation": -90.43, "x": 1651.93, "y": 253.29, "color": "abe323ff"}, {"name": "R BLUSH", "parent": "BODY", "rotation": -90.43, "x": 1416.81, "y": -428.04}, {"name": "L BLUSH", "parent": "BODY", "rotation": -90.43, "x": 1397.98, "y": 403.22}, {"name": "R EYELID", "parent": "BODY", "length": 381.21, "rotation": -90.32, "x": 1647.59, "y": -124.94}, {"name": "L EYELID", "parent": "BODY", "length": 410, "rotation": -90.87, "x": 1650.64, "y": 451.74}, {"name": "L CHEEK", "parent": "BODY", "length": 102.78, "rotation": -89.64, "x": 1408.38, "y": 290.1}, {"name": "R CHEEK", "parent": "BODY", "length": 165.21, "rotation": 90.06, "x": 1414.06, "y": -306.83}, {"name": "EATING", "parent": "BODY", "x": 1362.75, "y": 51.98}, {"name": "MOUTH", "parent": "BODY", "x": 1429.98, "y": 2.21}, {"name": "EYES_EATING", "parent": "BODY", "length": 255.92, "rotation": 89.57, "x": 1727.68, "y": -88.24}, {"name": "L EATING CHEEK", "parent": "BODY", "length": 206.87, "rotation": -1.17, "x": 1380.94, "y": 351.98}, {"name": "R EATING CHEEK", "parent": "BODY", "length": 218.81, "rotation": -1.13, "x": 1358.22, "y": -371.86}], "slots": [{"name": "Shadow", "bone": "root", "attachment": "Shadow"}, {"name": "BODY", "bone": "BODY", "attachment": "BODY"}, {"name": "HORN", "bone": "BODY", "attachment": "HORN"}, {"name": "R LEG", "bone": "R LEG", "attachment": "R LEG"}, {"name": "L LEG", "bone": "L LEG", "attachment": "L LEG"}, {"name": "L UPPER LEG", "bone": "L LEG", "attachment": "L UPPER LEG"}, {"name": "R UPPER LEG", "bone": "R LEG", "attachment": "R UPPER LEG"}, {"name": "R eyelid", "bone": "R EYELID", "attachment": "R eyelid"}, {"name": "L eyelid", "bone": "L EYELID", "attachment": "L eyelid"}, {"name": "L eye", "bone": "BODY", "attachment": "L eye"}, {"name": "R eye", "bone": "BODY", "attachment": "R eye"}, {"name": "L cheek", "bone": "L CHEEK", "attachment": "L cheek"}, {"name": "<PERSON> <PERSON>", "bone": "R CHEEK", "attachment": "<PERSON> <PERSON>"}, {"name": "MOUTH2", "bone": "MOUTH", "attachment": "MOUTH4"}, {"name": "L EATING CHEEK_", "bone": "L EATING CHEEK", "attachment": "L EATING CHEEK_"}, {"name": "R EATING CHEEK_", "bone": "R EATING CHEEK", "attachment": "R EATING CHEEK_"}, {"name": "EYE 3", "bone": "EYES_EATING", "attachment": "EYE 3"}, {"name": "SHIRT", "bone": "BODY", "attachment": "SHIRT"}, {"name": "PANTS", "bone": "BODY", "attachment": "PANTS"}, {"name": "Tie 2", "bone": "TIE 2", "attachment": "Tie 2"}, {"name": "Tie", "bone": "TIE 1", "attachment": "Tie"}, {"name": "R blush", "bone": "BODY", "attachment": "R blush"}, {"name": "L blush", "bone": "BODY", "attachment": "L blush"}, {"name": "L ARM", "bone": "L ARM", "attachment": "L ARM"}, {"name": "R ARM", "bone": "R ARM", "attachment": "R ARM"}, {"name": "R SHIRT ARM", "bone": "R ARM", "attachment": "R SHIRT ARM"}, {"name": "L SHIRT ARM", "bone": "L ARM", "attachment": "L SHIRT ARM"}], "ik": [{"name": "L LEG IK", "bones": ["L LEG", "L LEG 2"], "target": "L LEG2"}, {"name": "r leg", "order": 1, "bones": ["R LEG", "R LEG 2"], "target": "r leg", "bendPositive": false}], "transform": [{"name": "L EYE", "order": 2, "bones": ["R EYE"], "target": "L EYE", "local": true, "x": -4.08, "y": -560.93}], "skins": [{"name": "Skin_1", "attachments": {"BODY": {"BODY": {"name": "Skin_1/BODY", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-28.19, -617.06, -18.64, 625.9, 2616.28, 605.66, 2606.73, -637.3], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 1243, "height": 2635}}, "EYE 3": {"EYE 3": {"name": "Skin_1/EYE 3", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-448.76, 169.44, 630.24, 169.44, 630.24, -5.56, -448.76, -5.56], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 1079, "height": 175}}, "HORN": {"HORN": {"name": "Skin_1/HORN", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [2261.93, -345.61, 2267.91, 443.37, 2619.9, 440.7, 2613.92, -348.28], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 789, "height": 352}}, "L ARM": {"L ARM": {"name": "Skin_1/L ARM", "type": "mesh", "uvs": [0.85918, 0.0008, 0.80947, 0.01764, 0.7294, 0.06308, 0.66865, 0.10853, 0.56648, 0.19943, 0.51401, 0.25666, 0.49246, 0.28129, 0.46983, 0.30715, 0.44534, 0.33936, 0.42889, 0.36101, 0.41737, 0.37617, 0.40273, 0.40293, 0.38423, 0.43676, 0.37328, 0.45482, 0.35403, 0.48657, 0.33729, 0.51419, 0.2793, 0.62529, 0.23512, 0.71955, 0.15504, 0.75826, 0.04459, 0.81886, 0.0004, 0.85589, 0, 0.92154, 0.00593, 0.94847, 0.08877, 0.95183, 0.17161, 0.91649, 0.21855, 0.87104, 0.18265, 0.96025, 0.19646, 0.99728, 0.30967, 1, 0.36766, 0.99055, 0.38423, 0.91144, 0.41737, 0.9855, 0.48364, 1, 0.63275, 0.99896, 0.59961, 0.86767, 0.49744, 0.79024, 0.44498, 0.7448, 0.50021, 0.67578, 0.54439, 0.59499, 0.57274, 0.5285, 0.58028, 0.51082, 0.59042, 0.49537, 0.60428, 0.47426, 0.61497, 0.45796, 0.62446, 0.44349, 0.63525, 0.42788, 0.64988, 0.4067, 0.66865, 0.37953, 0.68858, 0.35455, 0.71835, 0.31725, 0.80947, 0.23646, 0.8647, 0.18428, 0.98068, 0.1119, 1, 0.06308, 1, 0, 0.90612, 0], "triangles": [25, 18, 17, 19, 18, 25, 24, 19, 25, 20, 19, 24, 21, 20, 24, 23, 22, 21, 24, 23, 21, 35, 30, 36, 30, 35, 34, 32, 31, 30, 34, 32, 30, 33, 32, 34, 43, 11, 44, 42, 12, 11, 43, 42, 11, 41, 12, 42, 13, 12, 41, 40, 13, 41, 14, 13, 40, 39, 14, 40, 39, 15, 14, 38, 15, 39, 16, 15, 38, 37, 16, 38, 36, 17, 16, 37, 36, 16, 25, 17, 36, 30, 25, 36, 25, 30, 26, 30, 28, 26, 28, 27, 26, 30, 29, 28, 55, 54, 53, 53, 0, 55, 0, 52, 1, 53, 52, 0, 52, 2, 1, 51, 2, 52, 3, 2, 51, 50, 3, 51, 4, 3, 50, 49, 4, 50, 5, 4, 49, 49, 6, 5, 48, 6, 49, 7, 6, 48, 47, 7, 48, 8, 7, 47, 46, 8, 47, 9, 8, 46, 45, 9, 46, 10, 9, 45, 44, 11, 10, 45, 44, 10], "vertices": [1, 2, -0.81, -28.11, 1, 1, 2, 15.63, -37.27, 1, 1, 2, 50.57, -46.78, 1, 1, 2, 82.15, -50.84, 1, 1, 2, 141.91, -53.55, 1, 2, 2, 177.48, -51.92, 0.99984, 3, -87.78, -29.48, 0.00016, 2, 2, 192.61, -50.92, 0.99414, 3, -72.94, -32.57, 0.00586, 2, 2, 208.49, -49.88, 0.95726, 3, -57.36, -35.82, 0.04274, 2, 2, 227.62, -47.54, 0.85877, 3, -38.3, -38.7, 0.14123, 2, 2, 240.48, -45.97, 0.75052, 3, -25.49, -40.63, 0.24948, 2, 2, 249.48, -44.87, 0.67473, 3, -16.52, -41.98, 0.32527, 2, 2, 264.39, -41.32, 0.44197, 3, -1.21, -42.56, 0.55803, 3, 2, 283.22, -36.84, 0.22181, 3, 18.14, -43.28, 0.77527, 4, -160.45, -120.46, 0.00292, 3, 2, 293.46, -34.75, 0.18833, 3, 28.57, -44.01, 0.79551, 4, -151.34, -115.35, 0.01616, 3, 2, 311.48, -31.07, 0.12943, 3, 46.91, -45.29, 0.83112, 4, -135.3, -106.36, 0.03945, 3, 2, 327.14, -27.87, 0.0782, 3, 62.86, -46.41, 0.86208, 4, -121.35, -98.55, 0.05971, 3, 2, 388.52, -12.36, 0.01452, 3, 126.14, -47.91, 0.75977, 4, -67.61, -65.09, 0.22571, 2, 3, 179.37, -47.59, 0.50302, 4, -23.28, -35.63, 0.49698, 2, 3, 206.99, -67.14, 0.22839, 4, 10.53, -36.82, 0.77161, 2, 3, 248.82, -93.01, 0.06105, 4, 59.7, -35.5, 0.93895, 2, 3, 272.23, -101.4, 0.00191, 4, 83.87, -29.69, 0.99809, 1, 4, 107.1, -2.65, 1, 2, 3, 319.97, -85.55, 0.0025, 4, 115.09, 9.76, 0.9975, 2, 3, 314.03, -58.72, 0.05225, 4, 95.41, 28.94, 0.94775, 2, 3, 287.92, -37.78, 0.21295, 4, 62.09, 32.12, 0.78705, 2, 3, 259.87, -29.79, 0.47975, 4, 34.26, 23.42, 0.52025, 2, 3, 309.7, -27.61, 0.76317, 4, 74.73, 52.57, 0.23683, 3, 3, 327.72, -17.58, 0.93502, 5, 55.19, -103.63, 0.00079, 4, 84.3, 70.84, 0.0642, 3, 3, 318.63, 18.8, 0.94075, 5, 72.9, -70.58, 0.05908, 4, 56.74, 96.27, 0.00017, 2, 3, 308.32, 35.79, 0.7723, 5, 76.68, -51.07, 0.2277, 2, 3, 265.55, 29, 0.49413, 5, 40.44, -27.36, 0.50587, 2, 3, 301.07, 50.81, 0.21986, 5, 81.41, -35.07, 0.78014, 2, 3, 302.48, 74.07, 0.05516, 5, 98.08, -18.79, 0.94484, 2, 3, 288.1, 121.29, 0.01372, 5, 119.15, 25.85, 0.98628, 2, 3, 222.74, 90.77, 0.11575, 5, 50.24, 47.15, 0.88425, 2, 3, 191.87, 46.52, 0.32967, 5, -2.36, 35.12, 0.67033, 2, 3, 173.05, 22.93, 0.62881, 5, -32.15, 30.29, 0.37119, 3, 2, 373.25, 64.22, 0.00125, 3, 131.95, 29.96, 0.85809, 5, -57.86, 63.11, 0.14066, 3, 2, 328.26, 53.51, 0.02426, 3, 85.74, 31.7, 0.95449, 5, -90.92, 95.45, 0.02125, 3, 2, 292.64, 42.45, 0.09612, 3, 48.46, 30.58, 0.89942, 5, -119.29, 119.67, 0.00446, 2, 2, 283.17, 39.51, 0.11523, 3, 38.54, 30.29, 0.88477, 2, 2, 274.27, 37.93, 0.19708, 3, 29.55, 31.15, 0.80292, 2, 2, 262.11, 35.78, 0.30894, 3, 17.26, 32.34, 0.69106, 2, 2, 252.73, 34.12, 0.44725, 3, 7.77, 33.26, 0.55275, 2, 2, 244.39, 32.65, 0.57003, 3, -0.65, 34.07, 0.42997, 2, 2, 235.31, 31.21, 0.67032, 3, -9.79, 35.12, 0.32968, 2, 2, 222.98, 29.26, 0.80632, 3, -22.19, 36.54, 0.19368, 2, 2, 207.17, 26.76, 0.94449, 3, -38.09, 38.37, 0.05551, 2, 2, 192.17, 25.21, 0.99372, 3, -52.96, 40.89, 0.00628, 2, 2, 169.76, 22.9, 0.99979, 3, -75.16, 44.67, 0.00021, 1, 2, 116.58, 25.39, 1, 1, 2, 82.87, 25.98, 1, 1, 2, 29.23, 37.87, 1, 1, 2, 3.34, 29.33, 1, 1, 2, -25.77, 11.26, 1, 1, 2, -9.38, -15.14, 1], "hull": 56, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 14, 16, 92, 94, 94, 96, 96, 98, 10, 12, 12, 14, 20, 22, 22, 24, 0, 110, 16, 18, 18, 20, 88, 90, 90, 92, 24, 26, 80, 82, 82, 84, 76, 78, 78, 80, 84, 86, 86, 88, 26, 28, 28, 30], "width": 331, "height": 543}}, "L blush": {"L blush": {"name": "Skin_1/L blush", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 23, 163.19, -73.33, 1, 1, 23, -159.81, -73.34, 1, 1, 23, -159.81, 83.67, 1, 1, 23, 163.19, 83.66, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 323, "height": 157}}, "L cheek": {"L cheek": {"name": "Skin_1/L cheek", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [181.94, -152.48, -185.02, -147.38, -181.31, 119.59, 185.65, 114.49], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 367, "height": 267}}, "L EATING CHEEK_": {"L EATING CHEEK_": {"name": "Skin_1/L EATING CHEEK_", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-226.33, -286.32, -233.83, 298.63, 213.14, 304.36, 220.64, -280.59], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 585, "height": 447}}, "L eye": {"L eye": {"name": "Skin_1/L eye", "type": "mesh", "uvs": [0.82844, 0.00491, 1, 0.37257, 1, 0.65758, 0.77556, 0.99495, 0.26896, 0.99691, 0.00448, 0.71659, 0.00404, 0.27265, 0.2704, 0.00367], "triangles": [1, 7, 0, 5, 6, 7, 5, 7, 4, 2, 3, 1, 7, 1, 4, 3, 4, 1], "vertices": [1, 21, 71.04, 99.92, 1, 1, 21, 107.24, 25.65, 1, 1, 21, 107.24, -31.92, 1, 1, 21, 59.89, -100.07, 1, 1, 21, -47.01, -100.47, 1, 1, 21, -102.81, -43.84, 1, 1, 21, -102.9, 45.83, 1, 1, 21, -46.7, 100.17, 1], "hull": 8, "edges": [0, 14, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14], "width": 211, "height": 202}}, "L eyelid": {"L eyelid": {"name": "Skin_1/L eyelid", "type": "mesh", "uvs": [0.99868, 0.29996, 0.99769, 0.73106, 0.84433, 0.99708, 0.67234, 0.99663, 0.4761, 0.99619, 0.31732, 0.99569, 0.16334, 0.99528, 0.0021, 0.76412, 0.00088, 0.27372, 0.14864, 0.00451, 0.85577, 0.00432], "triangles": [10, 2, 3, 6, 7, 8, 5, 6, 9, 10, 5, 9, 6, 8, 9, 10, 4, 5, 10, 3, 4, 2, 10, 0, 1, 2, 0], "vertices": [413.2, 48.61, 413.46, -42.36, 347.18, -98.99, 272.36, -99.46, 187, -100.02, 117.93, -100.43, 50.95, -100.86, -19.55, -52.61, -20.87, 50.85, 42.98, 108.14, 350.57, 110.51], "hull": 11, "edges": [0, 20, 0, 2, 2, 4, 12, 14, 14, 16, 16, 18, 18, 20, 10, 12, 4, 6, 6, 8, 8, 10], "width": 435, "height": 211}}, "L LEG": {"L LEG": {"name": "Skin_1/L LEG", "type": "mesh", "uvs": [0.99837, 0.01435, 0.99826, 0.06071, 0.888, 0.13491, 0.86717, 0.17821, 0.72578, 0.37534, 0.64765, 0.5523, 0.63845, 0.57315, 0.63538, 0.58419, 0.63014, 0.60309, 0.62863, 0.62492, 0.62633, 0.64524, 0.62275, 0.67625, 0.61858, 0.73233, 0.63594, 0.99401, 0.63163, 0.99939, 0.04386, 0.99936, 0.00127, 0.98373, 0.00171, 0.94079, 0.05715, 0.92357, 0.34331, 0.92358, 0.39002, 0.92191, 0.38974, 0.72883, 0.38971, 0.70858, 0.38966, 0.67933, 0.38963, 0.65371, 0.39247, 0.63432, 0.39456, 0.62009, 0.39679, 0.60489, 0.39908, 0.5893, 0.40085, 0.57729, 0.40425, 0.55408, 0.40799, 0.52866, 0.4104, 0.51224, 0.41279, 0.49592, 0.42069, 0.47835, 0.4368, 0.38461, 0.53443, 0.23078, 0.64958, 0.12262, 0.76309, 0.03478, 0.83636, 0.00039, 0.95475, 0.00043], "triangles": [14, 15, 19, 15, 18, 19, 19, 20, 14, 15, 16, 18, 18, 16, 17, 14, 20, 13, 20, 12, 13, 20, 21, 12, 11, 12, 22, 12, 21, 22, 22, 23, 11, 23, 24, 11, 11, 24, 10, 24, 25, 10, 10, 25, 9, 25, 26, 9, 26, 27, 9, 9, 27, 8, 27, 28, 8, 8, 28, 7, 28, 29, 7, 7, 29, 6, 29, 30, 6, 6, 30, 5, 30, 31, 5, 31, 32, 5, 32, 33, 5, 33, 34, 5, 5, 34, 4, 4, 34, 35, 35, 36, 4, 4, 36, 3, 36, 37, 3, 3, 37, 2, 37, 38, 2, 2, 38, 1, 1, 39, 40, 39, 1, 38, 1, 40, 0], "vertices": [1, 12, -8.72, 8.53, 1, 1, 12, 18.09, 15.37, 1, 1, 12, 67, 2.97, 1, 1, 12, 93.17, 4.97, 1, 1, 12, 214.86, 4.18, 1, 1, 12, 321.45, 13.82, 1, 2, 12, 334.01, 14.96, 0.95163, 13, -11.59, 18.38, 0.04837, 2, 12, 340.56, 15.94, 0.78179, 13, -5, 17.72, 0.21821, 2, 12, 351.77, 17.63, 0.27727, 13, 6.28, 16.6, 0.72273, 2, 12, 364.48, 20.55, 0.01166, 13, 19.32, 16.29, 0.98834, 1, 13, 31.45, 15.81, 1, 1, 13, 49.96, 15.07, 1, 1, 13, 83.44, 14.23, 1, 2, 13, 239.66, 18.35, 0.74511, 14, -18.18, 9.51, 0.25489, 2, 13, 242.87, 17.42, 0.73856, 14, -17.18, 12.71, 0.26144, 1, 14, 111.52, 10.51, 1, 1, 14, 120.69, 1.03, 1, 1, 14, 120.16, -24.6, 1, 1, 14, 107.85, -34.68, 1, 2, 13, 197.75, -45.82, 0.21388, 14, 45.19, -33.61, 0.78612, 2, 13, 196.73, -35.59, 0.48714, 14, 34.94, -34.43, 0.51286, 1, 13, 81.46, -35.9, 1, 1, 13, 69.37, -35.93, 1, 1, 13, 51.91, -35.97, 1, 2, 12, 394.12, -25.89, 0.00201, 13, 36.61, -36.01, 0.99799, 2, 12, 382.75, -28.16, 0.03637, 13, 25.04, -35.41, 0.96363, 2, 12, 374.4, -29.83, 0.10956, 13, 16.54, -34.97, 0.89044, 2, 12, 365.49, -31.61, 0.25181, 13, 7.46, -34.51, 0.74819, 2, 12, 356.35, -33.43, 0.46505, 13, -1.84, -34.02, 0.53495, 2, 12, 349.31, -34.84, 0.63415, 13, -9.02, -33.65, 0.36585, 2, 12, 335.71, -37.55, 0.88043, 13, -22.87, -32.94, 0.11957, 2, 12, 320.8, -40.53, 0.98199, 13, -38.05, -32.15, 0.01801, 2, 12, 311.17, -42.45, 0.99815, 13, -47.85, -31.64, 0.00185, 1, 12, 301.61, -44.36, 1, 1, 12, 291.01, -45.28, 1, 1, 12, 235.93, -55.75, 1, 1, 12, 141.66, -57.83, 1, 1, 12, 72.85, -49.43, 1, 1, 12, 15.88, -38.36, 1, 1, 12, -7.99, -27.91, 1, 1, 12, -14.4, -2.79, 1], "hull": 41, "edges": [0, 80, 0, 2, 2, 4, 4, 6, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 74, 76, 76, 78, 78, 80, 66, 68, 64, 66, 48, 50, 54, 56, 16, 18, 18, 20, 20, 22, 46, 48, 44, 46, 40, 42, 42, 44, 50, 52, 52, 54, 56, 58, 58, 60, 68, 70, 6, 8, 72, 74, 70, 72, 12, 14, 14, 16, 8, 10, 10, 12, 60, 62, 62, 64], "width": 219, "height": 597}}, "L SHIRT ARM": {"L SHIRT ARM": {"name": "Skin_1/L SHIRT ARM", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 3, 0, 1, 2, 3], "vertices": [1, 2, 207.31, 222.94, 1, 1, 2, 360.34, -25.75, 1, 1, 2, 71.63, -203.41, 1, 2, 2, -81.4, 45.27, 0.99999, 3, -311.16, 133.52, 1e-05], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 292, "height": 339}}, "L UPPER LEG": {"L UPPER LEG": {"name": "Skin_1/L UPPER LEG", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 12, 212.97, 246.1, 1, 1, 12, 311.71, -107.37, 1, 1, 12, -117.85, -227.36, 1, 1, 12, -216.58, 126.11, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 367, "height": 446}}, "MOUTH2": {"Mouth": {"name": "Skin_1/Mouth", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-65.59, -101.74, -64.16, 87.25, 0.84, 86.76, -0.59, -102.23], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 189, "height": 65}, "MOUTH2": {"name": "Skin_1/MOUTH2", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-20.56, -92.53, -19.69, 22.47, 120.31, 21.41, 119.44, -93.59], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 115, "height": 140}, "MOUTH3": {"name": "Skin_1/MOUTH3", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-21.02, -71.06, -19.96, 68.94, 40.04, 68.48, 38.98, -71.51], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 140, "height": 60}, "MOUTH4": {"name": "Skin_1/MOUTH4", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-243.68, -594.61, -234.56, 608.36, 40.43, 606.28, 31.31, -596.69], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 1203, "height": 275}, "MOUTH5": {"name": "Skin_1/MOUTH5", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-10.7, -129.24, -8.96, 100.76, 69.04, 100.17, 67.29, -129.83], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 230, "height": 78}, "MOUTH6": {"name": "Skin_1/MOUTH6", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-56.15, -88.84, -54.8, 89.15, 59.2, 88.29, 57.85, -89.71], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 178, "height": 114}}, "PANTS": {"PANTS": {"name": "Skin_1/PANTS", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-47.37, -593.53, -38.1, 629.44, 349.89, 626.5, 340.62, -596.47], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 1223, "height": 388}}, "R ARM": {"R ARM": {"name": "Skin_1/R ARM", "type": "mesh", "uvs": [1, 0.84045, 1, 0.90059, 1, 1, 0.81098, 1, 0.72817, 1, 0.67825, 1, 0.60111, 1, 0.45363, 1, 0, 1, 0, 0.08806, 0, 0, 0.15939, 0, 1, 0, 0.2285, 0.04417, 0.29565, 0.09688, 0.41018, 0.18649, 0.48917, 0.27873, 0.54644, 0.34198, 0.57804, 0.37492, 0.60174, 0.41841, 0.62346, 0.44477, 0.64321, 0.47903, 0.51484, 0.31431, 0.5647, 0.36131, 0.59135, 0.40053, 0.63031, 0.45937, 0.68099, 0.5218, 0.71561, 0.57879, 0.78716, 0.6866, 0.80794, 0.72511, 0.78255, 0.66042, 0.79319, 0.70588, 0.81335, 0.7404, 0.90278, 0.7796, 0.95363, 0.8106, 0.98234, 0.92047, 0.89871, 0.93041, 0.8357, 0.88454, 0.80935, 0.85931, 0.78987, 0.84937, 0.80706, 0.87842, 0.81508, 0.89218, 0.8483, 0.96175, 0.67258, 0.95893, 0.66124, 0.91654, 0.6601, 0.87566, 0.64648, 0.89307, 0.63741, 0.91124, 0.62947, 0.93092, 0.44682, 0.90973, 0.49447, 0.81964, 0.55006, 0.77574, 0.60111, 0.74773, 0.54812, 0.69582, 0.49943, 0.62937, 0.47067, 0.56883, 0.45075, 0.50533, 0.42641, 0.46546, 0.40871, 0.43888, 0.3733, 0.4064, 0.39599, 0.42659, 0.41893, 0.45262, 0.43576, 0.48272, 0.4564, 0.52763, 0.31304, 0.35129, 0.05703, 0.11888], "triangles": [6, 48, 43, 48, 6, 49, 8, 49, 7, 50, 49, 8, 6, 7, 49, 48, 47, 44, 48, 49, 47, 46, 47, 50, 47, 49, 50, 54, 50, 8, 46, 50, 45, 50, 51, 45, 51, 52, 45, 50, 53, 51, 51, 53, 52, 42, 36, 2, 36, 35, 2, 35, 1, 2, 36, 41, 37, 35, 36, 1, 1, 36, 37, 1, 37, 0, 37, 34, 0, 41, 40, 37, 40, 38, 37, 37, 38, 34, 40, 39, 38, 38, 33, 34, 38, 39, 33, 39, 32, 33, 32, 52, 29, 29, 52, 31, 52, 32, 39, 12, 0, 34, 33, 12, 34, 52, 28, 31, 33, 32, 29, 33, 30, 12, 28, 30, 29, 29, 31, 28, 33, 29, 30, 3, 42, 2, 3, 4, 42, 42, 4, 43, 6, 43, 5, 4, 5, 43, 55, 8, 59, 55, 59, 60, 63, 55, 60, 62, 63, 60, 57, 58, 61, 43, 41, 42, 42, 41, 36, 48, 44, 43, 43, 44, 41, 47, 46, 44, 46, 45, 44, 44, 40, 41, 40, 45, 39, 40, 44, 45, 62, 60, 58, 63, 62, 56, 58, 57, 62, 45, 52, 39, 50, 54, 53, 54, 8, 55, 28, 52, 30, 52, 53, 30, 53, 27, 30, 53, 54, 27, 30, 27, 12, 54, 26, 27, 54, 55, 26, 27, 26, 12, 55, 21, 26, 55, 63, 21, 21, 56, 25, 21, 63, 56, 12, 26, 20, 56, 20, 25, 56, 62, 20, 62, 19, 20, 62, 57, 19, 20, 26, 21, 57, 61, 19, 21, 25, 20, 61, 24, 19, 61, 58, 24, 12, 20, 24, 58, 18, 24, 58, 23, 18, 20, 19, 24, 24, 18, 12, 8, 64, 59, 8, 65, 64, 65, 14, 64, 65, 13, 14, 8, 9, 65, 58, 60, 23, 60, 17, 23, 60, 59, 17, 59, 22, 17, 59, 64, 22, 18, 23, 12, 23, 17, 12, 64, 16, 22, 64, 15, 16, 64, 14, 15, 12, 17, 16, 17, 22, 16, 16, 15, 12, 15, 14, 12, 65, 11, 13, 65, 9, 11, 14, 13, 12, 9, 10, 11, 13, 11, 12], "vertices": [1, 8, 92.49, 35.25, 1, 2, 7, 314.66, 86.9, 0.00301, 8, 114.89, 12.15, 0.99699, 2, 7, 364.29, 67.78, 0.21073, 8, 151.91, -26.03, 0.78927, 3, 7, 340.03, 4.81, 0.88719, 8, 103.47, -73.01, 0.11239, 9, 96.05, 100.14, 0.00041, 2, 7, 329.41, -22.78, 0.87527, 9, 104.25, 71.73, 0.12473, 2, 7, 323, -39.4, 0.66146, 9, 109.19, 54.61, 0.33854, 2, 7, 313.1, -65.1, 0.24831, 9, 116.82, 28.15, 0.75169, 1, 9, 131.41, -22.44, 1, 3, 6, 412.5, -320.12, 0.00422, 7, 235.97, -265.36, 0.00444, 9, 176.3, -178.04, 0.99134, 1, 6, 23.19, -26.06, 1, 1, 6, -14.41, 2.34, 1, 1, 6, 19.89, 47.74, 1, 3, 6, 200.76, 287.21, 0.79744, 7, -134.96, 260.08, 0.19314, 8, -220.53, 358.05, 0.00941, 1, 6, 53.62, 53.19, 1, 2, 6, 90.57, 55.32, 0.99985, 7, -176.97, 6.81, 0.00015, 3, 6, 153.46, 59.05, 0.99198, 7, -117.54, 27.73, 0.00774, 8, -302.24, 139.84, 0.00028, 3, 6, 209.84, 51.81, 0.93796, 7, -61.35, 36.31, 0.05844, 8, -247.64, 124.04, 0.0036, 3, 6, 249.17, 47.73, 0.68378, 7, -22.42, 43.22, 0.30485, 8, -209.4, 113.98, 0.01137, 3, 6, 270.03, 46.11, 0.41391, 7, -1.92, 47.41, 0.56652, 8, -189.04, 109.18, 0.01957, 3, 6, 293.69, 38.84, 0.15205, 7, 22.83, 46.95, 0.8174, 8, -166.77, 98.37, 0.03056, 3, 6, 309.62, 36.53, 0.07663, 7, 38.77, 49.11, 0.87956, 8, -151.38, 93.64, 0.04381, 3, 6, 328.49, 31.1, 0.0304, 7, 58.41, 49.11, 0.90751, 8, -133.56, 85.39, 0.0621, 3, 6, 230.55, 47.65, 0.85742, 7, -40.29, 38.02, 0.13622, 8, -227.81, 116.75, 0.00635, 3, 6, 261.35, 46.69, 0.52519, 7, -10.43, 45.59, 0.45925, 8, -197.52, 111.09, 0.01556, 3, 6, 283.83, 41.64, 0.24386, 7, 12.57, 46.92, 0.73109, 8, -176.09, 102.65, 0.02505, 3, 6, 317.33, 33.77, 0.05133, 7, 46.94, 48.59, 0.89902, 8, -144.19, 89.73, 0.04964, 3, 6, 354.88, 28.07, 0.01219, 7, 84.62, 53.47, 0.87645, 8, -107.95, 78.35, 0.11136, 3, 6, 386.66, 19.56, 0.00257, 7, 117.51, 54.04, 0.79369, 8, -77.85, 65.07, 0.20374, 2, 7, 180.52, 57.15, 0.34644, 8, -19.36, 41.44, 0.65356, 2, 7, 202.41, 56.66, 0.11385, 8, 0.31, 31.82, 0.88615, 3, 6, 435.91, 12.31, 6e-05, 7, 166.85, 60.64, 0.46201, 8, -30.29, 50.35, 0.53793, 2, 7, 190.91, 55.44, 0.23558, 8, -10.63, 35.53, 0.76442, 2, 7, 210.74, 55.52, 0.04937, 8, 7.39, 27.29, 0.95063, 2, 7, 241.78, 77.78, 0.00349, 8, 44.91, 34.46, 0.99651, 1, 8, 69.49, 35.19, 1, 2, 7, 322.32, 77.19, 0.04031, 8, 117.76, 0.13, 0.95969, 2, 7, 316.55, 47.42, 0.28639, 8, 100.03, -24.48, 0.71361, 2, 7, 285.56, 35.25, 0.36489, 8, 66.8, -22.52, 0.63511, 2, 7, 269.58, 31.32, 0.35917, 8, 50.65, -19.38, 0.64083, 2, 7, 262.12, 26.75, 0.42052, 8, 41.96, -20.4, 0.57948, 2, 7, 278.83, 26.89, 0.50854, 8, 57.18, -27.29, 0.49146, 2, 7, 286.73, 26.91, 0.54446, 8, 64.36, -30.58, 0.45554, 2, 7, 325.73, 24.6, 0.67403, 8, 98.79, -49.04, 0.32597, 2, 7, 301.77, -33.4, 0.64178, 9, 88.64, 46.57, 0.35822, 2, 7, 279.15, -29.02, 0.60353, 9, 67.97, 36.4, 0.39647, 2, 7, 258.6, -21.54, 0.64138, 9, 47.07, 29.95, 0.35862, 2, 7, 265.54, -29.43, 0.51978, 9, 57.37, 27.86, 0.48022, 2, 7, 273.45, -35.94, 0.44464, 9, 67.6, 27.44, 0.55536, 2, 7, 282.26, -42.37, 0.39016, 9, 78.51, 27.63, 0.60984, 3, 6, 470.11, -163.72, 0.00049, 7, 248.24, -99.14, 0.0001, 9, 85.68, -38.16, 0.99942, 3, 6, 441.9, -121.1, 0.00166, 7, 209.38, -65.95, 0.00736, 9, 34.66, -35.17, 0.99098, 3, 6, 435.12, -91.11, 0.00068, 7, 194.59, -38.98, 0.00498, 9, 6.59, -22.62, 0.99434, 3, 6, 434.14, -67.53, 5e-05, 7, 187.16, -16.59, 0.44981, 9, -12.86, -9.26, 0.55014, 3, 6, 400.58, -65.89, 0.0016, 7, 154.44, -24.26, 0.73373, 9, -34.3, -35.13, 0.26468, 3, 6, 361.74, -58.33, 0.00918, 7, 115.02, -27.7, 0.88118, 9, -63.64, -61.68, 0.10965, 3, 6, 329.7, -47.01, 0.02401, 7, 81.11, -25.64, 0.92825, 9, -91.91, -80.53, 0.04774, 3, 6, 298.31, -32.2, 0.06238, 7, 46.85, -20.07, 0.92062, 9, -122.58, -96.78, 0.017, 3, 6, 276.05, -26.28, 0.18542, 7, 23.82, -20.51, 0.80316, 9, -140.67, -111.04, 0.01142, 3, 6, 260.9, -22.76, 0.41579, 7, 8.28, -21.3, 0.57509, 9, -152.58, -121.05, 0.00912, 3, 6, 239.41, -22.37, 0.79813, 7, -12.48, -26.85, 0.19353, 9, -165.77, -138.01, 0.00834, 3, 6, 252.92, -22.41, 0.5736, 7, 0.51, -23.17, 0.41742, 9, -157.64, -127.23, 0.00898, 3, 6, 268.96, -24.27, 0.26461, 7, 16.45, -20.53, 0.72535, 9, -146.53, -115.51, 0.01004, 3, 6, 285.43, -29.19, 0.11983, 7, 33.64, -20.72, 0.86632, 9, -132.72, -105.27, 0.01386, 3, 6, 309.05, -37.78, 0.04236, 7, 58.71, -22.47, 0.9314, 9, -111.68, -91.53, 0.02623, 3, 6, 202.92, -21.77, 0.96577, 7, -47.72, -36.33, 0.02847, 9, -188.14, -166.85, 0.00575, 1, 6, 48.62, -19.75, 1], "hull": 13, "edges": [20, 22, 22, 24, 0, 24, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20], "width": 357, "height": 535}}, "R blush": {"R blush": {"name": "Skin_1/R blush", "type": "mesh", "uvs": [1, 0.98758, 0, 0.98758, 0, 0.01242, 1, 0.01242], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 22, 154.1, -79.46, 1, 1, 22, -168.9, -79.46, 1, 1, 22, -168.9, 77.54, 1, 1, 22, 154.1, 77.54, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 323, "height": 161}}, "R cheek": {"R cheek": {"name": "Skin_1/R cheek", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-173.98, 163.01, 193.01, 159.87, 190.73, -107.12, -176.26, -103.98], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 367, "height": 267}}, "R EATING CHEEK_": {"R EATING CHEEK_": {"name": "Skin_1/R EATING CHEEK_", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-215.76, -290.25, -222.81, 291.71, 221.16, 297.09, 228.21, -284.87], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 582, "height": 444}}, "R eye": {"R eye": {"name": "Skin_1/R eye", "type": "mesh", "uvs": [0.77763, 0.00507, 1, 0.33558, 1, 0.62616, 0.81984, 0.99641, 0.26275, 0.99548, 0.00481, 0.71474, 0.00383, 0.27597, 0.26491, 0.0042], "triangles": [1, 7, 0, 5, 6, 7, 4, 5, 7, 1, 4, 7, 2, 4, 1, 3, 4, 2], "vertices": [1, 20, -104.73, 56.72, 1, 1, 20, -38.74, 104.71, 1, 1, 20, 19.95, 105.66, 1, 1, 20, 95.35, 68.86, 1, 1, 20, 97.05, -48.68, 1, 1, 20, 41.23, -104.01, 1, 1, 20, -47.39, -105.65, 1, 1, 20, -103.17, -51.45, 1], "hull": 8, "edges": [0, 14, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14], "width": 211, "height": 202}}, "R eyelid": {"R eyelid": {"name": "Skin_1/R eyelid", "type": "mesh", "uvs": [0.99466, 0.25118, 1, 0.5025, 1, 0.68405, 0.86526, 0.99535, 0.65836, 0.99536, 0.51282, 0.99536, 0.3728, 0.99537, 0.15083, 0.99538, 0.00096, 0.73271, 0.00192, 0.27918, 0.14483, 0.00468, 0.83927, 0.00445], "triangles": [1, 11, 0, 11, 1, 4, 2, 3, 1, 11, 5, 10, 3, 4, 1, 4, 5, 11, 7, 8, 9, 7, 9, 10, 5, 6, 10, 6, 7, 10], "vertices": [394.16, 52.07, 396.38, -0.96, 396.31, -39.26, 337.57, -104.83, 247.56, -104.66, 184.25, -104.54, 123.35, -104.42, 26.79, -104.23, -38.3, -48.68, -37.69, 47.01, 24.59, 104.81, 326.67, 104.27], "hull": 12, "edges": [0, 22, 0, 2, 2, 4, 4, 6, 14, 16, 16, 18, 18, 20, 20, 22, 12, 14, 6, 8, 8, 10, 10, 12], "width": 435, "height": 211}}, "R LEG": {"R LEG": {"name": "Skin_1/R LEG", "type": "mesh", "uvs": [0, 0.06406, 0.04227, 0.11072, 0.10767, 0.17813, 0.14747, 0.23412, 0.18728, 0.29634, 0.21087, 0.34701, 0.24191, 0.42342, 0.24694, 0.44482, 0.25355, 0.47295, 0.25355, 0.49842, 0.25875, 0.5326, 0.26324, 0.56207, 0.26679, 0.58539, 0.2712, 0.61436, 0.28979, 0.73654, 0.29527, 0.86865, 0.27881, 0.9327, 0.26783, 0.98875, 0.36114, 0.99676, 1, 1, 1, 0.9367, 0.89352, 0.91068, 0.73435, 0.91268, 0.64105, 0.90668, 0.64654, 0.82861, 0.65751, 0.72853, 0.65751, 0.67239, 0.65751, 0.64046, 0.65317, 0.62144, 0.64927, 0.6044, 0.64555, 0.58808, 0.64206, 0.57283, 0.63556, 0.54438, 0.63082, 0.52882, 0.62633, 0.51408, 0.62006, 0.4935, 0.61567, 0.47908, 0.6107, 0.46278, 0.60263, 0.43628, 0.5907, 0.41354, 0.58143, 0.39585, 0.53128, 0.30017, 0.46542, 0.19208, 0.31723, 0.03795, 0.21844, 0, 0, 0], "triangles": [21, 20, 19, 22, 21, 19, 19, 18, 22, 18, 23, 22, 13, 12, 30, 13, 30, 29, 27, 13, 28, 26, 14, 27, 13, 27, 14, 28, 13, 29, 25, 14, 26, 24, 14, 25, 15, 14, 24, 23, 15, 24, 16, 15, 23, 18, 16, 23, 17, 16, 18, 0, 45, 44, 43, 1, 0, 43, 0, 44, 2, 1, 43, 2, 43, 42, 3, 2, 42, 4, 3, 42, 4, 42, 41, 5, 4, 41, 5, 41, 40, 6, 5, 40, 6, 40, 39, 7, 6, 39, 7, 39, 38, 8, 7, 38, 8, 38, 37, 36, 9, 8, 36, 8, 37, 9, 36, 35, 34, 10, 9, 34, 9, 35, 10, 34, 33, 11, 10, 33, 11, 33, 32, 11, 32, 31, 12, 11, 31, 12, 31, 30], "vertices": [1, 16, 26.6, -7.53, 1, 1, 16, 55.77, -5.52, 1, 1, 16, 98.14, -1.71, 1, 1, 16, 132.56, -1.6, 1, 1, 16, 170.57, -2.4, 1, 1, 16, 201.04, -4.91, 1, 1, 16, 246.75, -9.63, 1, 1, 16, 259.21, -11.58, 1, 1, 16, 275, -14.02, 1, 1, 16, 288.25, -18.03, 1, 2, 16, 307.64, -23.23, 0.99207, 17, -31.94, -37.13, 0.00793, 2, 16, 324.31, -27.93, 0.88185, 17, -14.69, -35.55, 0.11815, 2, 16, 337.46, -31.79, 0.62998, 17, -1.03, -34.44, 0.37002, 2, 16, 353.76, -36.74, 0.23377, 17, 15.96, -33.23, 0.76623, 1, 17, 87.88, -30.23, 1, 1, 17, 166.47, -28.14, 1, 1, 17, 204.62, -31.28, 1, 2, 17, 237.99, -33.28, 0.98262, 18, -33.71, -7.73, 0.01738, 2, 17, 242.53, -12.98, 0.75098, 18, -13.67, -13.29, 0.24902, 1, 18, 124.78, -20.67, 1, 1, 18, 126.26, 16.96, 1, 2, 17, 190, 101.96, 0.00016, 18, 103.78, 33.34, 0.99984, 2, 17, 191.58, 67.43, 0.07976, 18, 69.22, 33.51, 0.92024, 2, 17, 188.24, 47.15, 0.43106, 18, 49.13, 37.88, 0.56894, 2, 17, 141.78, 47.81, 0.96984, 18, 52.15, 84.24, 0.03016, 1, 17, 82.21, 49.51, 1, 2, 16, 413.92, 28.39, 0.00539, 17, 48.81, 49.13, 0.99461, 2, 16, 396.11, 35, 0.05789, 17, 29.81, 48.91, 0.94211, 2, 16, 385.17, 38.04, 0.14617, 17, 18.51, 47.84, 0.85383, 2, 16, 375.37, 40.77, 0.27824, 17, 8.37, 46.88, 0.72176, 2, 16, 365.98, 43.39, 0.44331, 17, -1.32, 45.96, 0.55669, 2, 16, 357.21, 45.83, 0.6084, 17, -10.39, 45.1, 0.3916, 2, 16, 340.82, 50.43, 0.85317, 17, -27.34, 43.53, 0.14683, 2, 16, 331.62, 52.86, 0.93192, 17, -36.8, 42.5, 0.06808, 2, 16, 322.88, 55.08, 0.97307, 17, -45.75, 41.45, 0.02693, 2, 16, 310.22, 58.25, 0.99701, 17, -58.71, 39.87, 0.00299, 2, 16, 301.07, 60.25, 0.99992, 17, -67.97, 38.46, 8e-05, 1, 16, 290.65, 62.12, 1, 1, 16, 274.02, 64.46, 1, 1, 16, 260.03, 65.28, 1, 1, 16, 249.13, 65.8, 1, 1, 16, 191.27, 69.35, 1, 1, 16, 125.42, 71.44, 1, 1, 16, 28.61, 63.02, 1, 1, 16, 1.42, 47.84, 1, 1, 16, -10.33, 1.92, 1], "hull": 46, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 16, 18, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 82, 84, 84, 86, 86, 88, 88, 90, 0, 90, 74, 76, 12, 14, 14, 16, 80, 82, 62, 64, 68, 70, 18, 20, 20, 22, 22, 24, 50, 52, 52, 54, 76, 78, 78, 80, 70, 72, 72, 74, 64, 66, 66, 68, 58, 60, 60, 62, 54, 56, 56, 58, 24, 26, 26, 28], "width": 217, "height": 595}}, "R SHIRT ARM": {"R SHIRT ARM": {"name": "Skin_1/R SHIRT ARM", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 6, 401.71, 17.05, 1, 1, 6, 224.81, -215.27, 1, 1, 6, -44.9, -9.9, 1, 1, 6, 131.99, 222.42, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 292, "height": 339}}, "R UPPER LEG": {"R UPPER LEG": {"name": "Skin_1/R UPPER LEG", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 16, 320.23, 114.95, 1, 1, 16, 219.71, -238.01, 1, 1, 16, -209.23, -115.85, 1, 1, 16, -108.71, 237.11, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 367, "height": 446}}, "Shadow": {"Shadow": {"name": "Skin_1/Shadow", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [629.08, -101.66, -574.92, -101.66, -574.92, 100.34, 629.08, 100.34], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 1204, "height": 202}}, "SHIRT": {"SHIRT": {"name": "Skin_1/SHIRT", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [275.8, -628.5, 285.5, 652.46, 1242.48, 645.21, 1232.77, -635.75], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 1281, "height": 957}}, "Tie": {"Tie": {"name": "Skin_1/Tie", "type": "mesh", "uvs": [0.77762, 0.5613, 1, 0.95224, 1, 1, 0.97653, 1, 0.65703, 0.89951, 0.22263, 0.66047, 0, 0.35728, 0, 0.06234, 0.00086, 0, 0.07371, 0, 0.32012, 0.1556], "triangles": [10, 6, 7, 7, 8, 9, 9, 10, 7, 6, 10, 0, 5, 6, 0, 4, 5, 0, 4, 0, 1, 3, 4, 1, 3, 1, 2], "vertices": [1, 10, 403.99, 54.38, 1, 1, 10, 650.49, 11.68, 1, 1, 10, 676.92, -1.14, 1, 1, 10, 673.74, -7.71, 1, 1, 10, 574.76, -70.14, 1, 1, 10, 383.53, -127.53, 1, 1, 10, 185.55, -108.45, 1, 1, 10, 22.35, -29.28, 1, 1, 10, -12.03, -12.31, 1, 1, 10, -2.14, 8.08, 1, 1, 10, 117.4, 35.26, 1], "hull": 11, "edges": [0, 20, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20], "width": 311, "height": 615}}, "Tie 2": {"Tie 2": {"name": "Skin_1/<PERSON><PERSON> 2", "type": "mesh", "uvs": [0.99843, 0, 1, 0.1214, 1, 0.3973, 0.63627, 0.75608, 0.08365, 1, 0.0017, 1, 0, 0.99949, 0, 0.97667, 0.09634, 0.75465, 0.28249, 0.49124, 0.57841, 0.20745, 0.90541, 0], "triangles": [11, 0, 1, 1, 10, 11, 2, 10, 1, 2, 9, 10, 3, 9, 2, 8, 9, 3, 5, 6, 7, 7, 4, 5, 8, 4, 7, 4, 8, 3], "vertices": [1, 11, -11.86, 11.83, 1, 1, 11, 31.9, 38.7, 1, 1, 11, 131.86, 98.95, 1, 1, 11, 312.35, 93.49, 1, 1, 11, 477.45, 19.44, 1, 1, 11, 488.83, 0.56, 1, 1, 11, 488.88, 0.05, 1, 1, 11, 480.62, -4.93, 1, 1, 11, 386.8, -31.22, 1, 1, 11, 265.53, -45.85, 1, 1, 11, 121.62, -39.64, 1, 1, 11, 1.06, -9.6, 1], "hull": 12, "edges": [0, 22, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22], "width": 269, "height": 423}}}}], "animations": {"DECISION MAKING": {"slots": {"EYE 3": {"attachment": [{"name": null}]}, "L EATING CHEEK_": {"attachment": [{"name": null}]}, "MOUTH2": {"attachment": [{"name": "MOUTH2"}]}, "R EATING CHEEK_": {"attachment": [{"name": null}]}}, "bones": {"BODY": {"rotate": [{}, {"time": 0.3, "value": 3.02, "curve": "stepped"}, {"time": 1, "value": 3.02, "curve": "stepped"}, {"time": 1.2, "value": 3.02}, {"time": 1.3667, "value": -7.67}, {"time": 1.8, "value": -2.71}, {"time": 2.5667}], "translate": [{}, {"time": 0.3, "x": 6, "y": -60}, {"time": 1, "x": 11.63, "y": -121.91}, {"time": 1.2, "x": 0.37, "y": 7.54}, {"time": 1.3667, "x": 1.3, "y": -130.47, "curve": "stepped"}, {"time": 2.1333, "x": 1.3, "y": -130.47}, {"time": 2.5667}]}, "R ARM": {"rotate": [{"curve": [0.075, 0, 0.225, -142.61]}, {"time": 0.3, "value": -142.61, "curve": [0.417, -142.61, 0.65, -139.46]}, {"time": 0.7667, "value": -139.46}, {"time": 1, "value": -10.39}, {"time": 1.1667, "value": 39.54}, {"time": 1.2667, "value": 97.31}, {"time": 1.3667, "value": 0.31}, {"time": 1.5, "value": -94.29}, {"time": 1.6, "value": -114.38, "curve": [1.733, -114.38, 2, 0]}, {"time": 2.1333}], "translate": [{"curve": "stepped"}, {"time": 2.1333}]}, "R ARM 1": {"rotate": [{"curve": [0.026, 0, 0.062, 5.63]}, {"time": 0.1, "value": 13.14, "curve": [0.17, -6.14, 0.251, -35.07]}, {"time": 0.3, "value": -35.07, "curve": [0.339, -35.07, 0.384, -38.78]}, {"time": 0.4333, "value": -45.13, "curve": [0.534, -43.37, 0.685, -37.22]}, {"time": 0.7667, "value": -37.22}, {"time": 1, "value": -11.9}, {"time": 1.1667, "value": 17.42}, {"time": 1.2667, "value": -39.09}, {"time": 1.3333, "value": 46.33}, {"time": 1.5, "value": -39.09}, {"time": 2.0333}], "translate": [{"curve": "stepped"}, {"time": 1.5, "curve": "stepped"}, {"time": 2.0333}]}, "L ARM": {"rotate": [{}, {"time": 0.3, "value": 33.79, "curve": "stepped"}, {"time": 2.1333, "value": 33.79}, {"time": 2.5667}], "translate": [{"curve": "stepped"}, {"time": 2.5667}]}, "L ARM1": {"rotate": [{}, {"time": 0.3, "value": 34.35, "curve": "stepped"}, {"time": 2.3333, "value": 34.35}, {"time": 2.5667}], "translate": [{"curve": "stepped"}, {"time": 2.5667}]}, "R ARM 2": {"rotate": [{"time": 0.3}], "translate": [{"curve": "stepped"}, {"time": 0.3}]}, "R ARM 3": {"translate": [{}]}, "L EYE": {"scale": [{"time": 0.4333}, {"time": 0.5333, "y": 0}, {"time": 0.6333}]}, "L EYELID": {"scale": [{"time": 0.4333}, {"time": 0.5333, "y": 0}, {"time": 0.6333}]}, "R EYELID": {"scale": [{"time": 0.4333}, {"time": 0.5333, "y": 0}, {"time": 0.6333}]}, "TIE 1": {"rotate": [{}, {"time": 0.6, "value": -11.91}, {"time": 1.3667, "value": 3.64}, {"time": 1.7333, "value": -4.52}, {"time": 2.0667, "value": 3.44}, {"time": 2.5667}], "translate": [{"curve": "stepped"}, {"time": 2.5667}]}, "TIE 2": {"rotate": [{}, {"time": 0.6, "value": 6.7}, {"time": 1.3667, "value": 13.13}, {"time": 1.7333, "value": 5.1}, {"time": 2.0667, "value": 11.72}, {"time": 2.5667}], "translate": [{"curve": "stepped"}, {"time": 2.5667}]}}}, "EATING": {"slots": {"EYE 3": {"attachment": [{"name": null}, {"time": 3.5333, "name": null}, {"time": 3.6667, "name": "EYE 3"}]}, "L eye": {"attachment": [{"name": "L eye"}, {"time": 3.5667, "name": "L eye"}, {"time": 3.6667, "name": null}]}, "L eyelid": {"attachment": [{"time": 3.5667, "name": "L eyelid"}, {"time": 3.6667, "name": null}]}, "R eye": {"attachment": [{"name": "R eye"}, {"time": 3.5667, "name": "R eye"}, {"time": 3.6667, "name": null}]}, "R eyelid": {"attachment": [{"time": 3.5667, "name": "R eyelid"}, {"time": 3.6667, "name": null}]}}, "bones": {"BODY": {"rotate": [{"curve": "stepped"}, {"time": 0.3333}, {"time": 0.6667, "value": 2.36}, {"time": 1, "value": -0.51}, {"time": 1.3333, "value": 2.36}, {"time": 1.6667, "value": -0.51}, {"time": 2, "value": 2.36}, {"time": 2.3333, "value": -0.51}, {"time": 2.6667, "value": 2.36}, {"time": 3.0667, "value": -0.51, "curve": "stepped"}, {"time": 3.2333, "value": -0.51, "curve": "stepped"}, {"time": 3.5667, "value": -0.51}, {"time": 4}, {"time": 4.3333, "value": 2.71}, {"time": 4.6, "value": -1.35}, {"time": 4.9667, "value": 2.71}, {"time": 5.2333, "value": -1.35}], "translate": [{}, {"time": 0.3333, "x": 6, "y": -60, "curve": "stepped"}, {"time": 0.6667, "x": 6, "y": -60, "curve": "stepped"}, {"time": 1, "x": 6, "y": -60, "curve": "stepped"}, {"time": 1.3333, "x": 6, "y": -60, "curve": "stepped"}, {"time": 1.6667, "x": 6, "y": -60, "curve": "stepped"}, {"time": 2, "x": 6, "y": -60, "curve": "stepped"}, {"time": 2.3333, "x": 6, "y": -60, "curve": "stepped"}, {"time": 2.6667, "x": 6, "y": -60, "curve": "stepped"}, {"time": 3.0667, "x": 6, "y": -60}, {"time": 3.2333, "x": 6, "y": -171.06}, {"time": 3.5667, "x": 6, "y": 30}, {"time": 4}]}, "EYES_EATING": {"rotate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}], "translate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}]}, "root": {"rotate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}], "translate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}]}, "L ARM": {"rotate": [{}, {"time": 1, "value": 138.83, "curve": "stepped"}, {"time": 3.2333, "value": 138.83}, {"time": 3.5, "value": -29.13}, {"time": 3.6667, "value": -10.85}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}], "translate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}]}, "L ARM1": {"rotate": [{}, {"time": 0.3333, "value": 84.78, "curve": "stepped"}, {"time": 3.2333, "value": 84.78}, {"time": 3.5, "value": 24.52}, {"time": 3.6667, "value": -20.71}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}], "translate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}]}, "L ARM2": {"rotate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}], "translate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}]}, "L ARM3": {"rotate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}], "translate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}]}, "R ARM": {"rotate": [{}, {"time": 1, "value": -150.1, "curve": "stepped"}, {"time": 3.1667, "value": -150.1}, {"time": 3.4333, "value": -22.11}, {"time": 3.5667, "value": 30.9}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}], "translate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}]}, "R ARM 1": {"rotate": [{}, {"time": 1, "value": -89.92}, {"time": 3.4333, "value": -32.88}, {"time": 3.5667, "value": -18.09}, {"time": 3.6667, "value": 29.61}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}], "translate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}]}, "R ARM 3": {"rotate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}], "translate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}]}, "R ARM 2": {"rotate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}], "translate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}]}, "TIE 1": {"rotate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}], "translate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}]}, "TIE 2": {"rotate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}], "translate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}]}, "L LEG": {"rotate": [{"value": -5.69, "curve": "stepped"}, {"time": 4, "value": -5.69, "curve": "stepped"}, {"time": 4.5667, "value": -5.69}], "translate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}]}, "L LEG 2": {"rotate": [{"value": 7.23, "curve": "stepped"}, {"time": 4, "value": 7.23, "curve": "stepped"}, {"time": 4.5667, "value": 7.23}], "translate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}]}, "L LEG 3": {"rotate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}], "translate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}]}, "L LEG2": {"rotate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}], "translate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}]}, "R LEG": {"rotate": [{"value": -1.65, "curve": "stepped"}, {"time": 4, "value": -1.65, "curve": "stepped"}, {"time": 4.5667, "value": -1.65}], "translate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}]}, "R LEG 2": {"rotate": [{"value": 0.13, "curve": "stepped"}, {"time": 4, "value": 0.13, "curve": "stepped"}, {"time": 4.5667, "value": 0.13}], "translate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}]}, "R LEG 3": {"rotate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}], "translate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}]}, "r leg": {"rotate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}], "translate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}]}, "R EYE": {"rotate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}], "translate": [{"x": -43.95, "y": 71.54, "curve": "stepped"}, {"time": 4, "x": -43.95, "y": 71.54, "curve": "stepped"}, {"time": 4.5667, "x": -43.95, "y": 71.54}]}, "L EYE": {"rotate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}], "translate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}]}, "R BLUSH": {"rotate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}], "translate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}]}, "L BLUSH": {"rotate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}], "translate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}]}, "R EYELID": {"rotate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}], "translate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}]}, "L EYELID": {"rotate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}], "translate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}]}, "L CHEEK": {"rotate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}], "translate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}]}, "R CHEEK": {"rotate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}], "translate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}]}, "EATING": {"rotate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}], "translate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}]}, "MOUTH": {"rotate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}], "translate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}], "scale": [{}, {"time": 0.3333, "y": 1.2}, {"time": 0.6667}, {"time": 1, "y": 1.2}, {"time": 1.3333}, {"time": 1.6667, "y": 1.2}, {"time": 2}, {"time": 2.3333, "y": 1.2}, {"time": 2.6667}, {"time": 3.0667, "y": 1.2}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}]}, "L EATING CHEEK": {"rotate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}], "translate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}], "scale": [{}, {"time": 0.3333, "x": 1.1, "y": 0.85}, {"time": 0.6667}, {"time": 1, "x": 1.1, "y": 0.85}, {"time": 1.3333}, {"time": 1.6667, "x": 1.1, "y": 0.85}, {"time": 2}, {"time": 2.3333, "x": 1.1, "y": 0.85}, {"time": 2.6667}, {"time": 3.0667, "x": 1.1, "y": 0.85}, {"time": 3.2333}, {"time": 3.4333, "x": 1.1, "y": 0.85, "curve": [4, 1.1, 3.858, 0, 4, 0.85, 3.858, 1]}, {"time": 4, "x": 0, "curve": "stepped"}, {"time": 4.5667, "x": 0}]}, "R EATING CHEEK": {"rotate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}], "translate": [{"curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.5667}], "scale": [{}, {"time": 0.3333, "x": 1.05, "y": 0.85}, {"time": 0.6667}, {"time": 1, "x": 1.05, "y": 0.85}, {"time": 1.3333}, {"time": 1.6667, "x": 1.05, "y": 0.85}, {"time": 2}, {"time": 2.3333, "x": 1.05, "y": 0.85}, {"time": 2.6667}, {"time": 3.0667, "x": 1.05, "y": 0.85}, {"time": 3.2333}, {"time": 3.4333, "x": 1.05, "y": 0.85, "curve": [4, 1.05, 3.858, 0, 4, 0.85, 3.858, 1]}, {"time": 4, "x": 0, "curve": "stepped"}, {"time": 4.5667, "x": 0}]}}}, "EXCERCISE": {"slots": {"EYE 3": {"attachment": [{"name": null}]}, "L EATING CHEEK_": {"attachment": [{"name": null}]}, "MOUTH2": {"attachment": [{"name": "MOUTH2"}, {"time": 2.4333, "name": "MOUTH2"}, {"time": 4.9, "name": "MOUTH2"}, {"time": 5.2, "name": "MOUTH2"}]}, "R EATING CHEEK_": {"attachment": [{"name": null}]}, "Shadow": {"attachment": [{"name": "Shadow"}, {"time": 2.4333, "name": "Shadow"}]}}, "bones": {"BODY": {"rotate": [{"curve": "stepped"}, {"time": 0.2}, {"time": 0.4667, "value": -4.18}, {"time": 0.7333, "value": -12.93}, {"time": 1, "value": -4.18}, {"time": 1.3, "value": -12.93}, {"time": 1.5667, "value": -4.18}, {"time": 1.8333, "value": -12.93}, {"time": 2.1, "value": -4.18}, {"time": 2.4333, "curve": "stepped"}, {"time": 2.6667}, {"time": 2.9333, "value": 9.25}, {"time": 3.2, "value": 15.64}, {"time": 3.2667, "value": 13.91}, {"time": 3.4667, "value": 9.25}, {"time": 3.7333, "value": 15.64}, {"time": 4.0667, "value": 9.25}, {"time": 4.3333, "value": 15.64}, {"time": 4.9, "curve": "stepped"}, {"time": 5.2}], "translate": [{}, {"time": 0.2, "x": -18, "y": -192}, {"time": 0.4667, "x": 42, "y": -12}, {"time": 0.7333, "x": 36.74, "y": -69.82}, {"time": 1, "x": 42, "y": -12}, {"time": 1.3, "x": 36.74, "y": -69.82}, {"time": 1.5667, "x": 42, "y": -12}, {"time": 1.8333, "x": 36.74, "y": -69.82}, {"time": 2.1, "x": 42, "y": -12}, {"time": 2.4333}, {"time": 2.6667, "y": -186.05}, {"time": 2.9333, "x": 15.77, "y": -38.88}, {"time": 3.2, "x": 11.26, "y": -70.46}, {"time": 3.4667, "x": 15.77, "y": -38.88}, {"time": 3.7333, "x": 11.26, "y": -70.46}, {"time": 4.0667, "x": 15.77, "y": -38.88}, {"time": 4.3333, "x": 11.26, "y": -70.46}, {"time": 4.9, "curve": "stepped"}, {"time": 5.2}]}, "L ARM": {"rotate": [{}, {"time": 0.2, "value": 10.88}, {"time": 0.4667, "value": -118.17}, {"time": 0.8, "value": -134.81}, {"time": 1, "value": -118.17}, {"time": 1.4, "value": -134.81}, {"time": 1.5667, "value": -118.17}, {"time": 1.9667, "value": -134.81}, {"time": 2.1, "value": -118.17}, {"time": 2.4333, "curve": "stepped"}, {"time": 2.6667}, {"time": 2.9333, "value": 21.78, "curve": "stepped"}, {"time": 4.8667, "value": 21.78}, {"time": 5.2}], "translate": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.4667, "curve": "stepped"}, {"time": 2.4333, "curve": "stepped"}, {"time": 2.6667, "curve": "stepped"}, {"time": 5.2}]}, "L ARM1": {"rotate": [{}, {"time": 0.2, "value": 23.9}, {"time": 0.4667, "value": -32.19}, {"time": 0.8, "value": -38.03}, {"time": 1, "value": -32.19}, {"time": 1.4, "value": -38.03}, {"time": 1.5667, "value": -32.19}, {"time": 1.9667, "value": -38.03}, {"time": 2.1, "value": -32.19}, {"time": 2.4333, "curve": "stepped"}, {"time": 2.6667}, {"time": 2.9333, "value": 43.95, "curve": "stepped"}, {"time": 4.8667, "value": 43.95}, {"time": 5.2}], "translate": [{"curve": "stepped"}, {"time": 0.4667, "curve": "stepped"}, {"time": 2.4333, "curve": "stepped"}, {"time": 2.6667, "curve": "stepped"}, {"time": 5.2}]}, "L ARM2": {"rotate": [{"curve": "stepped"}, {"time": 2.4333}], "translate": [{"curve": "stepped"}, {"time": 2.4333}]}, "L ARM3": {"rotate": [{"curve": "stepped"}, {"time": 2.4333}], "translate": [{"curve": "stepped"}, {"time": 2.4333}]}, "R ARM": {"rotate": [{}, {"time": 0.2, "value": -7.54}, {"time": 0.4667, "value": 3.47, "curve": "stepped"}, {"time": 1, "value": 3.47, "curve": "stepped"}, {"time": 1.5667, "value": 3.47, "curve": "stepped"}, {"time": 2.1, "value": 3.47}, {"time": 2.4333}, {"time": 2.9333, "value": 102.48}, {"time": 3.2667, "value": 108.78}, {"time": 3.5333, "value": 102.48}, {"time": 3.8333, "value": 108.78}, {"time": 4, "value": 102.48}, {"time": 4.4, "value": 108.78}, {"time": 5.2}], "translate": [{"curve": "stepped"}, {"time": 2.4333, "curve": "stepped"}, {"time": 5.2}]}, "R ARM 1": {"rotate": [{}, {"time": 0.2, "value": -17.17}, {"time": 0.4667, "value": -47.16, "curve": "stepped"}, {"time": 1, "value": -47.16, "curve": "stepped"}, {"time": 1.5667, "value": -47.16, "curve": "stepped"}, {"time": 2.1, "value": -47.16}, {"time": 2.4333}, {"time": 2.9333, "value": 30.81}, {"time": 3.2667, "value": 47.97}, {"time": 3.5333, "value": 30.81}, {"time": 3.8333, "value": 47.97}, {"time": 4, "value": 30.81}, {"time": 4.4, "value": 47.97}, {"time": 5.2}], "translate": [{"curve": "stepped"}, {"time": 2.4333, "curve": "stepped"}, {"time": 5.2}]}, "R ARM 3": {"rotate": [{"curve": "stepped"}, {"time": 2.4333}], "translate": [{"curve": "stepped"}, {"time": 2.4333}]}, "R ARM 2": {"rotate": [{"curve": "stepped"}, {"time": 2.4333}], "translate": [{"curve": "stepped"}, {"time": 2.4333}]}, "TIE 1": {"rotate": [{}, {"time": 0.2667, "value": -11.31}, {"time": 0.7667, "value": -2.9}, {"time": 1.1, "value": -10.09}, {"time": 1.3667, "value": -2.9}, {"time": 1.6333, "value": -10.09}, {"time": 1.9, "value": -2.9}, {"time": 2.2, "value": -10.09}, {"time": 2.4333}, {"time": 2.7333, "value": -11.31}, {"time": 2.9667, "value": -12.05}, {"time": 3.3, "value": -18.28}, {"time": 3.6, "value": -12.05}, {"time": 3.9, "value": -18.28}, {"time": 4.2333, "value": -12.05}, {"time": 4.5, "value": -18.28}, {"time": 5.2}], "translate": [{"curve": "stepped"}, {"time": 2.4333, "curve": "stepped"}, {"time": 5.2}]}, "TIE 2": {"rotate": [{}, {"time": 0.2667, "value": 9.86}, {"time": 0.7667, "value": 18.59}, {"time": 1.1, "value": 6.62}, {"time": 1.3667, "value": 18.59}, {"time": 1.6333, "value": 6.62}, {"time": 1.9, "value": 18.59}, {"time": 2.2, "value": 6.62}, {"time": 2.4333}, {"time": 2.7333, "value": 9.86}, {"time": 2.9667, "value": 9.69}, {"time": 3.3, "value": 0.19}, {"time": 3.6, "value": 9.69}, {"time": 3.9, "value": 0.19}, {"time": 4.2333, "value": 9.69}, {"time": 4.5, "value": 0.19}, {"time": 5.2}], "translate": [{"curve": "stepped"}, {"time": 2.4333, "curve": "stepped"}, {"time": 5.2}]}, "L LEG": {"rotate": [{"value": -5.69, "curve": "stepped"}, {"time": 2.4333, "value": -5.69}], "translate": [{"curve": "stepped"}, {"time": 2.4333}]}, "R LEG": {"rotate": [{"value": -1.65, "curve": "stepped"}, {"time": 2.4333, "value": -1.65}], "translate": [{"curve": "stepped"}, {"time": 2.4333}]}, "R EYE": {"rotate": [{"curve": "stepped"}, {"time": 2.4333}], "translate": [{"x": -43.95, "y": 71.54, "curve": "stepped"}, {"time": 2.4333, "x": -43.95, "y": 71.54}]}, "L EYE": {"rotate": [{"curve": "stepped"}, {"time": 2.4333}], "translate": [{"curve": "stepped"}, {"time": 2.4333}]}, "R BLUSH": {"rotate": [{"curve": "stepped"}, {"time": 2.4333}], "translate": [{"curve": "stepped"}, {"time": 2.4333}]}, "L BLUSH": {"rotate": [{"curve": "stepped"}, {"time": 2.4333}], "translate": [{"curve": "stepped"}, {"time": 2.4333}]}, "R EYELID": {"rotate": [{"curve": "stepped"}, {"time": 2.4333}], "translate": [{"curve": "stepped"}, {"time": 2.4333}]}, "L EYELID": {"rotate": [{"curve": "stepped"}, {"time": 2.4333}], "translate": [{"curve": "stepped"}, {"time": 2.4333}]}, "L CHEEK": {"rotate": [{"curve": "stepped"}, {"time": 2.4333}], "translate": [{"curve": "stepped"}, {"time": 2.4333}]}, "R CHEEK": {"rotate": [{"curve": "stepped"}, {"time": 2.4333}], "translate": [{"curve": "stepped"}, {"time": 2.4333}]}, "EATING": {"rotate": [{"curve": "stepped"}, {"time": 2.4333}], "translate": [{"curve": "stepped"}, {"time": 2.4333}]}}}, "GAMING": {"slots": {"EYE 3": {"attachment": [{"name": null}]}, "L EATING CHEEK_": {"attachment": [{"name": null}]}, "MOUTH2": {"attachment": [{"name": "MOUTH2"}]}, "R EATING CHEEK_": {"attachment": [{"name": null}]}}, "bones": {"root": {"rotate": [{"curve": "stepped"}, {"time": 1.8667}], "translate": [{"curve": "stepped"}, {"time": 1.8667}]}, "BODY": {"rotate": [{}, {"time": 0.3333, "value": -5.88}, {"time": 1.5667, "value": -11.97}, {"time": 1.8667}], "translate": [{}, {"time": 0.3333, "x": 6, "y": -24}, {"time": 0.5752, "x": 6, "y": -126}, {"time": 0.7333, "x": 24, "y": -18}, {"time": 0.8667, "x": -18, "y": -180}, {"time": 1.0333, "x": 24, "y": -18}, {"time": 1.2, "x": -18, "y": -180}, {"time": 1.3667, "x": 24, "y": -18}, {"time": 1.5667, "y": -126}, {"time": 1.8667}]}, "L ARM": {"rotate": [{}, {"time": 0.3333, "value": 149.47, "curve": "stepped"}, {"time": 1.5667, "value": 149.47}, {"time": 1.8667}], "translate": [{"curve": "stepped"}, {"time": 1.8667}]}, "L ARM1": {"rotate": [{}, {"time": 0.3333, "value": 34.5}, {"time": 1.5667, "value": 46.57}, {"time": 1.8667}], "translate": [{"curve": "stepped"}, {"time": 1.5667, "curve": "stepped"}, {"time": 1.8667}]}, "L ARM2": {"rotate": [{"curve": "stepped"}, {"time": 1.8667}], "translate": [{"curve": "stepped"}, {"time": 1.8667}]}, "L ARM3": {"rotate": [{"curve": "stepped"}, {"time": 1.8667}], "translate": [{"curve": "stepped"}, {"time": 1.8667}]}, "R ARM": {"rotate": [{}, {"time": 0.3333, "value": 68.32}, {"time": 0.6, "value": 6.04}, {"time": 0.8, "value": 68.32}, {"time": 0.9667, "value": 6.04}, {"time": 1.5667, "value": 15.25}, {"time": 1.8667}], "translate": [{"curve": "stepped"}, {"time": 0.6, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.8667}]}, "R ARM 1": {"rotate": [{}, {"time": 0.3333, "value": -54.34}, {"time": 0.6, "value": 10.41}, {"time": 0.8, "value": -54.34}, {"time": 0.9667, "value": 10.41}, {"time": 1.5667, "value": 5.71}, {"time": 1.8667}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6, "curve": "stepped"}, {"time": 0.8, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.8667}]}, "R ARM 3": {"rotate": [{}, {"time": 0.3333, "value": 9.81}, {"time": 1.8667}], "translate": [{"curve": "stepped"}, {"time": 1.8667}]}, "R ARM 2": {"rotate": [{}, {"time": 0.3333, "value": 45.24, "curve": "stepped"}, {"time": 1.5667, "value": 45.24}, {"time": 1.8667}], "translate": [{"curve": "stepped"}, {"time": 1.8667}]}, "TIE 1": {"rotate": [{"curve": "stepped"}, {"time": 1.8667}], "translate": [{"curve": "stepped"}, {"time": 1.8667}]}, "TIE 2": {"rotate": [{"curve": "stepped"}, {"time": 1.8667}], "translate": [{"curve": "stepped"}, {"time": 1.8667}]}, "L LEG": {"rotate": [{"value": -5.69, "curve": "stepped"}, {"time": 1.8667, "value": -5.69}], "translate": [{"curve": "stepped"}, {"time": 1.8667}]}, "L LEG 2": {"rotate": [{"value": 7.23, "curve": "stepped"}, {"time": 1.8667, "value": 7.23}], "translate": [{"curve": "stepped"}, {"time": 1.8667}]}, "L LEG 3": {"rotate": [{"curve": "stepped"}, {"time": 1.8667}], "translate": [{"curve": "stepped"}, {"time": 1.8667}]}, "L LEG2": {"rotate": [{"curve": "stepped"}, {"time": 1.8667}], "translate": [{"curve": "stepped"}, {"time": 1.8667}]}, "R LEG": {"rotate": [{"value": -1.65, "curve": "stepped"}, {"time": 1.8667, "value": -1.65}], "translate": [{"curve": "stepped"}, {"time": 1.8667}]}, "R LEG 2": {"rotate": [{"value": 0.13, "curve": "stepped"}, {"time": 1.8667, "value": 0.13}], "translate": [{"curve": "stepped"}, {"time": 1.8667}]}, "R LEG 3": {"rotate": [{"curve": "stepped"}, {"time": 1.8667}], "translate": [{"curve": "stepped"}, {"time": 1.8667}]}, "r leg": {"rotate": [{"curve": "stepped"}, {"time": 1.8667}], "translate": [{"curve": "stepped"}, {"time": 1.8667}]}, "R EYE": {"rotate": [{"curve": "stepped"}, {"time": 1.8667}], "translate": [{"x": -43.95, "y": 71.54, "curve": "stepped"}, {"time": 1.8667, "x": -43.95, "y": 71.54}]}, "L EYE": {"rotate": [{"curve": "stepped"}, {"time": 1.8667}], "translate": [{}, {"time": 0.3333, "x": -12.04, "y": -90.78, "curve": "stepped"}, {"time": 1.5667, "x": -12.04, "y": -90.78}, {"time": 1.8667}]}, "R BLUSH": {"rotate": [{"curve": "stepped"}, {"time": 1.8667}], "translate": [{}, {"time": 0.3333, "x": 142.61, "y": 115.15, "curve": "stepped"}, {"time": 1.5667, "x": 142.61, "y": 115.15}, {"time": 1.8667}]}, "L BLUSH": {"rotate": [{"curve": "stepped"}, {"time": 1.8667}], "translate": [{}, {"time": 0.3333, "x": 158.64, "y": -131.04, "curve": "stepped"}, {"time": 1.5667, "x": 158.64, "y": -131.04}, {"time": 1.8667}]}, "R EYELID": {"rotate": [{"curve": "stepped"}, {"time": 1.8667}], "translate": [{"curve": "stepped"}, {"time": 1.8667}]}, "L EYELID": {"rotate": [{"curve": "stepped"}, {"time": 1.8667}], "translate": [{"curve": "stepped"}, {"time": 1.8667}]}, "L CHEEK": {"rotate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 1.5667, "curve": "stepped"}, {"time": 1.8667}], "translate": [{}, {"time": 0.3333, "x": 178.62, "y": -20.47, "curve": "stepped"}, {"time": 1.5667, "x": 178.62, "y": -20.47}, {"time": 1.8667}]}, "R CHEEK": {"rotate": [{"curve": "stepped"}, {"time": 1.8667}], "translate": [{}, {"time": 0.3333, "x": 183.53, "y": -3.37, "curve": "stepped"}, {"time": 1.5667, "x": 183.53, "y": -3.37}, {"time": 1.8667}]}}}, "HUNGRY": {"slots": {"EYE 3": {"attachment": [{"name": null}]}, "L blush": {"attachment": [{"name": null}]}, "L EATING CHEEK_": {"attachment": [{"name": null}]}, "MOUTH2": {"attachment": [{"name": "MOUTH4"}]}, "R blush": {"attachment": [{"name": null}]}, "R EATING CHEEK_": {"attachment": [{"name": null}]}}, "bones": {"R BLUSH": {"rotate": [{"value": -0.03}], "translate": [{}]}, "R CHEEK": {"rotate": [{"value": -0.03}], "translate": [{}]}, "L EYE": {"rotate": [{}], "translate": [{}, {"time": 0.5667, "x": -43.76, "y": 72.42, "curve": "stepped"}, {"time": 1.7, "x": -43.76, "y": 72.42}, {"time": 2.1667, "x": -0.23, "y": -1.98}, {"time": 2.3333, "x": -2.47, "y": 1.19}, {"time": 2.4, "x": -0.23, "y": -1.98}, {"time": 2.4333, "x": -2.47, "y": 1.19}, {"time": 2.5, "x": -0.23, "y": -1.98}, {"time": 2.5667, "x": -2.47, "y": 1.19}, {"time": 2.6333, "x": -0.23, "y": -1.98}, {"time": 2.6667, "x": -2.47, "y": 1.19}, {"time": 2.7333, "x": -0.23, "y": -1.98}, {"time": 2.8, "x": -2.47, "y": 1.19}, {"time": 2.8333, "x": -0.23, "y": -1.98}, {"time": 2.9, "x": -2.47, "y": 1.19}, {"time": 2.9667, "x": -0.23, "y": -1.98}, {"time": 3.0667, "x": -2.47, "y": 1.19}, {"time": 3.1, "x": -0.23, "y": -1.98}, {"time": 3.1667, "x": -2.47, "y": 1.19}, {"time": 3.2333, "x": -0.23, "y": -1.98}, {"time": 3.3, "x": -2.47, "y": 1.19}, {"time": 3.3333, "x": -0.23, "y": -1.98}, {"time": 3.4, "x": -2.47, "y": 1.19}, {"time": 3.4667, "x": -0.23, "y": -1.98}, {"time": 3.5333, "x": -2.47, "y": 1.19}, {"time": 3.5667, "x": -0.23, "y": -1.98}, {"time": 3.6333, "x": -2.47, "y": 1.19}, {"time": 3.7, "x": -0.23, "y": -1.98}, {"time": 3.7667, "x": -2.47, "y": 1.19}, {"time": 3.8333, "x": -0.23, "y": -1.98}, {"time": 3.9, "x": -2.47, "y": 1.19}, {"time": 3.9667, "x": -0.23, "y": -1.98}, {"time": 4, "x": -2.47, "y": 1.19}, {"time": 4.0667, "x": -0.23, "y": -1.98}, {"time": 4.1333, "x": -2.47, "y": 1.19}, {"time": 4.2, "x": -0.23, "y": -1.98}, {"time": 4.2333, "x": -2.47, "y": 1.19}, {"time": 4.3, "x": -0.23, "y": -1.98}, {"time": 4.3667, "x": -2.47, "y": 1.19}, {"time": 4.4333, "x": -0.23, "y": -1.98}, {"time": 4.5, "x": -2.47, "y": 1.19}, {"time": 4.5667, "x": -0.23, "y": -1.98}, {"time": 4.6333, "x": -2.47, "y": 1.19}, {"time": 4.6667, "x": -0.23, "y": -1.98}, {"time": 4.7333, "x": -2.47, "y": 1.19}, {"time": 4.8, "x": -0.23, "y": -1.98}, {"time": 4.8667, "x": -2.47, "y": 1.19}, {"time": 4.9, "x": -0.23, "y": -1.98}, {"time": 4.9667, "x": -2.47, "y": 1.19}, {"time": 5.0333, "x": -0.23, "y": -1.98}, {"time": 5.1, "x": -2.47, "y": 1.19}, {"time": 5.1333, "x": -0.23, "y": -1.98}], "scale": [{}, {"time": 0.3333, "x": 0.3, "y": 0.3, "curve": "stepped"}, {"time": 4.9333, "x": 0.3, "y": 0.3}, {"time": 5.1333}]}, "root": {"rotate": [{}], "translate": [{}]}, "BODY": {"rotate": [{}, {"time": 0.5667, "value": 5.05}, {"time": 1.7, "value": 8.71}, {"time": 2.3333, "value": 3.46, "curve": "stepped"}, {"time": 2.5333, "value": 3.46}, {"time": 4.5333}], "translate": [{}, {"time": 0.5667, "x": 4.54, "y": -90.24, "curve": "stepped"}, {"time": 1.7, "x": 4.54, "y": -90.24}, {"time": 1.9333, "x": 30.77, "y": -243.3}, {"time": 2.1333, "x": 1.19, "y": -410.95, "curve": "stepped"}, {"time": 2.5333, "x": 1.19, "y": -410.95, "curve": "stepped"}, {"time": 4.1667, "x": 1.19, "y": -410.95}, {"time": 4.5333}]}, "L ARM": {"rotate": [{}, {"time": 0.8667, "value": 116.41}, {"time": 1.5, "value": 87.68}, {"time": 1.7, "value": 133.71}, {"time": 1.9333, "value": 87.68}, {"time": 2.1667, "value": 133.71}, {"time": 2.9, "value": 87.68}, {"time": 3.1, "value": 133.71}, {"time": 3.3333, "value": 87.68}, {"time": 3.5667, "value": 133.71, "curve": "stepped"}, {"time": 4.4333, "value": 133.71}, {"time": 4.9}], "translate": [{"curve": "stepped"}, {"time": 4.9}]}, "L ARM1": {"rotate": [{}, {"time": 0.8667, "value": 29.67, "curve": "stepped"}, {"time": 1.1667, "value": 29.67}, {"time": 1.5, "value": 62.26}, {"time": 1.7, "value": -1.83}, {"time": 1.9333, "value": 62.26}, {"time": 2.1667, "value": -1.83}, {"time": 2.9, "value": 62.26}, {"time": 3.1, "value": -1.83}, {"time": 3.3333, "value": 62.26}, {"time": 3.5667, "value": -1.83, "curve": "stepped"}, {"time": 4.4333, "value": -1.83}, {"time": 4.9, "curve": "stepped"}, {"time": 5.3}], "translate": [{"curve": "stepped"}, {"time": 4.9, "curve": "stepped"}, {"time": 5.3}]}, "L ARM2": {"rotate": [{}, {"time": 0.8667, "value": 34.39, "curve": "stepped"}, {"time": 3.8333, "value": 34.39}, {"time": 5.3}], "translate": [{"curve": "stepped"}, {"time": 5.3}]}, "L ARM3": {"rotate": [{}, {"time": 0.8667, "value": -13.37, "curve": "stepped"}, {"time": 1.6333, "value": -13.37}, {"time": 5.3}], "translate": [{"curve": "stepped"}, {"time": 5.3}]}, "R ARM": {"rotate": [{}, {"time": 0.8667, "value": -65.74}, {"time": 1.1667, "value": -82.76}, {"time": 1.6333, "value": -37.54}, {"time": 1.9333, "value": -1.36}, {"time": 2.2333, "value": -55.71}, {"time": 2.5, "value": -70.82}, {"time": 2.8667, "value": -10.67}, {"time": 3.4333, "value": -65.74}, {"time": 3.7333, "value": -82.76}, {"time": 4.2, "value": -37.54}, {"time": 4.4667, "value": -1.36}, {"time": 4.8, "value": -55.71}, {"time": 5.0667, "value": -70.82}, {"time": 5.3333}], "translate": [{"curve": "stepped"}, {"time": 5.3333}]}, "R ARM 1": {"rotate": [{}, {"time": 0.8667, "value": -51.69}, {"time": 1.1667, "value": -24.48}, {"time": 1.6333, "value": -33.09}, {"time": 1.9333, "value": -90.3}, {"time": 2.2333, "value": -58.59}, {"time": 2.5, "value": -21.41}, {"time": 2.8667, "value": -62.99}, {"time": 3.4333, "value": -51.69}, {"time": 3.7333, "value": -24.48}, {"time": 4.2, "value": -33.09}, {"time": 4.4667, "value": -90.3}, {"time": 4.8, "value": -58.59}, {"time": 5.0667, "value": -21.41}, {"time": 5.3333}], "translate": [{"curve": "stepped"}, {"time": 5.3333}]}, "R ARM 3": {"rotate": [{}], "translate": [{}]}, "R ARM 2": {"rotate": [{}], "translate": [{}]}, "TIE 1": {"rotate": [{"curve": "stepped"}, {"time": 0.5}, {"time": 1.2667, "value": -17.66}, {"time": 1.9, "value": -5.92}, {"time": 3.1333, "value": -13.87}, {"time": 3.6333, "value": -6.19}, {"time": 5.3333}], "translate": [{"curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 5.3333}]}, "TIE 2": {"rotate": [{"curve": "stepped"}, {"time": 0.5}, {"time": 0.8667, "value": 8.72}, {"time": 1.9, "value": 15.53}, {"time": 2.9333, "value": 4.07}, {"time": 3.6333, "value": 8.68}, {"time": 5.3333}], "translate": [{"curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 5.3333}]}, "L LEG": {"rotate": [{"value": -5.69}], "translate": [{}]}, "L LEG 2": {"rotate": [{"value": 7.23}], "translate": [{}]}, "L LEG 3": {"rotate": [{}], "translate": [{}]}, "L LEG2": {"rotate": [{"curve": "stepped"}, {"time": 1.9333, "curve": "stepped"}, {"time": 2.5333}], "translate": [{"curve": "stepped"}, {"time": 1.9333}, {"time": 2.1333, "x": 443.79, "y": -281.07, "curve": "stepped"}, {"time": 2.5333, "x": 443.79, "y": -281.07, "curve": "stepped"}, {"time": 3.7667, "x": 443.79, "y": -281.07}, {"time": 4.1667}]}, "R LEG": {"rotate": [{"value": -1.65}], "translate": [{}]}, "R LEG 2": {"rotate": [{"value": 0.13}], "translate": [{}]}, "R LEG 3": {"rotate": [{}], "translate": [{}]}, "r leg": {"rotate": [{"curve": "stepped"}, {"time": 1.9333}], "translate": [{"curve": "stepped"}, {"time": 1.9333, "curve": "stepped"}, {"time": 2.1333}, {"time": 2.3333, "x": 231.76, "y": -216.96, "curve": "stepped"}, {"time": 3.4, "x": 231.76, "y": -216.96}, {"time": 3.7667}]}, "R EYE": {"rotate": [{}], "translate": [{"x": -43.95, "y": 71.54}]}, "L BLUSH": {"rotate": [{}], "translate": [{}]}, "R EYELID": {"rotate": [{}], "translate": [{}], "scale": [{}, {"time": 0.5667, "y": 0.8, "curve": "stepped"}, {"time": 4.9333, "y": 0.8}, {"time": 5.1333}]}, "L EYELID": {"rotate": [{}], "translate": [{}], "scale": [{}, {"time": 0.5667, "y": 0.8, "curve": "stepped"}, {"time": 4.9333, "y": 0.8}, {"time": 5.1333}]}, "L CHEEK": {"rotate": [{}], "translate": [{}]}}, "deform": {"Skin_1": {"R blush": {"R blush": [{}]}}}}, "IDDLE1 _ WAVING HI": {"slots": {"EYE 3": {"attachment": [{"name": null}]}, "L EATING CHEEK_": {"attachment": [{"name": null}]}, "MOUTH2": {"attachment": [{"name": "MOUTH2"}]}, "R EATING CHEEK_": {"attachment": [{"name": null}]}}, "bones": {"root": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "BODY": {"rotate": [{}, {"time": 0.3333, "value": -4.34}, {"time": 0.6, "value": -7.22}, {"time": 0.7667, "value": -1.34}, {"time": 1.0667, "value": 10.43, "curve": [1.167, 10.43, 1.367, 4.82]}, {"time": 1.4667, "value": 4.82, "curve": [1.583, 4.82, 1.817, -6.51]}, {"time": 1.9333, "value": -6.51, "curve": [2.042, -6.51, 2.258, -7.79]}, {"time": 2.3667, "value": -7.79, "curve": [2.475, -7.79, 2.692, 0]}, {"time": 2.8}], "translate": [{}, {"time": 0.3333, "x": 16.14, "y": -188.1}, {"time": 0.7667, "x": -17.7, "y": 10.28}, {"time": 1.0667, "x": -37.03, "y": -74.36, "curve": [1.392, -37.03, 2.042, -45.26, 1.392, -74.36, 2.042, -96.59]}, {"time": 2.3667, "x": -45.26, "y": -96.59, "curve": [2.475, -45.26, 2.692, 0, 2.475, -96.59, 2.692, 0]}, {"time": 2.8}], "scale": [{"curve": "stepped"}, {"time": 2.8}]}, "L ARM": {"rotate": [{"curve": "stepped"}, {"time": 0.3333}, {"time": 0.6, "value": -7.53, "curve": "stepped"}, {"time": 2.2333, "value": -7.53, "curve": [2.375, -7.53, 2.658, 0]}, {"time": 2.8}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 2.8}], "scale": [{"curve": "stepped"}, {"time": 2.8}]}, "L ARM1": {"rotate": [{}, {"time": 0.3333, "value": -7.05}, {"time": 0.6, "value": 55.92, "curve": "stepped"}, {"time": 2.2333, "value": 55.92, "curve": [2.375, 55.92, 2.658, 0]}, {"time": 2.8}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6, "curve": "stepped"}, {"time": 2.2333, "curve": "stepped"}, {"time": 2.8}], "scale": [{"curve": "stepped"}, {"time": 2.8}]}, "L ARM2": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "L ARM3": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "R ARM": {"rotate": [{}, {"time": 0.7, "value": -23.77}, {"time": 0.9, "value": 98.63, "curve": [0.908, 98.63, 0.925, 114.79]}, {"time": 0.9333, "value": 114.79, "curve": [0.95, 114.79, 0.983, 61.91]}, {"time": 1, "value": 61.91, "curve": [1.017, 61.91, 1.05, 13.99]}, {"time": 1.0667, "value": 13.99, "curve": [1.1, 13.99, 1.167, -0.14]}, {"time": 1.2, "value": -0.14, "curve": [1.225, -0.14, 1.275, 98.63]}, {"time": 1.3, "value": 98.63, "curve": [1.308, 98.63, 1.325, 114.79]}, {"time": 1.3333, "value": 114.79, "curve": [1.35, 114.79, 1.383, 61.91]}, {"time": 1.4, "value": 61.91, "curve": [1.417, 61.91, 1.45, 13.99]}, {"time": 1.4667, "value": 13.99, "curve": [1.5, 13.99, 1.567, 98.63]}, {"time": 1.6, "value": 98.63, "curve": [1.608, 98.63, 1.625, 114.79]}, {"time": 1.6333, "value": 114.79, "curve": [1.658, 114.79, 1.708, 61.91]}, {"time": 1.7333, "value": 61.91, "curve": [1.75, 61.91, 1.783, 13.99]}, {"time": 1.8, "value": 13.99, "curve": [1.833, 13.99, 1.9, -0.14]}, {"time": 1.9333, "value": -0.14}], "translate": [{}, {"time": 0.9, "x": -0.39, "y": -5.99, "curve": "stepped"}, {"time": 1.3, "x": -0.39, "y": -5.99, "curve": "stepped"}, {"time": 1.6, "x": -0.39, "y": -5.99, "curve": "stepped"}, {"time": 2.1667, "x": -0.39, "y": -5.99}], "scale": [{}]}, "R ARM 1": {"rotate": [{"curve": "stepped"}, {"time": 0.3667}, {"time": 0.5, "value": -15.03}, {"time": 0.8667, "value": -4.24, "curve": [0.9, -4.24, 0.967, 58.07]}, {"time": 1, "value": 58.07, "curve": [1.042, 58.07, 1.125, 37.69]}, {"time": 1.1667, "value": 37.69, "curve": [1.183, 37.69, 1.217, -2.74]}, {"time": 1.2333, "value": -2.74, "curve": [1.25, -2.74, 1.283, -4.24]}, {"time": 1.3, "value": -4.24, "curve": [1.325, -4.24, 1.375, 58.07]}, {"time": 1.4, "value": 58.07, "curve": [1.442, 58.07, 1.525, -15.45]}, {"time": 1.5667, "value": -15.45, "curve": [1.6, -15.45, 1.667, 58.07]}, {"time": 1.7, "value": 58.07, "curve": [1.75, 58.07, 1.85, 11.98]}, {"time": 1.9, "value": 11.98, "curve": [1.975, 11.98, 2.125, -4.24]}, {"time": 2.2, "value": -4.24}], "translate": [{"curve": "stepped"}, {"time": 0.3667}], "scale": [{}]}, "R ARM 3": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "R ARM 2": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "TIE 1": {"rotate": [{}, {"time": 0.4667, "value": -8.18}, {"time": 1.2, "value": -11.31, "curve": [1.325, -11.31, 1.575, -5.17]}, {"time": 1.7, "value": -5.17, "curve": [1.967, -5.17, 2.5, 0]}, {"time": 2.7667}], "translate": [{"curve": "stepped"}, {"time": 2.7667}], "scale": [{"curve": "stepped"}, {"time": 2.7667}]}, "TIE 2": {"rotate": [{}, {"time": 0.4667, "value": 6.97}, {"time": 1.2, "value": 4.99, "curve": [1.325, 4.99, 1.575, 8.81]}, {"time": 1.7, "value": 8.81, "curve": [1.967, 8.81, 2.5, 0]}, {"time": 2.7667}], "translate": [{"curve": "stepped"}, {"time": 2.7667}], "scale": [{"curve": "stepped"}, {"time": 2.7667}]}, "L LEG": {"rotate": [{"value": -5.69}], "translate": [{}], "scale": [{}]}, "L LEG 2": {"rotate": [{"value": 7.23}], "translate": [{}], "scale": [{}]}, "L LEG 3": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "L LEG2": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "R LEG": {"rotate": [{"value": -1.65}], "translate": [{}], "scale": [{}]}, "R LEG 2": {"rotate": [{"value": 0.13}], "translate": [{}], "scale": [{}]}, "R LEG 3": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "r leg": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "R EYE": {"rotate": [{}], "translate": [{"x": -49.01, "y": 71.58}], "scale": [{}]}, "L EYE": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "R BLUSH": {"rotate": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 2.8333, "curve": "stepped"}, {"time": 2.9333}], "translate": [{}, {"time": 0.0667, "x": 67.25, "y": 112.52, "curve": "stepped"}, {"time": 2.8333, "x": 67.25, "y": 112.52}, {"time": 2.9333}], "scale": [{"curve": "stepped"}, {"time": 2.9333}]}, "L BLUSH": {"rotate": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 2.8333, "curve": "stepped"}, {"time": 2.9333}], "translate": [{}, {"time": 0.0667, "x": 95.97, "y": -72.32, "curve": "stepped"}, {"time": 2.8333, "x": 95.97, "y": -72.32}, {"time": 2.9333}], "scale": [{"curve": "stepped"}, {"time": 2.9333}]}, "L CHEEK": {"rotate": [{"curve": "stepped"}, {"time": 2.9333}], "translate": [{}, {"time": 0.0667, "x": 120.96, "y": -16.35, "curve": "stepped"}, {"time": 2.8667, "x": 120.96, "y": -16.35}, {"time": 2.9333}]}, "R CHEEK": {"rotate": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 2.8667, "curve": "stepped"}, {"time": 2.9333}], "translate": [{}, {"time": 0.0667, "x": 125.78, "y": -1.51, "curve": "stepped"}, {"time": 2.8667, "x": 125.78, "y": -1.51}, {"time": 2.9333}]}}}, "SLEEPY": {"slots": {"EYE 3": {"attachment": [{"name": null}]}, "L EATING CHEEK_": {"attachment": [{"name": null}]}, "MOUTH2": {"attachment": [{"name": "MOUTH3"}]}, "R EATING CHEEK_": {"attachment": [{"name": null}]}}, "bones": {"R CHEEK": {"rotate": [{"curve": "stepped"}, {"time": 3.9333}], "translate": [{"x": 514.56, "y": 23.17}, {"time": 0.5333, "x": 378.77, "y": 14.24, "curve": "stepped"}, {"time": 3.5, "x": 378.77, "y": 14.24}, {"time": 3.9333, "x": 514.56, "y": 23.17}]}, "root": {"rotate": [{"curve": "stepped"}, {"time": 5.1}], "translate": [{"curve": "stepped"}, {"time": 5.1}]}, "BODY": {"rotate": [{}, {"time": 0.5333, "value": -10.71, "curve": [0.829, -10.71, 0.858, -2.42]}, {"time": 0.9667, "value": -2.42, "curve": [1.582, -2.42, 1.642, -12.13]}, {"time": 1.8667, "value": -12.13, "curve": [2.041, -12.12, 1.958, -7.34]}, {"time": 2.2333, "value": -7.34, "curve": [2.867, -7.35, 2.567, -13.38]}, {"time": 3.5667, "value": -13.38, "curve": [3.625, -13.38, 3.742, 0.98]}, {"time": 3.8, "value": 0.98, "curve": [3.842, 0.98, 3.925, 5.98]}, {"time": 3.9667, "value": 5.98, "curve": [4.092, 5.98, 4.342, 0]}, {"time": 4.4667}, {"time": 4.7333, "value": -1.28}, {"time": 5.1}], "translate": [{}, {"time": 0.5333, "x": 1.44, "y": -51.95, "curve": [1.444, 1.44, 1.533, -1.28, 1.444, -51.95, 1.533, -104.86]}, {"time": 1.8667, "x": -1.28, "y": -104.86, "curve": "stepped"}, {"time": 2.2333, "x": -1.28, "y": -104.86, "curve": [2.867, -1.25, 2.567, 16.72, 2.867, -104.97, 2.567, -164.86]}, {"time": 3.5667, "x": 16.72, "y": -164.86, "curve": [3.625, 16.72, 3.742, 0, 3.625, -164.86, 3.742, 0]}, {"time": 3.8, "curve": [3.8, 4.5, 3.933, 13.5, 3.8, -6, 3.933, -18]}, {"time": 3.9333, "x": 18, "y": -24, "curve": [4.067, 18, 4.333, 0, 4.067, -24, 4.333, 0]}, {"time": 4.4667, "curve": "stepped"}, {"time": 5.1}]}, "L ARM": {"rotate": [{}, {"time": 0.5333, "value": 170.29}, {"time": 0.9667, "value": 157.02}, {"time": 1.9667, "value": 146.22}, {"time": 2.2667, "value": 175.41}, {"time": 3.5667, "value": 170.29}, {"time": 3.7667, "value": 54.83}, {"time": 4.1, "value": -16.09}, {"time": 4.4667, "curve": "stepped"}, {"time": 5.1}], "translate": [{"curve": "stepped"}, {"time": 4.1, "curve": "stepped"}, {"time": 4.4667, "curve": "stepped"}, {"time": 5.1}]}, "L ARM1": {"rotate": [{}, {"time": 0.5333, "value": 40.07}, {"time": 1.3667, "value": 15, "curve": "stepped"}, {"time": 1.9333, "value": 15}, {"time": 3.1667, "value": 40.07}, {"time": 3.9667, "value": 6.3}, {"time": 4.4667, "curve": "stepped"}, {"time": 5.1}], "translate": [{"curve": "stepped"}, {"time": 1.9333, "curve": "stepped"}, {"time": 4.4667, "curve": "stepped"}, {"time": 5.1}]}, "L ARM2": {"rotate": [{"curve": "stepped"}, {"time": 5.1}], "translate": [{"curve": "stepped"}, {"time": 5.1}]}, "L ARM3": {"rotate": [{"curve": "stepped"}, {"time": 5.1}], "translate": [{"curve": "stepped"}, {"time": 5.1}]}, "R ARM": {"rotate": [{}, {"time": 0.5333, "value": -165.23}, {"time": 1.2667, "value": -185.17}, {"time": 3.5667, "value": -165.23, "curve": [3.64, -165.23, 3.842, -74.77]}, {"time": 3.9333, "value": -2.39, "curve": [3.993, -2.39, 4.137, 9.88]}, {"time": 4.2333, "value": 9.88, "curve": [4.347, 9.88, 4.658, 0]}, {"time": 4.8}], "translate": [{"curve": "stepped"}, {"time": 0.5333, "curve": "stepped"}, {"time": 3.5667, "curve": "stepped"}, {"time": 4.8}]}, "R ARM 1": {"rotate": [{}, {"time": 0.5333, "value": -22.94, "curve": "stepped"}, {"time": 3.5667, "value": -22.94, "curve": [3.64, -22.94, 3.842, -32.64]}, {"time": 3.9333, "value": -40.4, "curve": [3.993, -40.4, 4.137, -0.42]}, {"time": 4.2333, "value": -0.42, "curve": [4.347, -0.42, 4.658, 0]}, {"time": 4.8}], "translate": [{"curve": "stepped"}, {"time": 4.8}]}, "R ARM 3": {"rotate": [{"curve": "stepped"}, {"time": 5.1}], "translate": [{"curve": "stepped"}, {"time": 5.1}]}, "R ARM 2": {"rotate": [{"curve": "stepped"}, {"time": 5.1}], "translate": [{"curve": "stepped"}, {"time": 5.1}]}, "TIE 1": {"rotate": [{}, {"time": 0.6, "value": -6.53}, {"time": 1, "value": -3.76}, {"time": 1.6, "value": -6.53}, {"time": 2, "value": -3.76}, {"time": 2.5333, "value": -6.53}, {"time": 2.9333, "value": -3.76}, {"time": 3.4333, "value": -6.53}, {"time": 3.8333, "value": 1.63}, {"time": 5.1}], "translate": [{"curve": "stepped"}, {"time": 5.1}]}, "TIE 2": {"rotate": [{}, {"time": 0.6, "value": 8.61}, {"time": 1, "value": 4.1}, {"time": 5.1}], "translate": [{"curve": "stepped"}, {"time": 5.1}]}, "L LEG": {"rotate": [{"value": -5.69, "curve": "stepped"}, {"time": 5.1, "value": -5.69}], "translate": [{"curve": "stepped"}, {"time": 5.1}]}, "L LEG 2": {"rotate": [{"value": 7.23, "curve": "stepped"}, {"time": 5.1, "value": 7.23}], "translate": [{"curve": "stepped"}, {"time": 5.1}]}, "L LEG 3": {"rotate": [{"curve": "stepped"}, {"time": 5.1}], "translate": [{"curve": "stepped"}, {"time": 5.1}]}, "L LEG2": {"rotate": [{"curve": "stepped"}, {"time": 5.1}], "translate": [{"curve": "stepped"}, {"time": 5.1}]}, "R LEG": {"rotate": [{"value": -1.65, "curve": "stepped"}, {"time": 5.1, "value": -1.65}], "translate": [{"curve": "stepped"}, {"time": 5.1}]}, "R LEG 2": {"rotate": [{"value": 0.13, "curve": "stepped"}, {"time": 5.1, "value": 0.13}], "translate": [{"curve": "stepped"}, {"time": 5.1}]}, "R LEG 3": {"rotate": [{"curve": "stepped"}, {"time": 5.1}], "translate": [{"curve": "stepped"}, {"time": 5.1}]}, "r leg": {"rotate": [{"curve": "stepped"}, {"time": 5.1}], "translate": [{"curve": "stepped"}, {"time": 5.1}]}, "R EYE": {"rotate": [{"curve": "stepped"}, {"time": 5.1}], "translate": [{"x": -43.95, "y": 71.54, "curve": "stepped"}, {"time": 5.1, "x": -43.95, "y": 71.54}]}, "L EYE": {"rotate": [{"curve": "stepped"}, {"time": 5.1}], "translate": [{}, {"time": 0.5333, "x": 5.6, "y": -39.03}, {"time": 5.1}]}, "R BLUSH": {"rotate": [{"curve": "stepped"}, {"time": 5.1}], "translate": [{"curve": "stepped"}, {"time": 5.1}]}, "L BLUSH": {"rotate": [{"curve": "stepped"}, {"time": 5.1}], "translate": [{"curve": "stepped"}, {"time": 0.5333, "curve": "stepped"}, {"time": 5.1}], "scale": [{"time": 0.5333}]}, "R EYELID": {"rotate": [{"curve": "stepped"}, {"time": 5.1}], "translate": [{"curve": "stepped"}, {"time": 0.5333, "curve": "stepped"}, {"time": 5.1}]}, "L EYELID": {"rotate": [{"curve": "stepped"}, {"time": 5.1}], "translate": [{"curve": "stepped"}, {"time": 5.1}]}, "L CHEEK": {"rotate": [{"curve": "stepped"}, {"time": 3.9333}], "translate": [{"x": 514.15, "y": -30.97}, {"time": 0.5333, "x": 365.7, "y": -31.64, "curve": "stepped"}, {"time": 3.5, "x": 365.7, "y": -31.64}, {"time": 3.9333, "x": 514.15, "y": -30.97}]}}}, "WAKE UP": {"slots": {"EYE 3": {"attachment": [{"name": null}]}, "L EATING CHEEK_": {"attachment": [{"name": null}]}, "MOUTH2": {"attachment": [{"name": "MOUTH2"}, {"time": 0.3333, "name": "MOUTH6"}]}, "R EATING CHEEK_": {"attachment": [{"name": null}]}}, "bones": {"MOUTH": {"rotate": [{}], "translate": [{}], "scale": [{"time": 0.3333, "x": 0, "y": 0}, {"time": 1.1}]}, "BODY": {"rotate": [{}, {"time": 0.3333, "value": -0.19}, {"time": 0.7, "value": 2.85}, {"time": 0.8333, "value": -1.41}, {"time": 0.9667, "value": 2.85}, {"time": 1.1, "value": -1.41}, {"time": 1.2, "value": 2.85}, {"time": 1.3, "value": -1.41}, {"time": 1.4, "value": 2.85}, {"time": 1.5333, "value": -1.41}, {"time": 1.7333, "value": 2.85}, {"time": 2.0333, "curve": "stepped"}, {"time": 2.2, "curve": "stepped"}, {"time": 2.4667}], "translate": [{}, {"time": 0.3333, "x": 5.44, "y": -261.22}, {"time": 1.2, "x": 17.44, "y": -39.22}, {"time": 2.0333}, {"time": 2.2, "x": -6, "y": -72}, {"time": 2.4667}]}, "L ARM": {"rotate": [{}, {"time": 0.6667, "value": 99.09}, {"time": 1.1667, "value": 175.77}, {"time": 1.5667, "value": 203.23, "curve": "stepped"}, {"time": 1.9667, "value": 203.23}, {"time": 2.4667, "value": 360}], "translate": [{"curve": "stepped"}, {"time": 2.4667}]}, "L ARM1": {"rotate": [{}, {"time": 1.1667, "value": 40.73}, {"time": 1.5667, "value": -11.27, "curve": "stepped"}, {"time": 1.9667, "value": -11.27}, {"time": 2.4667}], "translate": [{"curve": "stepped"}, {"time": 2.4667}]}, "L ARM2": {"rotate": [{}], "translate": [{}]}, "L ARM3": {"rotate": [{}], "translate": [{}]}, "R ARM": {"rotate": [{}, {"time": 0.6667, "value": -101.46}, {"time": 1.1667, "value": -190.32}, {"time": 1.5667, "value": -211.99, "curve": "stepped"}, {"time": 1.9667, "value": -211.99}, {"time": 2.4667, "value": -360}], "translate": [{"curve": "stepped"}, {"time": 2.4667}]}, "R ARM 1": {"rotate": [{}, {"time": 0.6667, "value": -32.42}, {"time": 1.1667, "value": -41.82}, {"time": 1.5667, "value": 10.82, "curve": "stepped"}, {"time": 1.9667, "value": 10.82}, {"time": 2.4667}], "translate": [{"curve": "stepped"}, {"time": 2.4667}]}, "R ARM 3": {"rotate": [{}], "translate": [{}]}, "R ARM 2": {"rotate": [{}], "translate": [{}]}, "TIE 1": {"rotate": [{}], "translate": [{}]}, "TIE 2": {"rotate": [{}], "translate": [{}]}, "L LEG": {"rotate": [{"value": -5.69}], "translate": [{}]}, "L LEG 2": {"rotate": [{"value": 7.23}], "translate": [{}]}, "R LEG": {"rotate": [{"value": -1.65}], "translate": [{}]}, "R LEG 2": {"rotate": [{"value": 0.13}], "translate": [{}]}, "R EYE": {"rotate": [{}], "translate": [{"x": -43.95, "y": 71.54}]}, "L EYE": {"rotate": [{"curve": "stepped"}, {"time": 2.3333}], "translate": [{"curve": "stepped"}, {"time": 2.3333}], "scale": [{}, {"time": 0.3333, "y": 0}, {"time": 2.1, "y": 0.1}, {"time": 2.3333}]}, "R BLUSH": {"rotate": [{}], "translate": [{}]}, "L BLUSH": {"rotate": [{}], "translate": [{}]}, "R EYELID": {"rotate": [{"curve": "stepped"}, {"time": 2.3333}], "translate": [{"curve": "stepped"}, {"time": 2.3333}], "scale": [{}, {"time": 0.3333, "y": 0}, {"time": 2.1, "y": 0.1}, {"time": 2.3333}]}, "L EYELID": {"rotate": [{"curve": "stepped"}, {"time": 2.3333}], "translate": [{"curve": "stepped"}, {"time": 2.3333}], "scale": [{}, {"time": 0.3333, "y": 0}, {"time": 2.1, "y": 0.1}, {"time": 2.3333}]}, "L CHEEK": {"rotate": [{}], "translate": [{}]}, "R CHEEK": {"rotate": [{}], "translate": [{}]}, "EATING": {"rotate": [{}], "translate": [{}]}}}}}
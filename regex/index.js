// const fs = require("node:fs");
// const data = fs.readFileSync(__dirname + "/saymee-loyalty-out__2025-02-24.log", "utf8");
// let regex = /.*processing first Login event.*\n.*không.*gói c<PERSON>ớc/g;

// let m;
// let matches = [];
// while ((m = regex.exec(data)) !== null) {
//   // m is a match object, which has the index of the current match
//     matches.push(m[0]);
//     console.log(`Count: ${matches.length}`);
// }

// fs.writeFileSync(__dirname + "/output_saymee-loyalty-out__2025-02-24.txt", matches.join("\n"), "utf8");

// const fs = require("node:fs");
// const data = fs.readFileSync(
//   __dirname + "/output_saymee-loyalty-out__2025-02-24.txt",
//   "utf8"
// );
// let regExp = /(\+84|84|0|(.{0}))([3|5|7|8|9])([0-9]{8})/g;

// let m;
// let matches = [];
// let distinct = {};
// while ((m = regExp.exec(data)) !== null) {
//   if (distinct[m[0]]) {
//     continue;
//   }
//   distinct[m[0]] = true;
//   // m is a match object, which has the index of the current match
//   matches.push(m[0]);
//   console.log(`Count: ${matches.length}`);
// }

// fs.writeFileSync(
//   __dirname + "/phone-out__2025-02-24.txt",
//   matches.join("\n"),
//   "utf8"
// );

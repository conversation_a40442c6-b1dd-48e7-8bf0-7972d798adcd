const express = require("express");
const app = express();
const multer = require("multer");
const path = require("path");
const fs = require("fs");

const uploadFolder = path.join(__dirname, "public/images");

// Multer config
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, uploadFolder);
  },
  filename: (req, file, cb) => {
    // Save with original name (you can modify this for uniqueness)
    cb(null, file.originalname);
  },
});

app.use(express.json());
// Serve static files from "uploads" folder

const upload = multer({ storage });

// app.get(fileUpload)
app.use((req, _res, next) => {
  console.log(req.method, req.url);
  // res.send("Hello World");
  next();
});

app.use("/public/images", express.static(uploadFolder));

// Route: Upload file
app.post("/upload-image", upload.single("file"), (req, res) => {
  if (!req.file) return res.status(400).send("No file uploaded.");
  res.send({
    message: "File uploaded successfully!",
    filename: req.file.filename,
    url: `/public/images/${req.file.filename}`,
  });
});

app.use((req, res) => {
  res.send("Hello World");
});

app.listen(3000, () => {
  console.log("Server is running on port 3000");
});

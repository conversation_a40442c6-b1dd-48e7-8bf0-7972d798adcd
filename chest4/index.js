let data = {
  skeleton: {
    hash: "KnqfuVdjzvc",
    spine: "4.0.64",
    x: -216.83,
    y: -25.96,
    width: 445.75,
    height: 497.39,
    images: "./Images/",
    audio: "C:/Users/<USER>/Downloads/Outsource/Fix",
  },
  bones: [
    {
      name: "root",
    },
    {
      name: "bone",
      parent: "root",
      x: 20.15,
      y: 1.55,
    },
    {
      name: "<PERSON>",
      parent: "bone",
      length: 94.41,
      rotation: 90,
      x: -20.15,
      y: -1.55,
    },
    {
      name: "Nap",
      parent: "<PERSON>",
      length: 74.45,
      x: 98.5,
    },
    {
      name: "<PERSON><PERSON>_<PERSON>",
      parent: "<PERSON>",
      length: 21.67,
      rotation: 180,
      x: 88.51,
      y: -9.11,
      scaleX: 0.9867,
      scaleY: 0.9867,
    },
    {
      name: "Bong",
      parent: "bone",
      x: -42.65,
      y: -1.31,
    },
    {
      name: "<PERSON>rk<PERSON><PERSON>",
      parent: "bone",
      rotation: -5.81,
      x: 72.8,
      y: 19.81,
      scaleX: 0.565,
      scaleY: 0.565,
    },
    {
      name: "<PERSON>rk<PERSON>2",
      parent: "bone",
      x: 50.72,
      y: 42.84,
    },
    {
      name: "<PERSON>rkle3",
      parent: "bone",
      rotation: -5.04,
      x: -99.38,
      y: 158.01,
    },
    {
      name: "Sparkle4",
      parent: "bone",
      rotation: -2.9,
      x: -70.68,
      y: 194.78,
      scaleX: 0.6475,
      scaleY: 0.6475,
    },
    {
      name: "Bubble1",
      parent: "bone",
      length: 16.74,
      rotation: 90,
      x: 138.57,
      y: 15.69,
    },
    {
      name: "Bubble2",
      parent: "bone",
      length: 16.74,
      rotation: 90,
      x: 105.61,
      y: 3.88,
      scaleX: 0.5232,
      scaleY: 0.5232,
    },
    {
      name: "Bubble3",
      parent: "bone",
      length: 16.74,
      rotation: 90,
      x: 58.88,
      y: 3.39,
      scaleX: 0.3609,
      scaleY: 0.3609,
    },
    {
      name: "Bubble4",
      parent: "bone",
      length: 16.74,
      rotation: 90,
      x: -85.73,
      y: 27,
      scaleX: 0.7437,
      scaleY: 0.7437,
    },
    {
      name: "Bubble5",
      parent: "bone",
      length: 16.74,
      rotation: 90,
      x: -143.77,
      y: 16.67,
      scaleX: 0.3969,
      scaleY: 0.3969,
    },
    {
      name: "Bubble6",
      parent: "bone",
      length: 16.74,
      rotation: 90,
      x: -162.95,
      y: 70.78,
      scaleX: 0.5623,
      scaleY: 0.5623,
    },
    {
      name: "Bubble7",
      parent: "bone",
      length: 16.74,
      rotation: 90,
      x: 103.76,
      y: 62.57,
      scaleX: 0.3236,
      scaleY: 0.3236,
    },
    {
      name: "Card_Fly",
      parent: "bone",
      length: 109.51,
      rotation: 90,
      x: -20.15,
      y: 38.45,
    },
    {
      name: "Card2",
      parent: "Card_Fly",
      rotation: -90.72,
      x: 56.95,
      y: 113.22,
    },
    {
      name: "Card",
      parent: "Card_Fly",
      rotation: -90.72,
      x: 55.5,
    },
    {
      name: "Card3",
      parent: "Card_Fly",
      rotation: -90.72,
      x: 54.06,
      y: -115.87,
    },
  ],
  slots: [
    {
      name: "Bong",
      bone: "Bong",
      attachment: "Bong",
    },
    {
      name: "Than_close",
      bone: "Than",
      attachment: "Than_close",
    },
    {
      name: "Anh_sang_close",
      bone: "Anh_Sang",
      attachment: "Anh_sang_close",
    },
    {
      name: "Nap_close",
      bone: "Nap",
      attachment: "Nap_close",
    },
    {
      name: "Mieng_open",
      bone: "Than",
      attachment: "Mieng_open",
    },
    {
      name: "Glow2",
      bone: "Nap",
      attachment: "Glow2",
    },
    {
      name: "Glow3",
      bone: "Nap",
      attachment: "Glow3",
    },
    {
      name: "Glow1",
      bone: "Nap",
      attachment: "Glow1",
    },
    {
      name: "Golden",
      bone: "Card",
      attachment: "Golden2",
    },
    {
      name: "Golden2",
      bone: "Card2",
    },
    {
      name: "Golden3",
      bone: "Card3",
    },
    {
      name: "Sparkles",
      bone: "Sparkle1",
      attachment: "Sparkles",
    },
    {
      name: "Sparkles2",
      bone: "Sparkle2",
      attachment: "Sparkles",
    },
    {
      name: "Sparkles3",
      bone: "Sparkle3",
      attachment: "Sparkles",
    },
    {
      name: "Sparkles4",
      bone: "Sparkle4",
      attachment: "Sparkles",
    },
    {
      name: "Bubble",
      bone: "Bubble1",
      attachment: "Bubble",
    },
    {
      name: "Bubble2",
      bone: "Bubble2",
      attachment: "Bubble",
    },
    {
      name: "Bubble7",
      bone: "Bubble7",
      attachment: "Bubble",
    },
    {
      name: "Bubble4",
      bone: "Bubble4",
      attachment: "Bubble",
    },
    {
      name: "Bubble5",
      bone: "Bubble5",
      attachment: "Bubble",
    },
    {
      name: "Bubble6",
      bone: "Bubble6",
      attachment: "Bubble",
    },
    {
      name: "Bubble3",
      bone: "Bubble3",
      attachment: "Bubble",
    },
    {
      name: "Frame 2071857771",
      bone: "Card2",
      attachment: "Frame 2071857777",
    },
    {
      name: "Frame 2071857772",
      bone: "Card",
      attachment: "Frame 2071857775",
    },
    {
      name: "Frame 2071857774",
      bone: "root",
    },
    {
      name: "Golden4",
      bone: "root",
    },
  ],
  skins: [
    {
      name: "default",
      attachments: {
        Anh_sang_close: {
          Anh_sang_close: {
            type: "mesh",
            uvs: [
              0.83896, 0.0151, 0.84844, 0.02967, 0.89592, 0.18031, 0.95568,
              0.39237, 0.96581, 0.4564, 0.96592, 0.49334, 0.956, 0.51239,
              0.76694, 0.59492, 0.59907, 0.67298, 0.48417, 0.72327, 0.45906,
              0.73319, 0.20363, 0.849, 0.13046, 0.87775, 0.05132, 0.91329,
              0.02743, 0.91339, 0.02689, 0.86237, 0.03784, 0.75926, 0.09046,
              0.42532, 0.10665, 0.34167, 0.13922, 0.3243, 0.3597, 0.22625,
              0.70753, 0.07186, 0.80066, 0.02971,
            ],
            triangles: [
              6, 3, 4, 6, 4, 5, 7, 21, 22, 22, 2, 7, 1, 22, 0, 2, 22, 1, 6, 7,
              2, 6, 2, 3, 8, 20, 21, 8, 21, 7, 9, 20, 8, 10, 20, 9, 11, 19, 20,
              11, 20, 10, 19, 17, 18, 11, 17, 19, 11, 12, 17, 16, 17, 12, 13,
              15, 16, 12, 13, 16, 14, 15, 13,
            ],
            vertices: [
              1, 4, -12.03, 95.01, 1, 1, 4, -10.29, 97.54, 1, 1, 4, 5.52,
              109.14, 1, 1, 4, 27.45, 123.42, 1, 1, 4, 33.72, 125.45, 1, 1, 4,
              37.09, 124.97, 1, 1, 4, 38.38, 121.84, 1, 1, 4, 37.49, 66.12, 1,
              1, 4, 37.14, 16.58, 1, 1, 4, 36.62, -17.28, 1, 1, 4, 36.4, -24.67,
              1, 1, 4, 35.6, -100.01, 1, 1, 4, 34.97, -121.53, 1, 1, 4, 34.69,
              -144.86, 1, 1, 4, 33.64, -151.76, 1, 1, 4, 28.97, -151.2, 1, 1, 4,
              20.08, -146.6, 1, 1, 4, -7.95, -126.75, 1, 1, 4, -14.84, -120.91,
              1, 1, 4, -14.97, -111.26, 1, 1, 4, -14.1, -46.26, 1, 1, 4, -12.71,
              56.29, 1, 1, 4, -12.41, 83.75, 1,
            ],
            hull: 23,
            edges: [
              0, 44, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16,
              18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32,
              34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44,
            ],
            width: 292,
            height: 92,
          },
        },
        Bong: {
          Bong: {
            type: "mesh",
            uvs: [1, 1, 0, 1, 0, 0, 1, 0],
            triangles: [1, 2, 3, 1, 3, 0],
            vertices: [210, -26.2, -165, -26.2, -165, 27.8, 210, 27.8],
            hull: 4,
            edges: [0, 2, 2, 4, 4, 6, 0, 6],
            width: 375,
            height: 54,
          },
        },
        Bubble: {
          Bubble: {
            type: "mesh",
            uvs: [1, 1, 0, 1, 0, 0, 1, 0],
            triangles: [1, 2, 3, 1, 3, 0],
            vertices: [-4.97, -4.27, -4.97, 5.73, 5.03, 5.73, 5.03, -4.27],
            hull: 4,
            edges: [0, 2, 2, 4, 4, 6, 0, 6],
            width: 10,
            height: 10,
          },
        },
        Bubble2: {
          Bubble: {
            type: "mesh",
            uvs: [1, 1, 0, 1, 0, 0, 1, 0],
            triangles: [1, 2, 3, 1, 3, 0],
            vertices: [-4.97, -4.27, -4.97, 5.73, 5.03, 5.73, 5.03, -4.27],
            hull: 4,
            edges: [0, 2, 2, 4, 4, 6, 0, 6],
            width: 10,
            height: 10,
          },
        },
        Bubble3: {
          Bubble: {
            type: "mesh",
            uvs: [1, 1, 0, 1, 0, 0, 1, 0],
            triangles: [1, 2, 3, 1, 3, 0],
            vertices: [-4.97, -4.27, -4.97, 5.73, 5.03, 5.73, 5.03, -4.27],
            hull: 4,
            edges: [0, 2, 2, 4, 4, 6, 0, 6],
            width: 10,
            height: 10,
          },
        },
        Bubble4: {
          Bubble: {
            type: "mesh",
            uvs: [1, 1, 0, 1, 0, 0, 1, 0],
            triangles: [1, 2, 3, 1, 3, 0],
            vertices: [-4.97, -4.27, -4.97, 5.73, 5.03, 5.73, 5.03, -4.27],
            hull: 4,
            edges: [0, 2, 2, 4, 4, 6, 0, 6],
            width: 10,
            height: 10,
          },
        },
        Bubble5: {
          Bubble: {
            type: "mesh",
            uvs: [1, 1, 0, 1, 0, 0, 1, 0],
            triangles: [1, 2, 3, 1, 3, 0],
            vertices: [-4.97, -4.27, -4.97, 5.73, 5.03, 5.73, 5.03, -4.27],
            hull: 4,
            edges: [0, 2, 2, 4, 4, 6, 0, 6],
            width: 10,
            height: 10,
          },
        },
        Bubble6: {
          Bubble: {
            type: "mesh",
            uvs: [1, 1, 0, 1, 0, 0, 1, 0],
            triangles: [1, 2, 3, 1, 3, 0],
            vertices: [-4.97, -4.27, -4.97, 5.73, 5.03, 5.73, 5.03, -4.27],
            hull: 4,
            edges: [0, 2, 2, 4, 4, 6, 0, 6],
            width: 10,
            height: 10,
          },
        },
        Bubble7: {
          Bubble: {
            type: "mesh",
            uvs: [1, 1, 0, 1, 0, 0, 1, 0],
            triangles: [1, 2, 3, 1, 3, 0],
            vertices: [-4.97, -4.27, -4.97, 5.73, 5.03, 5.73, 5.03, -4.27],
            hull: 4,
            edges: [0, 2, 2, 4, 4, 6, 0, 6],
            width: 10,
            height: 10,
          },
        },
        "Frame 2071857771": {
          "Frame 2071857771": {
            width: 180,
            height: 210,
          },
          "Frame 2071857772": {
            width: 180,
            height: 210,
          },
          "Frame 2071857773": {
            width: 180,
            height: 210,
          },
          "Frame 2071857775": {
            width: 180,
            height: 210,
          },
          "Frame 2071857776": {
            width: 180,
            height: 210,
          },
          "Frame 2071857777": {
            path: "Frame 2071857774",
            width: 180,
            height: 210,
          },
          Ticket_hoi_suc: {
            width: 180,
            height: 210,
          },
        },
        "Frame 2071857772": {
          "Frame 2071857771": {
            width: 180,
            height: 210,
          },
          "Frame 2071857772": {
            width: 180,
            height: 210,
          },
          "Frame 2071857773": {
            width: 180,
            height: 210,
          },
          "Frame 2071857774": {
            width: 180,
            height: 210,
          },
          "Frame 2071857775": {
            width: 180,
            height: 210,
          },
          "Frame 2071857776": {
            width: 180,
            height: 210,
          },
          Ticket_hoi_suc: {
            width: 180,
            height: 210,
          },
        },
        Glow1: {
          Glow1: {
            type: "mesh",
            uvs: [1, 1, 0, 1, 0, 0, 1, 0],
            triangles: [1, 2, 3, 0, 1, 3],
            vertices: [
              -16.74, -46.81, -15.36, 35.17, 290.59, 30.05, 289.22, -51.94,
            ],
            hull: 4,
            edges: [0, 2, 2, 4, 4, 6, 0, 6],
            width: 82,
            height: 306,
          },
        },
        Glow2: {
          Glow2: {
            type: "mesh",
            uvs: [1, 1, 0, 1, 0, 0, 1, 0],
            triangles: [1, 2, 3, 1, 3, 0],
            vertices: [
              -18.38, -222.49, -11.02, 216.83, 372.93, 210.4, 365.57, -228.92,
            ],
            hull: 4,
            edges: [0, 2, 2, 4, 4, 6, 0, 6],
            width: 332,
            height: 384,
          },
        },
        Glow3: {
          Glow3: {
            type: "mesh",
            uvs: [1, 1, 0, 1, 0, 0, 1, 0],
            triangles: [1, 2, 3, 1, 3, 0],
            vertices: [
              -19.49, -189.04, -13.2, 185.91, 338.75, 180.01, 332.46, -194.94,
            ],
            hull: 4,
            edges: [0, 2, 2, 4, 4, 6, 0, 6],
            width: 375,
            height: 352,
          },
        },
        Golden: {
          "Card 2": {
            type: "mesh",
            uvs: [1, 1, 0, 1, 0, 0, 1, 0],
            triangles: [1, 2, 3, 1, 3, 0],
            vertices: [
              49.65, -59.39, -47.34, -60.6, -48.85, 59.39, 48.14, 60.6,
            ],
            hull: 4,
            edges: [0, 2, 2, 4, 4, 6, 0, 6],
            width: 96,
            height: 120,
          },
          Golden2: {
            type: "mesh",
            path: "Golden",
            uvs: [1, 1, 0, 1, 0, 0, 1, 0],
            triangles: [1, 2, 3, 1, 3, 0],
            vertices: [
              91.78, -103.83, -88.21, -106.09, -90.84, 103.89, 89.14, 106.15,
            ],
            hull: 4,
            edges: [0, 2, 2, 4, 4, 6, 0, 6],
            width: 180,
            height: 210,
          },
        },
        Golden2: {
          "Card 2": {
            type: "mesh",
            uvs: [1, 1, 0, 1, 0, 0, 1, 0],
            triangles: [1, 2, 3, 1, 3, 0],
            vertices: [
              49.25, -59.39, -47.74, -60.6, -49.25, 59.39, 47.74, 60.6,
            ],
            hull: 4,
            edges: [0, 2, 2, 4, 4, 6, 0, 6],
            width: 96,
            height: 120,
          },
          Golden: {
            type: "mesh",
            uvs: [
              0.81646, 0.87578, 0.18354, 0.87578, 0.18354, 0.12422, 0.81646,
              0.12422,
            ],
            triangles: [1, 2, 3, 1, 3, 0],
            vertices: [
              60.91, -71.84, -59.08, -73.35, -60.91, 71.84, 59.08, 73.35,
            ],
            hull: 4,
            edges: [0, 2, 2, 4, 4, 6, 0, 6],
            width: 180,
            height: 210,
          },
        },
        Golden3: {
          Golden: {
            type: "mesh",
            uvs: [
              0.81646, 0.87578, 0.18354, 0.87578, 0.18354, 0.12422, 0.81646,
              0.12422,
            ],
            triangles: [1, 2, 3, 1, 3, 0],
            vertices: [
              61.31, -71.84, -58.68, -73.35, -60.51, 71.84, 59.48, 73.35,
            ],
            hull: 4,
            edges: [0, 2, 2, 4, 4, 6, 0, 6],
            width: 180,
            height: 210,
          },
          Trans: {
            type: "mesh",
            uvs: [1, 1, 0, 1, 0, 0, 1, 0],
            triangles: [1, 2, 3, 1, 3, 0],
            vertices: [
              49.65, -59.39, -47.34, -60.6, -48.85, 59.39, 48.14, 60.6,
            ],
            hull: 4,
            edges: [0, 2, 2, 4, 4, 6, 0, 6],
            width: 97,
            height: 120,
          },
        },
        Mieng_open: {
          Mieng_open: {
            type: "mesh",
            uvs: [1, 1, 0, 1, 0, 0, 1, 0],
            triangles: [1, 2, 3, 1, 3, 0],
            vertices: [
              82.05, -94.98, 83.92, 102.31, 115.13, 102.05, 113.25, -95.23,
            ],
            hull: 4,
            edges: [0, 2, 2, 4, 4, 6, 0, 6],
            width: 192,
            height: 24,
          },
        },
        Nap_close: {
          Nap_close: {
            type: "mesh",
            uvs: [1, 0.9313, 0, 0.9313, 0, 0.0687, 0.99996, 0],
            triangles: [0, 1, 2, 0, 2, 3],
            vertices: [
              1, 3, -52.9, -106.87, 1, 1, 3, -24.62, 127.33, 1, 1, 3, 87.53,
              113.07, 1, 1, 3, 68.18, -122.25, 1,
            ],
            hull: 4,
            edges: [0, 2, 2, 4, 4, 6, 0, 6],
            width: 237,
            height: 144,
          },
          Nap_open: {
            type: "mesh",
            uvs: [0.99999, 1, 0.00001, 1, 0, 0.00001, 0.99999, 0],
            triangles: [3, 1, 2, 0, 1, 3],
            vertices: [
              1, 3, 2.41, -110.22, 1, 1, 3, 6.24, 118.82, 1, 1, 3, 118.53,
              116.93, 1, 1, 3, 114.7, -112.1, 1,
            ],
            hull: 4,
            edges: [0, 2, 2, 4, 4, 6, 0, 6],
            width: 211,
            height: 112,
          },
        },
        Sparkles: {
          Sparkles: {
            type: "mesh",
            uvs: [1, 1, 0, 1, 0, 0, 1, 0],
            triangles: [1, 2, 3, 1, 3, 0],
            vertices: [
              9.94, -10.77, -11.06, -10.77, -11.06, 10.23, 9.94, 10.23,
            ],
            hull: 4,
            edges: [0, 2, 2, 4, 4, 6, 0, 6],
            width: 21,
            height: 21,
          },
        },
        Sparkles2: {
          Sparkles: {
            type: "mesh",
            uvs: [1, 1, 0, 1, 0, 0, 1, 0],
            triangles: [1, 2, 3, 1, 3, 0],
            vertices: [
              9.94, -10.77, -11.06, -10.77, -11.06, 10.23, 9.94, 10.23,
            ],
            hull: 4,
            edges: [0, 2, 2, 4, 4, 6, 0, 6],
            width: 21,
            height: 21,
          },
        },
        Sparkles3: {
          Sparkles: {
            type: "mesh",
            uvs: [1, 1, 0, 1, 0, 0, 1, 0],
            triangles: [1, 2, 3, 1, 3, 0],
            vertices: [
              9.94, -10.77, -11.06, -10.77, -11.06, 10.23, 9.94, 10.23,
            ],
            hull: 4,
            edges: [0, 2, 2, 4, 4, 6, 0, 6],
            width: 21,
            height: 21,
          },
        },
        Sparkles4: {
          Sparkles: {
            type: "mesh",
            uvs: [1, 1, 0, 1, 0, 0, 1, 0],
            triangles: [1, 2, 3, 1, 3, 0],
            vertices: [
              9.94, -10.77, -11.06, -10.77, -11.06, 10.23, 9.94, 10.23,
            ],
            hull: 4,
            edges: [0, 2, 2, 4, 4, 6, 0, 6],
            width: 21,
            height: 21,
          },
        },
        Than_close: {
          Than_close: {
            type: "mesh",
            uvs: [
              0.93713, 0, 0.98761, 0.59342, 0.95799, 0.79912, 0.15094, 0.98669,
              0.0547, 0.86707, 0, 0.24516, 0, 0.21318, 0.91823, 0,
            ],
            triangles: [5, 6, 7, 0, 1, 7, 3, 4, 5, 7, 3, 5, 1, 3, 7, 2, 3, 1],
            vertices: [
              105.96, -101.9, 23.87, -102.04, -3.14, -91.59, -3.78, 91.81,
              15.42, 111.02, 101.5, 111.56, 105.84, 110.97, 106.54, -97.69,
            ],
            hull: 8,
            edges: [0, 14, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14],
            width: 220,
            height: 120,
          },
          Than_open: {
            type: "mesh",
            uvs: [1, 1, 0, 1, 0, 0, 1, 0],
            triangles: [1, 2, 3, 1, 3, 0],
            vertices: [
              1, 2, -3.29, -98.73, 1, 1, 2, -1.46, 108.26, 1, 1, 2, 99.53,
              107.37, 1, 1, 2, 97.71, -99.62, 1,
            ],
            hull: 4,
            edges: [0, 2, 2, 4, 4, 6, 0, 6],
            width: 206,
            height: 91,
          },
        },
      },
    },
  ],
  animations: {
    Idle: {
      slots: {
        Anh_sang_close: {
          rgba: [
            {
              time: 0.2,
              color: "ffffffff",
            },
            {
              time: 0.5,
              color: "ffffff37",
            },
            {
              time: 0.9667,
              color: "ffffffd7",
            },
            {
              time: 1.1667,
              color: "ffffff40",
            },
            {
              time: 1.5,
              color: "ffffffff",
              curve: "stepped",
            },
            {
              time: 1.8667,
              color: "ffffffff",
            },
            {
              time: 2.2333,
              color: "ffffff8b",
            },
            {
              time: 2.5,
              color: "ffffffff",
            },
          ],
        },
        Bong: {
          rgba: [
            {
              color: "ffffffc8",
            },
            {
              time: 0.2,
              color: "ffffffff",
            },
            {
              time: 0.4667,
              color: "ffffffc8",
            },
            {
              time: 0.6667,
              color: "ffffffff",
            },
            {
              time: 0.9,
              color: "ffffffc8",
            },
            {
              time: 1.1333,
              color: "ffffffff",
            },
            {
              time: 1.4,
              color: "ffffffc8",
            },
            {
              time: 1.7,
              color: "ffffffff",
            },
            {
              time: 2,
              color: "ffffffc8",
            },
          ],
        },
        Bubble: {
          rgba: [
            {
              color: "ffffff00",
            },
            {
              time: 0.4333,
              color: "ffffffff",
            },
            {
              time: 0.8333,
              color: "ffffff00",
            },
          ],
        },
        Bubble2: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.7,
              color: "ffffff00",
            },
            {
              time: 1.1333,
              color: "ffffffff",
            },
            {
              time: 1.5333,
              color: "ffffff00",
            },
          ],
        },
        Bubble3: {
          rgba: [
            {
              color: "ffffff00",
            },
            {
              time: 0.8667,
              color: "ffffffff",
            },
            {
              time: 1.3333,
              color: "ffffff00",
            },
          ],
        },
        Bubble4: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.8333,
              color: "ffffff00",
            },
            {
              time: 1.2667,
              color: "ffffffff",
            },
            {
              time: 1.6667,
              color: "ffffff00",
            },
          ],
        },
        Bubble5: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.2667,
              color: "ffffff00",
            },
            {
              time: 0.6667,
              color: "ffffffff",
            },
            {
              time: 1.0667,
              color: "ffffff00",
            },
          ],
        },
        Bubble6: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 1.2667,
              color: "ffffff00",
            },
            {
              time: 1.6667,
              color: "ffffffff",
            },
            {
              time: 2.0667,
              color: "ffffff00",
            },
          ],
        },
        Bubble7: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.2333,
              color: "ffffff00",
            },
            {
              time: 0.6667,
              color: "ffffffff",
            },
            {
              time: 1.0667,
              color: "ffffff00",
            },
          ],
        },
        "Frame 2071857771": {
          attachment: [
            {
              name: null,
            },
          ],
        },
        "Frame 2071857772": {
          attachment: [
            {
              name: null,
            },
          ],
        },
        Glow1: {
          attachment: [
            {
              name: null,
            },
          ],
        },
        Glow2: {
          attachment: [
            {
              name: null,
            },
          ],
        },
        Glow3: {
          attachment: [
            {
              name: null,
            },
          ],
        },
        Golden: {
          attachment: [
            {
              name: null,
            },
          ],
        },
        Mieng_open: {
          attachment: [
            {
              name: null,
            },
          ],
        },
        Sparkles: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.7667,
              color: "ffffff00",
            },
            {
              time: 1.3333,
              color: "ffffffff",
            },
            {
              time: 1.6667,
              color: "ffffff00",
            },
          ],
        },
        Sparkles2: {
          rgba: [
            {
              color: "ffffff00",
            },
            {
              time: 0.5,
              color: "ffffffff",
            },
            {
              time: 0.9,
              color: "ffffff00",
            },
          ],
        },
        Sparkles3: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.5333,
              color: "ffffff00",
            },
            {
              time: 0.9333,
              color: "ffffffff",
            },
            {
              time: 1.3,
              color: "ffffff00",
            },
          ],
        },
        Sparkles4: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 1.5,
              color: "ffffff00",
            },
            {
              time: 1.9667,
              color: "ffffffff",
            },
            {
              time: 2.5,
              color: "ffffff00",
            },
          ],
          attachment: [
            {
              time: 3,
              name: "Sparkles",
            },
          ],
        },
      },
      bones: {
        Anh_Sang: {
          translate: [
            {
              x: 7.44,
              y: -1.55,
            },
          ],
          scale: [
            {
              x: 0.97,
              y: 0.97,
            },
          ],
        },
        Bong: {
          scale: [
            {
              x: 0.929,
              y: 0.929,
              curve: [0.067, 0.929, 0.133, 1, 0.067, 0.929, 0.133, 1],
            },
            {
              time: 0.2,
              curve: [0.289, 1, 0.378, 0.929, 0.289, 1, 0.378, 0.929],
            },
            {
              time: 0.4667,
              x: 0.929,
              y: 0.929,
              curve: [0.533, 0.929, 0.6, 1, 0.533, 0.929, 0.6, 1],
            },
            {
              time: 0.6667,
              curve: [0.744, 1, 0.822, 0.929, 0.744, 1, 0.822, 0.929],
            },
            {
              time: 0.9,
              x: 0.929,
              y: 0.929,
              curve: [0.978, 0.929, 1.056, 1, 0.978, 0.929, 1.056, 1],
            },
            {
              time: 1.1333,
              curve: [1.222, 1, 1.311, 0.929, 1.222, 1, 1.311, 0.929],
            },
            {
              time: 1.4,
              x: 0.929,
              y: 0.929,
              curve: [1.5, 0.929, 1.6, 1, 1.5, 0.929, 1.6, 1],
            },
            {
              time: 1.7,
              curve: [1.8, 1, 1.9, 0.929, 1.8, 1, 1.9, 0.929],
            },
            {
              time: 2,
              x: 0.929,
              y: 0.929,
            },
          ],
        },
        Bubble1: {
          translate: [
            {},
            {
              time: 0.8333,
              y: 32.35,
            },
          ],
        },
        Bubble2: {
          translate: [
            {
              time: 0.7,
            },
            {
              time: 1.5333,
              y: 32.35,
            },
          ],
        },
        Bubble3: {
          translate: [
            {
              time: 1.3333,
            },
            {
              time: 2.2333,
              y: 32.35,
            },
          ],
        },
        Bubble4: {
          translate: [
            {
              time: 0.8333,
            },
            {
              time: 1.6667,
              y: 32.35,
            },
          ],
        },
        Bubble5: {
          translate: [
            {
              time: 0.2667,
            },
            {
              time: 1.0667,
              y: 32.35,
            },
          ],
        },
        Bubble6: {
          translate: [
            {
              time: 1.2667,
            },
            {
              time: 2.0667,
              y: 32.35,
            },
          ],
        },
        Bubble7: {
          translate: [
            {
              time: 0.2333,
            },
            {
              time: 1.0667,
              y: 32.35,
            },
          ],
        },
        Nap: {
          rotate: [
            {
              value: 0.87,
            },
          ],
          translate: [
            {
              x: 2.37,
              curve: [0.044, 2.37, 0.093, -1.89, 0.044, 0, 0.093, -0.03],
            },
            {
              time: 0.1333,
              x: -1.89,
              y: -0.03,
              curve: [0.201, -1.89, 0.268, 5.39, 0.201, -0.03, 0.268, 0.04],
            },
            {
              time: 0.3333,
              x: 5.43,
              y: 0.04,
              curve: [1.227, 5.94, 0.444, 3.72, 1.227, 0.05, 0.444, 0.01],
            },
            {
              time: 0.5,
              x: 2.37,
              curve: [0.544, 1.28, 0.593, -1.89, 0.544, -0.01, 0.593, -0.03],
            },
            {
              time: 0.6333,
              x: -1.89,
              y: -0.03,
              curve: [0.701, -1.89, 0.768, 5.39, 0.701, -0.03, 0.768, 0.04],
            },
            {
              time: 0.8333,
              x: 5.43,
              y: 0.04,
              curve: [1.727, 5.94, 0.944, 3.73, 1.727, 0.05, 0.944, 0.01],
            },
            {
              time: 1,
              x: 2.37,
              curve: [1.044, 1.29, 1.093, -1.89, 1.044, -0.01, 1.093, -0.03],
            },
            {
              time: 1.1333,
              x: -1.89,
              y: -0.03,
              curve: [1.201, -1.89, 1.268, 5.39, 1.201, -0.03, 1.268, 0.04],
            },
            {
              time: 1.3333,
              x: 5.43,
              y: 0.04,
              curve: [2.227, 5.94, 1.444, 3.72, 2.227, 0.05, 1.444, 0.01],
            },
            {
              time: 1.5,
              x: 2.37,
              curve: [1.544, 1.28, 1.593, -1.89, 1.544, -0.01, 1.593, -0.03],
            },
            {
              time: 1.6333,
              x: -1.89,
              y: -0.03,
              curve: [1.701, -1.89, 1.96, -4.43, 1.701, -0.03, 1.96, -0.04],
            },
            {
              time: 2,
              x: -4.43,
              y: -0.04,
              curve: [2.067, -4.43, 2.101, 5.39, 2.067, -0.04, 2.101, 0.04],
            },
            {
              time: 2.1667,
              x: 5.43,
              y: 0.04,
              curve: [3.06, 5.94, 2.3, 3.83, 3.06, 0.05, 2.3, 0.01],
            },
            {
              time: 2.3667,
              x: 2.37,
              curve: [2.411, 1.39, 2.46, -1.89, 2.411, -0.01, 2.46, -0.03],
            },
            {
              time: 2.5,
              x: -1.89,
              y: -0.03,
              curve: [2.567, -1.89, 2.633, 2.37, 2.567, -0.03, 2.633, 0],
            },
            {
              time: 2.7,
              x: 2.37,
            },
          ],
          scale: [
            {
              x: 1.045,
            },
          ],
        },
        Than: {
          rotate: [
            {
              value: -0.5,
              curve: "stepped",
            },
            {
              time: 0.5333,
              value: -0.5,
              curve: [0.589, -0.5, 0.644, 9.59],
            },
            {
              time: 0.7,
              value: 11.38,
              curve: [0.733, 12.45, 0.767, 12.45],
            },
            {
              time: 0.8,
              value: 12.45,
              curve: [0.844, 12.45, 0.889, -0.5],
            },
            {
              time: 0.9333,
              value: -0.5,
              curve: "stepped",
            },
            {
              time: 1,
              value: -0.5,
              curve: [1.056, -0.5, 1.111, -7.65],
            },
            {
              time: 1.1667,
              value: -9.98,
              curve: [1.2, -11.37, 1.233, -11.66],
            },
            {
              time: 1.2667,
              value: -11.66,
              curve: [1.311, -11.66, 1.356, -0.5],
            },
            {
              time: 1.4,
              value: -0.5,
            },
          ],
          translate: [
            {
              y: -0.6,
              curve: "stepped",
            },
            {
              time: 0.1,
              y: -0.6,
              curve: [0.144, 0, 0.189, 0, 0.144, -0.6, 0.189, 65.89],
            },
            {
              time: 0.2333,
              y: 71.88,
              curve: [0.267, 0, 0.3, 0, 0.267, 76.37, 0.3, 76.37],
            },
            {
              time: 0.3333,
              y: 76.37,
              curve: [0.378, 0, 0.422, 0, 0.378, 76.37, 0.422, -0.6],
            },
            {
              time: 0.4667,
              y: -0.6,
              curve: "stepped",
            },
            {
              time: 0.5667,
              y: -0.6,
              curve: [0.611, 0, 0.656, 0, 0.611, -0.6, 0.656, 71.88],
            },
            {
              time: 0.7,
              y: 71.88,
              curve: "stepped",
            },
            {
              time: 0.8,
              y: 71.88,
              curve: [0.844, 0, 0.889, 0, 0.844, 71.88, 0.889, -0.6],
            },
            {
              time: 0.9333,
              y: -0.6,
              curve: "stepped",
            },
            {
              time: 1.0333,
              y: -0.6,
              curve: [1.078, 0, 1.122, 0, 1.078, -0.6, 1.122, 71.88],
            },
            {
              time: 1.1667,
              y: 71.88,
              curve: "stepped",
            },
            {
              time: 1.2667,
              y: 71.88,
              curve: [1.311, 0, 1.356, 0, 1.311, 71.88, 1.356, -0.6],
            },
            {
              time: 1.4,
              y: -0.6,
              curve: "stepped",
            },
            {
              time: 1.6333,
              y: -0.6,
              curve: [1.656, 0, 1.678, -4.64, 1.656, -0.6, 1.678, -0.6],
            },
            {
              time: 1.7,
              x: -4.64,
              y: -0.6,
              curve: [1.722, -4.64, 1.744, 5.22, 1.722, -0.6, 1.744, -0.6],
            },
            {
              time: 1.7667,
              x: 5.22,
              y: -0.6,
              curve: [1.789, 5.22, 1.811, -4.64, 1.789, -0.6, 1.811, -0.6],
            },
            {
              time: 1.8333,
              x: -4.64,
              y: -0.6,
              curve: [1.856, -4.64, 1.878, 5.22, 1.856, -0.6, 1.878, -0.6],
            },
            {
              time: 1.9,
              x: 5.22,
              y: -0.6,
              curve: [1.922, 5.22, 1.944, -2.4, 1.922, -0.6, 1.944, -0.6],
            },
            {
              time: 1.9667,
              x: -2.4,
              y: -0.6,
              curve: [2.011, -2.4, 2.056, 0, 2.011, -0.6, 2.056, 68.29],
            },
            {
              time: 2.1,
              y: 71.88,
              curve: [2.156, 0, 2.211, 0, 2.156, 76.37, 2.211, 76.37],
            },
            {
              time: 2.2667,
              y: 76.37,
              curve: [2.3, 0, 2.333, 0, 2.3, 76.37, 2.333, -0.6],
            },
            {
              time: 2.3667,
              y: -0.6,
            },
          ],
          scale: [
            {
              curve: [0.033, 1, 0.067, 0.664, 0.033, 1, 0.067, 1],
            },
            {
              time: 0.1,
              x: 0.664,
              curve: [0.144, 0.664, 0.189, 1.045, 0.144, 1, 0.189, 1],
            },
            {
              time: 0.2333,
              x: 1.082,
              curve: [0.267, 1.11, 0.3, 1.11, 0.267, 1, 0.3, 1],
            },
            {
              time: 0.3333,
              x: 1.11,
              curve: [0.378, 1.11, 0.422, 1.11, 0.378, 1, 0.422, 1],
            },
            {
              time: 0.4667,
              x: 1.039,
              curve: [0.5, 0.986, 0.533, 0.664, 0.5, 1, 0.533, 1],
            },
            {
              time: 0.5667,
              x: 0.664,
              curve: [0.611, 0.664, 0.656, 1.029, 0.611, 1, 0.656, 1],
            },
            {
              time: 0.7,
              x: 1.082,
              curve: [0.733, 1.122, 0.767, 1.122, 0.733, 1, 0.767, 1],
            },
            {
              time: 0.8,
              x: 1.122,
              curve: [0.844, 1.122, 0.889, 1.122, 0.844, 1, 0.889, 1],
            },
            {
              time: 0.9333,
              x: 1.039,
              curve: [0.967, 0.977, 1, 0.664, 0.967, 1, 1, 1],
            },
            {
              time: 1.0333,
              x: 0.664,
              curve: [1.078, 0.664, 1.122, 1.042, 1.078, 1, 1.122, 1],
            },
            {
              time: 1.1667,
              x: 1.082,
              curve: [1.2, 1.112, 1.233, 1.112, 1.2, 1, 1.233, 1],
            },
            {
              time: 1.2667,
              x: 1.112,
              curve: [1.311, 1.112, 1.356, 1.112, 1.311, 1, 1.356, 1],
            },
            {
              time: 1.4,
              x: 1.039,
              curve: [1.433, 0.984, 1.467, 0.664, 1.433, 1, 1.467, 1],
            },
            {
              time: 1.5,
              x: 0.664,
              curve: [1.544, 0.664, 1.589, 1, 1.544, 1, 1.589, 1],
            },
            {
              time: 1.6333,
              curve: [1.744, 1, 1.856, 0.664, 1.744, 1, 1.856, 1],
            },
            {
              time: 1.9667,
              x: 0.664,
              curve: [2.011, 0.664, 2.056, 1.06, 2.011, 1, 2.056, 1],
            },
            {
              time: 2.1,
              x: 1.082,
              curve: [2.156, 1.11, 2.211, 1.11, 2.156, 1, 2.211, 1],
            },
            {
              time: 2.2667,
              x: 1.11,
              curve: [2.3, 1.11, 2.333, 1.103, 2.3, 1, 2.333, 1],
            },
            {
              time: 2.3667,
              x: 1.039,
              curve: [2.411, 0.954, 2.456, 0.664, 2.411, 1, 2.456, 1],
            },
            {
              time: 2.5,
              x: 0.664,
              curve: [2.533, 0.664, 2.567, 1, 2.533, 1, 2.567, 1],
            },
            {
              time: 2.6,
            },
          ],
        },
        Sparkle1: {
          rotate: [
            {
              time: 0.7667,
              value: 12.94,
            },
            {
              time: 1.6667,
              value: -6.03,
            },
          ],
        },
        Sparkle2: {
          rotate: [
            {
              value: 12.94,
            },
            {
              time: 0.9,
              value: -6.03,
            },
          ],
        },
        Sparkle3: {
          rotate: [
            {},
            {
              time: 0.5333,
              value: 12.94,
            },
            {
              time: 1.3,
              value: -6.03,
            },
          ],
        },
        Sparkle4: {
          rotate: [
            {},
            {
              time: 1.5,
              value: 12.94,
            },
            {
              time: 2.5,
              value: -6.03,
            },
          ],
        },
      },
      deform: {
        default: {
          Nap_close: {
            Nap_close: [
              {
                vertices: [
                  3.27547, 1.9277, 3.8477, 1.8578, 3.81247, 1.58382, 3.23745,
                  1.63179,
                ],
              },
            ],
          },
          Than_close: {
            Than_close: [
              {
                offset: 12,
                vertices: [8.10991, 0.00001, 8.10991],
              },
            ],
          },
        },
      },
    },
    Open: {
      slots: {
        Anh_sang_close: {
          rgba: [
            {
              color: "ffffffff",
            },
            {
              time: 0.2667,
              color: "ffffff33",
            },
            {
              time: 0.5,
              color: "ffffffff",
            },
          ],
          attachment: [
            {
              time: 0.5333,
              name: null,
            },
          ],
        },
        Bong: {
          rgba: [
            {
              color: "ffffffc8",
            },
            {
              time: 0.5,
              color: "ffffffff",
              curve: [
                0.556, 1, 0.611, 1, 0.556, 1, 0.611, 1, 0.556, 1, 0.611, 1,
                0.556, 0.92, 0.611, 0.76,
              ],
            },
            {
              time: 0.6667,
              color: "ffffffc1",
              curve: [
                0.756, 1, 0.844, 1, 0.756, 1, 0.844, 1, 0.756, 1, 0.844, 1,
                0.756, 0.76, 0.844, 1,
              ],
            },
            {
              time: 0.9333,
              color: "ffffffff",
            },
          ],
        },
        Bubble: {
          rgba: [
            {
              color: "ffffff00",
            },
            {
              time: 0.4333,
              color: "ffffffff",
            },
            {
              time: 0.8333,
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 1.6667,
              color: "ffffff00",
            },
            {
              time: 2.1,
              color: "ffffffff",
            },
            {
              time: 2.5,
              color: "ffffff00",
            },
          ],
        },
        Bubble2: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.7,
              color: "ffffff00",
            },
            {
              time: 1.1333,
              color: "ffffffff",
            },
            {
              time: 1.5333,
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 2.3667,
              color: "ffffff00",
            },
            {
              time: 2.8,
              color: "ffffffff",
            },
            {
              time: 3.2,
              color: "ffffff00",
            },
          ],
        },
        Bubble3: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.4333,
              color: "ffffff00",
            },
            {
              time: 0.8667,
              color: "ffffffff",
            },
            {
              time: 1.2667,
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 2.1,
              color: "ffffff00",
            },
            {
              time: 2.5333,
              color: "ffffffff",
            },
            {
              time: 2.9333,
              color: "ffffff00",
            },
          ],
        },
        Bubble4: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.8333,
              color: "ffffff00",
            },
            {
              time: 1.2667,
              color: "ffffffff",
            },
            {
              time: 1.6667,
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 2.5,
              color: "ffffff00",
            },
            {
              time: 2.9333,
              color: "ffffffff",
            },
            {
              time: 3.3333,
              color: "ffffff00",
            },
          ],
        },
        Bubble5: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.2667,
              color: "ffffff00",
            },
            {
              time: 0.6667,
              color: "ffffffff",
            },
            {
              time: 1.0667,
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 1.9333,
              color: "ffffff00",
            },
            {
              time: 2.3333,
              color: "ffffffff",
            },
            {
              time: 2.7333,
              color: "ffffff00",
            },
          ],
        },
        Bubble6: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.6667,
              color: "ffffff00",
            },
            {
              time: 1.0667,
              color: "ffffffff",
            },
            {
              time: 1.4667,
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 2.3333,
              color: "ffffff00",
            },
            {
              time: 2.7333,
              color: "ffffffff",
            },
            {
              time: 3.1333,
              color: "ffffff00",
            },
          ],
        },
        Bubble7: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.2333,
              color: "ffffff00",
            },
            {
              time: 0.6667,
              color: "ffffffff",
            },
            {
              time: 1.0667,
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 1.9,
              color: "ffffff00",
            },
            {
              time: 2.3333,
              color: "ffffffff",
            },
            {
              time: 2.7333,
              color: "ffffff00",
            },
          ],
        },
        "Frame 2071857771": {
          attachment: [
            {
              name: null,
            },
          ],
        },
        "Frame 2071857772": {
          attachment: [
            {
              name: null,
            },
            {
              time: 0.8333,
              name: "Frame 2071857771",
            },
            {
              time: 1.1333,
              name: null,
            },
            {
              time: 1.3333,
              name: "Frame 2071857772",
            },
            {
              time: 1.5667,
              name: null,
            },
            {
              time: 1.8,
              name: "Frame 2071857773",
            },
            {
              time: 2.0333,
              name: null,
            },
            {
              time: 2.2333,
              name: "Frame 2071857775",
            },
            {
              time: 2.5,
              name: null,
            },
          ],
        },
        Glow1: {
          rgba: [
            {
              time: 0.5333,
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.9,
              color: "ffffff00",
            },
            {
              time: 1.3333,
              color: "ffffff26",
            },
            {
              time: 1.6333,
              color: "ffffff3c",
            },
            {
              time: 1.6667,
              color: "ffffff3e",
            },
            {
              time: 2,
              color: "ffffff5b",
            },
            {
              time: 3.1333,
              color: "ffffff00",
            },
          ],
          attachment: [
            {
              name: null,
            },
            {
              time: 0.5333,
              name: "Glow1",
            },
          ],
        },
        Glow2: {
          rgba: [
            {
              time: 0.5333,
              color: "ffffff45",
            },
            {
              time: 0.7333,
              color: "ffffffff",
              curve: "stepped",
            },
            {
              time: 1.2333,
              color: "ffffffff",
            },
            {
              time: 1.3333,
              color: "ffffff7b",
            },
            {
              time: 1.5333,
              color: "ffffff67",
            },
            {
              time: 1.6667,
              color: "ffffff6a",
            },
            {
              time: 1.7667,
              color: "ffffff6d",
            },
            {
              time: 2.1,
              color: "ffffffff",
            },
            {
              time: 2.4,
              color: "ffffff7b",
            },
            {
              time: 2.9333,
              color: "ffffffff",
            },
            {
              time: 3.4667,
              color: "ffffff93",
            },
          ],
          attachment: [
            {
              name: null,
            },
            {
              time: 0.5333,
              name: "Glow2",
            },
          ],
        },
        Glow3: {
          rgba: [
            {
              time: 0.5333,
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.6667,
              color: "ffffff00",
            },
            {
              time: 1,
              color: "ffffff8a",
            },
            {
              time: 1.3333,
              color: "ffffffff",
              curve: "stepped",
            },
            {
              time: 2,
              color: "ffffffff",
            },
            {
              time: 2.3,
              color: "ffffffed",
            },
            {
              time: 2.8333,
              color: "ffffff8a",
            },
            {
              time: 3.4667,
              color: "ffffffff",
            },
          ],
          attachment: [
            {
              name: null,
            },
            {
              time: 0.5333,
              name: "Glow3",
            },
          ],
        },
        Golden: {
          attachment: [
            {
              name: null,
            },
            {
              time: 0.5333,
              name: "Golden2",
            },
            {
              time: 0.8333,
              name: null,
            },
            {
              time: 1.1,
              name: "Golden2",
            },
            {
              time: 1.3333,
              name: null,
            },
            {
              time: 1.5667,
              name: "Golden2",
            },
            {
              time: 1.8,
              name: null,
            },
            {
              time: 2.0333,
              name: "Golden2",
            },
            {
              time: 2.2333,
              name: null,
            },
            {
              time: 2.5,
              name: "Golden2",
            },
          ],
        },
        Mieng_open: {
          attachment: [
            {
              name: null,
            },
            {
              time: 0.5333,
              name: "Mieng_open",
            },
          ],
        },
        Nap_close: {
          attachment: [
            {
              time: 0.5333,
              name: "Nap_open",
            },
          ],
        },
        Sparkles: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.5,
              color: "ffffff00",
            },
            {
              time: 1.0667,
              color: "ffffffff",
            },
            {
              time: 1.4,
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 1.9,
              color: "ffffff00",
            },
            {
              time: 2.4667,
              color: "ffffffff",
            },
            {
              time: 2.8,
              color: "ffffff00",
            },
          ],
        },
        Sparkles2: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 2,
              color: "ffffff00",
            },
            {
              time: 2.5,
              color: "ffffffff",
            },
            {
              time: 2.9,
              color: "ffffff00",
            },
          ],
        },
        Sparkles3: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 1.0333,
              color: "ffffff00",
            },
            {
              time: 1.4333,
              color: "ffffffff",
            },
            {
              time: 1.6667,
              color: "ffffff5c",
            },
            {
              time: 1.8,
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 2.5,
              color: "ffffff00",
            },
            {
              time: 2.9,
              color: "ffffffff",
            },
            {
              time: 3.2667,
              color: "ffffff00",
            },
          ],
        },
        Sparkles4: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 1,
              color: "ffffff00",
            },
            {
              time: 1.4667,
              color: "ffffffff",
            },
            {
              time: 1.6667,
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 2.2333,
              color: "ffffff00",
            },
            {
              time: 2.7,
              color: "ffffffff",
            },
            {
              time: 3.0333,
              color: "ffffff00",
            },
          ],
        },
        Than_close: {
          attachment: [
            {
              time: 0.5333,
              name: "Than_open",
            },
          ],
        },
      },
      bones: {
        Anh_Sang: {
          translate: [
            {
              x: 7.44,
              y: -1.55,
            },
          ],
          scale: [
            {
              x: 0.97,
              y: 0.97,
            },
            {
              time: 0.5,
              y: 0.963,
            },
          ],
        },
        Bong: {
          translate: [
            {
              y: 0.01,
            },
          ],
          scale: [
            {
              x: 0.929,
              y: 0.929,
            },
            {
              time: 0.5,
              x: 0.868,
              y: 0.868,
              curve: [0.556, 0.926, 0.611, 1.041, 0.556, 0.926, 0.611, 1.041],
            },
            {
              time: 0.6667,
              x: 1.041,
              y: 1.041,
              curve: [0.756, 1.041, 0.844, 1, 0.756, 1.041, 0.844, 1],
            },
            {
              time: 0.9333,
            },
          ],
        },
        Bubble1: {
          translate: [
            {
              y: 32.35,
              curve: "stepped",
            },
            {
              time: 0.8333,
              y: 32.35,
            },
            {
              time: 1.6667,
            },
            {
              time: 1.6667,
            },
            {
              time: 2.5,
              y: 32.35,
            },
          ],
        },
        Bubble2: {
          translate: [
            {
              y: 32.35,
            },
            {
              time: 0.7,
            },
            {
              time: 1.5333,
              y: 32.35,
            },
            {
              time: 1.6667,
              y: 0.01,
            },
            {
              time: 1.6667,
              curve: "stepped",
            },
            {
              time: 2.3667,
            },
            {
              time: 3.2,
              y: 32.35,
            },
          ],
        },
        Bubble3: {
          translate: [
            {
              y: 32.35,
            },
            {
              time: 0.4333,
            },
            {
              time: 1.2667,
              y: 32.35,
            },
            {
              time: 1.6667,
            },
            {
              time: 1.6667,
              curve: "stepped",
            },
            {
              time: 2.1,
            },
            {
              time: 2.9333,
              y: 32.35,
            },
          ],
        },
        Bubble4: {
          translate: [
            {
              y: 32.35,
            },
            {
              time: 0.8333,
              curve: "stepped",
            },
            {
              time: 2.5,
            },
            {
              time: 3.3333,
              y: 32.35,
            },
          ],
        },
        Bubble5: {
          translate: [
            {
              y: 32.35,
            },
            {
              time: 0.2667,
            },
            {
              time: 1.0667,
              y: 32.35,
            },
            {
              time: 1.6667,
            },
            {
              time: 1.6667,
              curve: "stepped",
            },
            {
              time: 1.9333,
            },
            {
              time: 2.7333,
              y: 32.35,
            },
          ],
        },
        Bubble6: {
          translate: [
            {
              y: 32.35,
            },
            {
              time: 0.6667,
            },
            {
              time: 1.4667,
              y: 32.35,
            },
            {
              time: 1.6667,
              y: 0.01,
            },
            {
              time: 1.6667,
              curve: "stepped",
            },
            {
              time: 2.3333,
            },
            {
              time: 3.1333,
              y: 32.35,
            },
          ],
        },
        Bubble7: {
          translate: [
            {
              y: 32.35,
            },
            {
              time: 0.2333,
            },
            {
              time: 1.0667,
              y: 32.35,
            },
            {
              time: 1.6667,
            },
            {
              time: 1.6667,
              curve: "stepped",
            },
            {
              time: 1.9,
            },
            {
              time: 2.7333,
              y: 32.35,
            },
          ],
        },
        Nap: {
          rotate: [
            {
              value: 0.87,
            },
          ],
          translate: [
            {
              x: 2.37,
            },
            {
              time: 0.5,
              x: -3.16,
              curve: [0.556, -3.6, 0.611, -5.27, 0.556, 0, 0.611, 0],
            },
            {
              time: 0.6667,
              x: -5.27,
              curve: "stepped",
            },
            {
              time: 0.8,
              x: -5.27,
              curve: [0.889, -5.27, 0.978, 0.02, 0.889, 0, 0.978, 0],
            },
            {
              time: 1.0667,
              x: 0.02,
              curve: [1.267, 0.02, 1.133, 1.5, 1.267, 0, 1.133, 0],
            },
            {
              time: 1.3333,
              x: 1.5,
            },
          ],
          scale: [
            {
              x: 1.045,
            },
            {
              time: 0.5,
              x: 0.93,
              curve: "stepped",
            },
            {
              time: 0.5333,
              x: 0.93,
              curve: [0.556, 0.93, 0.578, 1.099, 0.556, 1, 0.578, 1],
            },
            {
              time: 0.6,
              x: 1.118,
              curve: [0.644, 1.155, 0.689, 1.155, 0.644, 1, 0.689, 1],
            },
            {
              time: 0.7333,
              x: 1.155,
              curve: [0.844, 1.155, 0.933, 0.676, 0.844, 1, 0.933, 1],
            },
            {
              time: 1.0667,
              x: 0.676,
              curve: [1.267, 0.676, 1.133, 1, 1.267, 1, 1.133, 1],
            },
            {
              time: 1.3333,
            },
          ],
        },
        Card: {
          rotate: [
            {
              value: 0.72,
            },
          ],
          translate: [
            {
              time: 0.5333,
              x: -15.18,
            },
            {
              time: 2.7667,
              x: -15.18,
              y: 6,
            },
          ],
          scale: [
            {
              time: 0.5333,
              x: -1,
              curve: "stepped",
            },
            {
              time: 2.4333,
              x: -1,
              curve: [2.533, -0.267, 2.633, 1.2, 2.533, 1.067, 2.633, 1.2],
            },
            {
              time: 2.7333,
              x: 1.2,
              y: 1.2,
              curve: [2.811, 1.2, 2.889, 0.85, 2.811, 1.2, 2.889, 0.85],
            },
            {
              time: 2.9667,
              x: 0.85,
              y: 0.85,
              curve: [3.044, 0.85, 3.122, 0.95, 3.044, 0.85, 3.122, 0.95],
            },
            {
              time: 3.2,
            },
          ],
        },
        Card2: {
          rotate: [
            {
              value: 0.72,
            },
          ],
          translate: [
            {
              time: 0.5333,
              x: -23.09,
              y: -61.58,
              curve: [
                0.556, -23.09, 0.578, -23.09, 0.556, -93.96, 0.578, -158.73,
              ],
            },
            {
              time: 0.6,
              x: -23.09,
              y: -158.73,
              curve: [
                0.622, -23.09, 0.644, 25.9, 0.622, -158.73, 0.644, -129.36,
              ],
            },
            {
              time: 0.6667,
              x: 25.9,
              y: -113.17,
              curve: [0.689, 25.9, 0.711, -23.09, 0.689, -96.98, 0.711, -61.58],
            },
            {
              time: 0.7333,
              x: -23.09,
              y: -61.58,
              curve: [
                0.756, -23.09, 0.778, -23.09, 0.756, -61.58, 0.778, -158.73,
              ],
            },
            {
              time: 0.8,
              x: -23.09,
              y: -158.73,
              curve: [
                0.822, -23.09, 0.844, 25.9, 0.822, -158.73, 0.844, -126.12,
              ],
            },
            {
              time: 0.8667,
              x: 25.9,
              y: -113.17,
              curve: [0.9, 25.9, 0.933, -23.09, 0.9, -93.74, 0.933, -61.58],
            },
            {
              time: 0.9667,
              x: -23.09,
              y: -61.58,
              curve: [1, -23.09, 1.033, -23.09, 1, -61.58, 1.033, -158.73],
            },
            {
              time: 1.0667,
              x: -23.09,
              y: -158.73,
              curve: [1.1, -23.09, 1.133, 25.9, 1.1, -158.73, 1.133, -129.36],
            },
            {
              time: 1.1667,
              x: 25.9,
              y: -113.17,
              curve: [1.2, 25.9, 1.233, -23.09, 1.2, -96.98, 1.233, -61.58],
            },
            {
              time: 1.2667,
              x: -23.09,
              y: -61.58,
              curve: [
                1.367, -23.09, 1.467, -18.78, 1.367, -61.58, 1.467, -96.01,
              ],
            },
            {
              time: 1.5667,
              x: -16.63,
              y: -113.22,
              curve: [1.6, -15.91, 1.633, -16.63, 1.6, -118.97, 1.633, -113.22],
            },
            {
              time: 1.6667,
              x: -16.63,
              y: -113.22,
            },
          ],
          scale: [
            {
              time: 1.7333,
              curve: [1.789, 1, 1.844, 1.2, 1.789, 1, 1.844, 1.2],
            },
            {
              time: 1.9,
              x: 1.2,
              y: 1.2,
              curve: [1.944, 1.2, 1.989, 1.067, 1.944, 1.2, 1.989, 1.067],
            },
            {
              time: 2.0333,
            },
          ],
          shear: [
            {
              time: 0.5333,
              x: -45,
              curve: [0.556, -15, 0.578, 45, 0.556, 0, 0.578, 0],
            },
            {
              time: 0.6,
              x: 45,
              curve: [0.622, 45, 0.644, 15, 0.622, 0, 0.644, 0],
            },
            {
              time: 0.6667,
              curve: [0.689, -15, 0.711, -45, 0.689, 0, 0.711, 0],
            },
            {
              time: 0.7333,
              x: -45,
              curve: [0.756, -45, 0.778, 45, 0.756, 0, 0.778, 0],
            },
            {
              time: 0.8,
              x: 45,
              curve: [0.822, 45, 0.844, 12, 0.822, 0, 0.844, 0],
            },
            {
              time: 0.8667,
              curve: [0.9, -18, 0.933, -45, 0.9, 0, 0.933, 0],
            },
            {
              time: 0.9667,
              x: -45,
              curve: [1, -45, 1.033, 45, 1, 0, 1.033, 0],
            },
            {
              time: 1.0667,
              x: 45,
              curve: [1.1, 45, 1.133, 15, 1.1, 0, 1.133, 0],
            },
            {
              time: 1.1667,
              curve: [1.2, -15, 1.233, -45, 1.2, 0, 1.233, 0],
            },
            {
              time: 1.2667,
              x: -45,
              curve: [1.367, -45, 1.467, -15, 1.367, 0, 1.467, 0],
            },
            {
              time: 1.5667,
              curve: [1.6, 5.01, 1.633, 0, 1.6, 0, 1.633, 0],
            },
            {
              time: 1.6667,
            },
          ],
        },
        Card3: {
          rotate: [
            {
              value: 0.72,
            },
          ],
          translate: [
            {
              time: 0.5333,
              x: -20.2,
              y: 71.17,
              curve: [0.556, -2.04, 0.578, 34.3, 0.556, 85.89, 0.578, 99.67],
            },
            {
              time: 0.6,
              x: 34.3,
              y: 115.32,
              curve: [0.622, 34.3, 0.644, -20.47, 0.622, 130.96, 0.644, 165.06],
            },
            {
              time: 0.6667,
              x: -20.47,
              y: 165.06,
              curve: [
                0.689, -20.47, 0.711, -20.47, 0.689, 165.06, 0.711, 71.17,
              ],
            },
            {
              time: 0.7333,
              x: -20.2,
              y: 71.17,
              curve: [0.756, -19.94, 0.778, 34.3, 0.756, 71.17, 0.778, 99.67],
            },
            {
              time: 0.8,
              x: 34.3,
              y: 115.32,
              curve: [0.822, 34.3, 0.844, -20.47, 0.822, 130.96, 0.844, 165.06],
            },
            {
              time: 0.8667,
              x: -20.47,
              y: 165.06,
              curve: [0.9, -20.47, 0.933, -20.47, 0.9, 165.06, 0.933, 71.17],
            },
            {
              time: 0.9667,
              x: -20.2,
              y: 71.17,
              curve: [1, -19.94, 1.033, 34.3, 1, 71.17, 1.033, 99.67],
            },
            {
              time: 1.0667,
              x: 34.3,
              y: 115.32,
              curve: [1.1, 34.3, 1.133, -20.47, 1.1, 130.96, 1.133, 165.06],
            },
            {
              time: 1.1667,
              x: -20.47,
              y: 165.06,
              curve: [1.2, -20.47, 1.233, -20.47, 1.2, 165.06, 1.233, 86.1],
            },
            {
              time: 1.2667,
              x: -20.2,
              y: 71.17,
              curve: [1.367, -19.41, 1.467, -16.11, 1.367, 26.38, 1.467, 14.3],
            },
            {
              time: 1.5667,
              x: -14.06,
              y: -14.13,
              curve: [1.6, -13.37, 1.633, -14.06, 1.6, -23.63, 1.633, -14.13],
            },
            {
              time: 1.6667,
              x: -14.06,
              y: -14.13,
            },
          ],
          scale: [
            {
              time: 1.9,
              curve: [1.956, 1, 2.011, 1.2, 1.956, 1, 2.011, 1.2],
            },
            {
              time: 2.0667,
              x: 1.2,
              y: 1.2,
              curve: [2.111, 1.2, 2.156, 1.067, 2.111, 1.2, 2.156, 1.067],
            },
            {
              time: 2.2,
            },
          ],
          shear: [
            {
              time: 0.5333,
              x: 45,
              curve: [0.556, 30, 0.578, 15, 0.556, 0, 0.578, 0],
            },
            {
              time: 0.6,
              curve: [0.622, -15, 0.644, -45, 0.622, 0, 0.644, 0],
            },
            {
              time: 0.6667,
              x: -45,
              curve: [0.689, -45, 0.711, 45, 0.689, 0, 0.711, 0],
            },
            {
              time: 0.7333,
              x: 45,
              curve: [0.756, 45, 0.778, 15, 0.756, 0, 0.778, 0],
            },
            {
              time: 0.8,
              curve: [0.822, -15, 0.844, -45, 0.822, 0, 0.844, 0],
            },
            {
              time: 0.8667,
              x: -45,
              curve: [0.9, -45, 0.933, 45, 0.9, 0, 0.933, 0],
            },
            {
              time: 0.9667,
              x: 45,
              curve: [1, 45, 1.033, 15, 1, 0, 1.033, 0],
            },
            {
              time: 1.0667,
              curve: [1.1, -15, 1.133, -45, 1.1, 0, 1.133, 0],
            },
            {
              time: 1.1667,
              x: -45,
              curve: [1.2, -45, 1.233, 45, 1.2, 0, 1.233, 0],
            },
            {
              time: 1.2667,
              x: 45,
              curve: [1.367, 45, 1.467, 15, 1.367, 0, 1.467, 0],
            },
            {
              time: 1.5667,
              curve: [1.6, -5.01, 1.633, 0, 1.6, 0, 1.633, 0],
            },
            {
              time: 1.6667,
            },
          ],
        },
        Than: {
          rotate: [
            {
              value: -0.5,
            },
            {
              time: 0.1,
              value: 8.29,
              curve: [0.133, 8.29, 0.167, -11.89],
            },
            {
              time: 0.2,
              value: -11.89,
              curve: [0.233, -11.89, 0.267, 4.18],
            },
            {
              time: 0.3,
              value: 4.18,
              curve: [0.333, 4.18, 0.367, -4.51],
            },
            {
              time: 0.4,
              value: -4.51,
              curve: [0.433, -4.51, 0.467, 0.45],
            },
            {
              time: 0.5,
              value: 0.45,
              curve: "stepped",
            },
            {
              time: 0.8333,
              value: 0.45,
              curve: [0.967, 0.45, 1.1, 0],
            },
            {
              time: 1.2333,
            },
          ],
          translate: [
            {
              y: -0.6,
            },
            {
              time: 0.1,
              y: 11.31,
              curve: [0.111, 0, 0.122, 0, 0.111, 11.31, 0.122, 5.82],
            },
            {
              time: 0.1333,
              y: 6.5,
              curve: [0.156, 0, 0.178, 0, 0.156, 7.86, 0.178, 15.75],
            },
            {
              time: 0.2,
              y: 14.66,
              curve: [0.223, 0, 0.245, 0, 0.223, 13.57, 0.245, 7.4],
            },
            {
              time: 0.2667,
              y: 5.7,
              curve: [0.278, 0, 0.289, 0, 0.278, 4.85, 0.289, 5.06],
            },
            {
              time: 0.3,
              y: 4.82,
              curve: [0.322, 0, 0.344, 0, 0.322, 4.32, 0.344, 3.26],
            },
            {
              time: 0.3667,
              y: 3.47,
              curve: [0.378, 0, 0.389, 0, 0.378, 3.58, 0.389, 6],
            },
            {
              time: 0.4,
              y: 5.53,
              curve: [0.433, 0, 0.467, 0, 0.433, 4.14, 0.467, 0.59],
            },
            {
              time: 0.5,
              y: 0.59,
              curve: [0.533, 0, 0.567, 0, 0.533, 0.59, 0.567, 10.49],
            },
            {
              time: 0.6,
              y: 14.55,
              curve: [0.633, 0, 0.667, 0, 0.633, 18.6, 0.667, 24.92],
            },
            {
              time: 0.7,
              y: 24.92,
              curve: [0.744, 0, 0.789, 0, 0.744, 24.92, 0.789, 22.06],
            },
            {
              time: 0.8333,
              y: 17.38,
              curve: [0.867, 0, 0.9, 0, 0.867, 13.87, 0.9, 0.79],
            },
            {
              time: 0.9333,
              y: 0.35,
              curve: [1.033, 0, 1.133, 0.15, 1.033, -0.95, 1.133, -0.95],
            },
            {
              time: 1.2333,
              x: 0.15,
              y: -0.95,
            },
          ],
          scale: [
            {},
            {
              time: 0.5,
              x: 0.735,
              curve: [0.533, 0.735, 0.567, 1.2, 0.533, 1, 0.567, 1],
            },
            {
              time: 0.6,
              x: 1.2,
              curve: [0.678, 1.2, 0.756, 1.146, 0.678, 1, 0.756, 1],
            },
            {
              time: 0.8333,
              x: 1.118,
              curve: [0.867, 1.106, 0.9, 1.118, 0.867, 1, 0.9, 1],
            },
            {
              time: 0.9333,
              x: 1.08,
              curve: [0.967, 1.043, 1, 0.84, 0.967, 1, 1, 1],
            },
            {
              time: 1.0333,
              x: 0.84,
              curve: [1.1, 0.84, 1.167, 0.982, 1.1, 1, 1.167, 1],
            },
            {
              time: 1.2333,
            },
          ],
        },
        Sparkle1: {
          rotate: [
            {
              value: -6.03,
            },
            {
              time: 0.5,
              value: 12.94,
            },
            {
              time: 1.4,
              value: -6.03,
            },
            {
              time: 1.6667,
              value: 4.09,
            },
            {
              time: 1.9,
              value: 12.94,
            },
            {
              time: 2.8,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: -30.48,
              y: 217.9,
            },
          ],
        },
        Sparkle2: {
          rotate: [
            {
              value: -6.03,
            },
            {
              time: 1.6667,
            },
            {
              time: 2,
              value: 12.94,
            },
            {
              time: 2.9,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: -129.03,
              y: 259.03,
            },
          ],
        },
        Sparkle3: {
          rotate: [
            {
              value: -6.03,
            },
            {
              time: 1.0333,
              value: 12.94,
            },
            {
              time: 1.6667,
              value: -2.73,
            },
            {
              time: 1.8,
              value: -6.03,
            },
            {
              time: 2.1667,
            },
            {
              time: 2.5,
              value: 12.94,
            },
            {
              time: 3.2667,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: 7.33,
              y: 48.22,
            },
          ],
        },
        Sparkle4: {
          rotate: [
            {
              value: -6.03,
            },
            {
              time: 1,
              value: 12.94,
            },
            {
              time: 1.6667,
            },
            {
              time: 1.6667,
            },
            {
              time: 1.8,
              value: -6.03,
            },
            {
              time: 2.2333,
              value: 12.94,
            },
            {
              time: 3.0333,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: 177.32,
              y: 116.05,
            },
            {
              time: 1.6667,
              x: 177.32,
              y: 116.05,
            },
          ],
        },
        Card_Fly: {
          translate: [
            {
              time: 0.5,
              x: 0.15,
              curve: [0.511, 0.15, 0.522, 0.15, 0.511, 20.84, 0.522, 51.84],
            },
            {
              time: 0.5333,
              x: 0.15,
              y: 62.53,
              curve: [0.633, 0.15, 0.633, 4.15, 0.633, 158.69, 0.633, 234.54],
            },
            {
              time: 0.7333,
              x: 6.15,
              y: 320.55,
            },
          ],
          scale: [
            {
              time: 0.5,
              x: 0.5,
              y: 0.5,
              curve: [0.611, 0.667, 0.622, 0.833, 0.611, 0.667, 0.622, 0.833],
            },
            {
              time: 0.7333,
              curve: [0.766, 1.048, 0.893, 1.068, 0.766, 1.048, 0.893, -1.077],
            },
            {
              time: 0.9667,
              x: 1.072,
              y: -1.072,
              curve: [1.067, 1.078, 1.1, 1, 1.067, -1.066, 1.1, 1],
            },
            {
              time: 1.2,
              curve: [1.311, 1, 1.322, 1.072, 1.311, 1, 1.322, -1.072],
            },
            {
              time: 1.4333,
              x: 1.072,
              y: -1.072,
              curve: [1.544, 1.072, 1.556, 1, 1.544, -1.072, 1.556, 1],
            },
            {
              time: 1.6667,
              curve: [1.744, 1, 1.789, 1.072, 1.744, 1, 1.789, -1.072],
            },
            {
              time: 1.9,
              x: 1.072,
              y: -1.072,
              curve: [2.011, 1.072, 2.022, 1, 2.011, -1.072, 2.022, 1],
            },
            {
              time: 2.1333,
              curve: [2.211, 1, 2.256, 1.072, 2.211, 1, 2.256, -1.072],
            },
            {
              time: 2.3667,
              x: 1.072,
              y: -1.072,
              curve: [2.478, 1.072, 2.489, 1, 2.478, -1.072, 2.489, 1],
            },
            {
              time: 2.6,
            },
          ],
        },
        bone: {
          translate: [
            {
              x: -0.15,
              y: -0.55,
            },
          ],
        },
      },
      deform: {
        default: {
          Mieng_open: {
            Mieng_open: [
              {
                time: 1.2333,
                offset: 1,
                vertices: [
                  -0.03851, 0.00952, -0.03851, 0.00952, -0.03851, 0.00952,
                  -0.03851,
                ],
              },
            ],
          },
          Nap_close: {
            Nap_open: [
              {
                time: 1.3333,
                vertices: [
                  -0.47011, -0.01443, -0.47011, -0.01443, -0.47011, -0.01443,
                  -0.47011, -0.01443,
                ],
              },
            ],
            Nap_close: [
              {
                vertices: [
                  3.27547, 1.9277, 3.8477, 1.8578, 3.81247, 1.58382, 3.23745,
                  1.63179,
                ],
              },
              {
                time: 1.6667,
              },
            ],
          },
          Than_close: {
            Than_close: [
              {
                offset: 12,
                vertices: [8.10991, 0.00001, 8.10991],
              },
            ],
          },
        },
      },
      drawOrder: [
        {
          time: 0.5667,
          offsets: [
            {
              slot: "Golden3",
              offset: -2,
            },
          ],
        },
        {
          time: 0.6333,
          offsets: [
            {
              slot: "Golden",
              offset: 2,
            },
          ],
        },
        {
          time: 0.7,
        },
        {
          time: 0.7667,
          offsets: [
            {
              slot: "Golden3",
              offset: -2,
            },
          ],
        },
        {
          time: 0.8333,
          offsets: [
            {
              slot: "Golden",
              offset: 2,
            },
          ],
        },
        {
          time: 0.9,
        },
        {
          time: 1,
          offsets: [
            {
              slot: "Golden3",
              offset: -2,
            },
          ],
        },
        {
          time: 1.1333,
          offsets: [
            {
              slot: "Golden",
              offset: 2,
            },
          ],
        },
        {
          time: 1.2333,
        },
        {
          time: 1.2667,
          offsets: [
            {
              slot: "Golden3",
              offset: -2,
            },
          ],
        },
      ],
    },
    Open_Flip_Cards_1: {
      slots: {
        Anh_sang_close: {
          attachment: [
            {
              name: null,
            },
          ],
        },
        Bubble: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.4,
              color: "ffffffff",
            },
            {
              time: 0.7,
              color: "ffffff00",
            },
          ],
        },
        Bubble2: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.5667,
              color: "ffffff00",
            },
            {
              time: 1,
              color: "ffffffff",
            },
            {
              time: 1.4,
              color: "ffffff00",
            },
          ],
        },
        Bubble3: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.3,
              color: "ffffff00",
            },
            {
              time: 0.7333,
              color: "ffffffff",
            },
            {
              time: 1.1333,
              color: "ffffff00",
            },
          ],
        },
        Bubble4: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.7,
              color: "ffffff00",
            },
            {
              time: 1.1333,
              color: "ffffffff",
            },
            {
              time: 1.5333,
              color: "ffffff00",
            },
          ],
        },
        Bubble5: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.1333,
              color: "ffffff00",
            },
            {
              time: 0.5333,
              color: "ffffffff",
            },
            {
              time: 0.9333,
              color: "ffffff00",
            },
          ],
        },
        Bubble6: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.5333,
              color: "ffffff00",
            },
            {
              time: 0.9333,
              color: "ffffffff",
            },
            {
              time: 1.3333,
              color: "ffffff00",
            },
          ],
        },
        Bubble7: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.1,
              color: "ffffff00",
            },
            {
              time: 0.5333,
              color: "ffffffff",
            },
            {
              time: 0.9333,
              color: "ffffff00",
            },
          ],
        },
        "Frame 2071857771": {
          attachment: [
            {
              name: null,
            },
            {
              time: 0.5,
              name: "Frame 2071857771",
            },
          ],
        },
        "Frame 2071857772": {
          attachment: [
            {
              name: null,
            },
          ],
        },
        Glow1: {
          rgba: [
            {
              color: "ffffff3e",
            },
            {
              time: 0.6333,
              color: "ffffffff",
            },
            {
              time: 1.2,
              color: "ffffffc0",
            },
            {
              time: 1.6667,
              color: "ffffff26",
            },
          ],
        },
        Glow2: {
          rgba: [
            {
              color: "ffffff6a",
            },
            {
              time: 0.5,
              color: "ffffffff",
              curve: "stepped",
            },
            {
              time: 0.8333,
              color: "ffffffff",
            },
            {
              time: 1.3667,
              color: "ffffff7b",
            },
          ],
        },
        Glow3: {
          rgba: [
            {
              color: "ffffffff",
              curve: "stepped",
            },
            {
              time: 0.4,
              color: "ffffffed",
            },
            {
              time: 1.0667,
              color: "ffffff8a",
            },
            {
              time: 1.6333,
              color: "ffffffff",
            },
          ],
        },
        Golden: {
          attachment: [
            {
              time: 0.3667,
              name: null,
            },
          ],
        },
        Nap_close: {
          attachment: [
            {
              name: "Nap_open",
            },
          ],
        },
        Sparkles: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.1,
              color: "ffffff00",
            },
            {
              time: 0.6667,
              color: "ffffffff",
            },
            {
              time: 1,
              color: "ffffff00",
            },
          ],
        },
        Sparkles2: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.5333,
              color: "ffffff00",
            },
            {
              time: 1.0333,
              color: "ffffffff",
            },
            {
              time: 1.4333,
              color: "ffffff00",
            },
          ],
        },
        Sparkles3: {
          rgba: [
            {
              color: "ffffff5c",
            },
            {
              time: 0.3667,
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.9,
              color: "ffffff00",
            },
            {
              time: 1.3,
              color: "ffffffff",
            },
            {
              time: 1.6667,
              color: "ffffff00",
            },
          ],
        },
        Sparkles4: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.7,
              color: "ffffff00",
            },
            {
              time: 1.1667,
              color: "ffffffff",
            },
            {
              time: 1.5,
              color: "ffffff00",
            },
          ],
        },
        Than_close: {
          attachment: [
            {
              name: "Than_open",
            },
          ],
        },
      },
      bones: {
        Anh_Sang: {
          scale: [
            {
              y: 0.963,
            },
          ],
        },
        Bong: {
          translate: [
            {
              y: 0.01,
            },
          ],
        },
        Bubble1: {
          translate: [
            {},
            {
              time: 0.7,
              y: 32.35,
            },
          ],
        },
        Bubble2: {
          translate: [
            {
              y: 0.01,
            },
            {
              time: 0.5667,
            },
            {
              time: 1.4,
              y: 32.35,
            },
          ],
        },
        Bubble3: {
          translate: [
            {},
            {
              time: 0.3,
            },
            {
              time: 1.1333,
              y: 32.35,
            },
          ],
        },
        Bubble4: {
          translate: [
            {
              time: 0.7,
            },
            {
              time: 1.5333,
              y: 32.35,
            },
          ],
        },
        Bubble5: {
          translate: [
            {},
            {
              time: 0.1333,
            },
            {
              time: 0.9333,
              y: 32.35,
            },
          ],
        },
        Bubble6: {
          translate: [
            {
              y: 0.01,
            },
            {
              time: 0.5333,
            },
            {
              time: 1.3333,
              y: 32.35,
            },
          ],
        },
        Bubble7: {
          translate: [
            {},
            {
              time: 0.1,
            },
            {
              time: 0.9333,
              y: 32.35,
            },
          ],
        },
        Nap: {
          translate: [
            {
              x: 1.5,
            },
          ],
        },
        Card: {
          rotate: [
            {
              value: 0.72,
            },
          ],
          translate: [
            {
              x: -15.18,
              y: 6,
            },
          ],
          scale: [
            {
              curve: [0.056, 1.133, 0.139, 1.4, 0.056, 1.133, 0.111, 1.267],
            },
            {
              time: 0.1667,
              x: 1.2,
              y: 1.2,
              curve: [0.222, 0.8, 0.278, 0.4, 0.222, 1.133, 0.278, 1.067],
            },
            {
              time: 0.3333,
              x: 0,
            },
            {
              time: 0.5,
            },
          ],
        },
        Card2: {
          rotate: [
            {
              value: 0.72,
            },
          ],
          translate: [
            {
              x: -16.63,
              y: -107.22,
            },
            {
              time: 1.0667,
              x: -16.75,
              y: -107.22,
            },
          ],
          scale: [
            {
              time: 0.1333,
              curve: [0.189, 1.067, 0.244, 1.2, 0.189, 1.067, 0.244, 1.2],
            },
            {
              time: 0.3,
              x: 1.2,
              y: 1.2,
              curve: [0.356, 1.2, 0.411, 0.4, 0.356, 1.2, 0.411, 1.067],
            },
            {
              time: 0.4667,
              x: 0,
            },
            {
              time: 0.6333,
            },
          ],
        },
        Card3: {
          rotate: [
            {
              value: 0.72,
            },
          ],
          translate: [
            {
              x: -14.06,
              y: -14.13,
            },
          ],
          scale: [
            {
              time: 0.4,
              curve: [0.456, 1, 0.511, 1.2, 0.456, 1, 0.511, 1.2],
            },
            {
              time: 0.5667,
              x: 1.2,
              y: 1.2,
              curve: [0.622, 1.2, 0.678, 0.4, 0.622, 1.2, 0.678, 1.067],
            },
            {
              time: 0.7333,
              x: 0,
            },
            {
              time: 0.9,
            },
          ],
        },
        Than: {
          translate: [
            {
              x: 0.15,
              y: -0.95,
            },
          ],
        },
        Sparkle1: {
          rotate: [
            {
              value: 4.09,
            },
            {
              time: 0.1,
              value: 12.94,
            },
            {
              time: 1,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: -30.48,
              y: 217.9,
            },
          ],
        },
        Sparkle2: {
          rotate: [
            {},
            {
              time: 0.5333,
              value: 12.94,
            },
            {
              time: 1.4333,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: -129.03,
              y: 259.03,
            },
          ],
        },
        Sparkle3: {
          rotate: [
            {
              value: -2.73,
            },
            {
              time: 0.3667,
            },
            {
              time: 0.9,
              value: 12.94,
            },
            {
              time: 1.6667,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: 7.33,
              y: 48.22,
            },
          ],
        },
        Sparkle4: {
          rotate: [
            {},
            {
              time: 0.7,
              value: 12.94,
            },
            {
              time: 1.5,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: 177.32,
              y: 116.05,
            },
          ],
        },
        Card_Fly: {
          translate: [
            {
              x: 6.15,
              y: 320.55,
            },
          ],
        },
        bone: {
          translate: [
            {
              x: -0.15,
              y: -0.55,
            },
          ],
        },
      },
      deform: {
        default: {
          Mieng_open: {
            Mieng_open: [
              {
                offset: 1,
                vertices: [
                  -0.03851, 0.00952, -0.03851, 0.00952, -0.03851, 0.00952,
                  -0.03851,
                ],
              },
            ],
          },
          Nap_close: {
            Nap_open: [
              {
                vertices: [
                  -0.47011, -0.01443, -0.47011, -0.01443, -0.47011, -0.01443,
                  -0.47011, -0.01443,
                ],
              },
            ],
          },
        },
      },
      drawOrder: [
        {
          offsets: [
            {
              slot: "Golden3",
              offset: -2,
            },
          ],
        },
      ],
    },
    Open_Flip_Cards_2: {
      slots: {
        Anh_sang_close: {
          attachment: [
            {
              name: null,
            },
          ],
        },
        Bubble: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.4,
              color: "ffffffff",
            },
            {
              time: 0.7,
              color: "ffffff00",
            },
          ],
        },
        Bubble2: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.5667,
              color: "ffffff00",
            },
            {
              time: 1,
              color: "ffffffff",
            },
            {
              time: 1.4,
              color: "ffffff00",
            },
          ],
        },
        Bubble3: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.3,
              color: "ffffff00",
            },
            {
              time: 0.7333,
              color: "ffffffff",
            },
            {
              time: 1.1333,
              color: "ffffff00",
            },
          ],
        },
        Bubble4: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.7,
              color: "ffffff00",
            },
            {
              time: 1.1333,
              color: "ffffffff",
            },
            {
              time: 1.5333,
              color: "ffffff00",
            },
          ],
        },
        Bubble5: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.1333,
              color: "ffffff00",
            },
            {
              time: 0.5333,
              color: "ffffffff",
            },
            {
              time: 0.9333,
              color: "ffffff00",
            },
          ],
        },
        Bubble6: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.5333,
              color: "ffffff00",
            },
            {
              time: 0.9333,
              color: "ffffffff",
            },
            {
              time: 1.3333,
              color: "ffffff00",
            },
          ],
        },
        Bubble7: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.1,
              color: "ffffff00",
            },
            {
              time: 0.5333,
              color: "ffffffff",
            },
            {
              time: 0.9333,
              color: "ffffff00",
            },
          ],
        },
        "Frame 2071857771": {
          attachment: [
            {
              name: null,
            },
            {
              time: 0.5,
              name: "Frame 2071857772",
            },
          ],
        },
        "Frame 2071857772": {
          attachment: [
            {
              name: null,
            },
          ],
        },
        Glow1: {
          rgba: [
            {
              color: "ffffff3e",
            },
            {
              time: 0.6333,
              color: "ffffffff",
            },
            {
              time: 1.2,
              color: "ffffffc0",
            },
            {
              time: 1.6667,
              color: "ffffff26",
            },
          ],
        },
        Glow2: {
          rgba: [
            {
              color: "ffffff6a",
            },
            {
              time: 0.5,
              color: "ffffffff",
              curve: "stepped",
            },
            {
              time: 0.8333,
              color: "ffffffff",
            },
            {
              time: 1.3667,
              color: "ffffff7b",
            },
          ],
        },
        Glow3: {
          rgba: [
            {
              color: "ffffffff",
              curve: "stepped",
            },
            {
              time: 0.4,
              color: "ffffffed",
            },
            {
              time: 1.0667,
              color: "ffffff8a",
            },
            {
              time: 1.6333,
              color: "ffffffff",
            },
          ],
        },
        Golden: {
          attachment: [
            {
              time: 0.3667,
              name: null,
            },
          ],
        },
        Nap_close: {
          attachment: [
            {
              name: "Nap_open",
            },
          ],
        },
        Sparkles: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.1,
              color: "ffffff00",
            },
            {
              time: 0.6667,
              color: "ffffffff",
            },
            {
              time: 1,
              color: "ffffff00",
            },
          ],
        },
        Sparkles2: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.5333,
              color: "ffffff00",
            },
            {
              time: 1.0333,
              color: "ffffffff",
            },
            {
              time: 1.4333,
              color: "ffffff00",
            },
          ],
        },
        Sparkles3: {
          rgba: [
            {
              color: "ffffff5c",
            },
            {
              time: 0.3667,
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.9,
              color: "ffffff00",
            },
            {
              time: 1.3,
              color: "ffffffff",
            },
            {
              time: 1.6667,
              color: "ffffff00",
            },
          ],
        },
        Sparkles4: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.7,
              color: "ffffff00",
            },
            {
              time: 1.1667,
              color: "ffffffff",
            },
            {
              time: 1.5,
              color: "ffffff00",
            },
          ],
        },
        Than_close: {
          attachment: [
            {
              name: "Than_open",
            },
          ],
        },
      },
      bones: {
        Anh_Sang: {
          scale: [
            {
              y: 0.963,
            },
          ],
        },
        Bong: {
          translate: [
            {
              y: 0.01,
            },
          ],
        },
        Bubble1: {
          translate: [
            {},
            {
              time: 0.7,
              y: 32.35,
            },
          ],
        },
        Bubble2: {
          translate: [
            {
              y: 0.01,
            },
            {
              time: 0.5667,
            },
            {
              time: 1.4,
              y: 32.35,
            },
          ],
        },
        Bubble3: {
          translate: [
            {},
            {
              time: 0.3,
            },
            {
              time: 1.1333,
              y: 32.35,
            },
          ],
        },
        Bubble4: {
          translate: [
            {
              time: 0.7,
            },
            {
              time: 1.5333,
              y: 32.35,
            },
          ],
        },
        Bubble5: {
          translate: [
            {},
            {
              time: 0.1333,
            },
            {
              time: 0.9333,
              y: 32.35,
            },
          ],
        },
        Bubble6: {
          translate: [
            {
              y: 0.01,
            },
            {
              time: 0.5333,
            },
            {
              time: 1.3333,
              y: 32.35,
            },
          ],
        },
        Bubble7: {
          translate: [
            {},
            {
              time: 0.1,
            },
            {
              time: 0.9333,
              y: 32.35,
            },
          ],
        },
        Nap: {
          translate: [
            {
              x: 1.5,
            },
          ],
        },
        Card: {
          rotate: [
            {
              value: 0.72,
            },
          ],
          translate: [
            {
              x: -15.18,
              y: 6,
            },
          ],
          scale: [
            {
              curve: [0.056, 1.133, 0.139, 1.4, 0.056, 1.133, 0.111, 1.267],
            },
            {
              time: 0.1667,
              x: 1.2,
              y: 1.2,
              curve: [0.222, 0.8, 0.278, 0.4, 0.222, 1.133, 0.278, 1.067],
            },
            {
              time: 0.3333,
              x: 0,
            },
            {
              time: 0.5,
            },
          ],
        },
        Card2: {
          rotate: [
            {
              value: 0.72,
            },
          ],
          translate: [
            {
              x: -16.63,
              y: -107.22,
            },
          ],
          scale: [
            {
              time: 0.1333,
              curve: [0.189, 1.067, 0.244, 1.2, 0.189, 1.067, 0.244, 1.2],
            },
            {
              time: 0.3,
              x: 1.2,
              y: 1.2,
              curve: [0.356, 1.2, 0.411, 0.4, 0.356, 1.2, 0.411, 1.067],
            },
            {
              time: 0.4667,
              x: 0,
            },
            {
              time: 0.6333,
            },
          ],
        },
        Card3: {
          rotate: [
            {
              value: 0.72,
            },
          ],
          translate: [
            {
              x: -14.06,
              y: -14.13,
            },
          ],
          scale: [
            {
              time: 0.4,
              curve: [0.456, 1, 0.511, 1.2, 0.456, 1, 0.511, 1.2],
            },
            {
              time: 0.5667,
              x: 1.2,
              y: 1.2,
              curve: [0.622, 1.2, 0.678, 0.4, 0.622, 1.2, 0.678, 1.067],
            },
            {
              time: 0.7333,
              x: 0,
            },
            {
              time: 0.9,
            },
          ],
        },
        Than: {
          translate: [
            {
              x: 0.15,
              y: -0.95,
            },
          ],
        },
        Sparkle1: {
          rotate: [
            {
              value: 4.09,
            },
            {
              time: 0.1,
              value: 12.94,
            },
            {
              time: 1,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: -30.48,
              y: 217.9,
            },
          ],
        },
        Sparkle2: {
          rotate: [
            {},
            {
              time: 0.5333,
              value: 12.94,
            },
            {
              time: 1.4333,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: -129.03,
              y: 259.03,
            },
          ],
        },
        Sparkle3: {
          rotate: [
            {
              value: -2.73,
            },
            {
              time: 0.3667,
            },
            {
              time: 0.9,
              value: 12.94,
            },
            {
              time: 1.6667,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: 7.33,
              y: 48.22,
            },
          ],
        },
        Sparkle4: {
          rotate: [
            {},
            {
              time: 0.7,
              value: 12.94,
            },
            {
              time: 1.5,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: 177.32,
              y: 116.05,
            },
          ],
        },
        Card_Fly: {
          translate: [
            {
              x: 6.15,
              y: 320.55,
            },
          ],
        },
        bone: {
          translate: [
            {
              x: -0.15,
              y: -0.55,
            },
          ],
        },
      },
      deform: {
        default: {
          Mieng_open: {
            Mieng_open: [
              {
                offset: 1,
                vertices: [
                  -0.03851, 0.00952, -0.03851, 0.00952, -0.03851, 0.00952,
                  -0.03851,
                ],
              },
            ],
          },
          Nap_close: {
            Nap_open: [
              {
                vertices: [
                  -0.47011, -0.01443, -0.47011, -0.01443, -0.47011, -0.01443,
                  -0.47011, -0.01443,
                ],
              },
            ],
          },
        },
      },
      drawOrder: [
        {
          offsets: [
            {
              slot: "Golden3",
              offset: -2,
            },
          ],
        },
      ],
    },
    Open_Flip_Cards_3: {
      slots: {
        Anh_sang_close: {
          attachment: [
            {
              name: null,
            },
          ],
        },
        Bubble: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.4,
              color: "ffffffff",
            },
            {
              time: 0.7,
              color: "ffffff00",
            },
          ],
        },
        Bubble2: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.5667,
              color: "ffffff00",
            },
            {
              time: 1,
              color: "ffffffff",
            },
            {
              time: 1.4,
              color: "ffffff00",
            },
          ],
        },
        Bubble3: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.3,
              color: "ffffff00",
            },
            {
              time: 0.7333,
              color: "ffffffff",
            },
            {
              time: 1.1333,
              color: "ffffff00",
            },
          ],
        },
        Bubble4: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.7,
              color: "ffffff00",
            },
            {
              time: 1.1333,
              color: "ffffffff",
            },
            {
              time: 1.5333,
              color: "ffffff00",
            },
          ],
        },
        Bubble5: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.1333,
              color: "ffffff00",
            },
            {
              time: 0.5333,
              color: "ffffffff",
            },
            {
              time: 0.9333,
              color: "ffffff00",
            },
          ],
        },
        Bubble6: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.5333,
              color: "ffffff00",
            },
            {
              time: 0.9333,
              color: "ffffffff",
            },
            {
              time: 1.3333,
              color: "ffffff00",
            },
          ],
        },
        Bubble7: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.1,
              color: "ffffff00",
            },
            {
              time: 0.5333,
              color: "ffffffff",
            },
            {
              time: 0.9333,
              color: "ffffff00",
            },
          ],
        },
        "Frame 2071857771": {
          attachment: [
            {
              name: null,
            },
            {
              time: 0.5,
              name: "Frame 2071857773",
            },
          ],
        },
        "Frame 2071857772": {
          attachment: [
            {
              name: null,
            },
          ],
        },
        Glow1: {
          rgba: [
            {
              color: "ffffff3e",
            },
            {
              time: 0.6333,
              color: "ffffffff",
            },
            {
              time: 1.2,
              color: "ffffffc0",
            },
            {
              time: 1.6667,
              color: "ffffff26",
            },
          ],
        },
        Glow2: {
          rgba: [
            {
              color: "ffffff6a",
            },
            {
              time: 0.5,
              color: "ffffffff",
              curve: "stepped",
            },
            {
              time: 0.8333,
              color: "ffffffff",
            },
            {
              time: 1.3667,
              color: "ffffff7b",
            },
          ],
        },
        Glow3: {
          rgba: [
            {
              color: "ffffffff",
              curve: "stepped",
            },
            {
              time: 0.4,
              color: "ffffffed",
            },
            {
              time: 1.0667,
              color: "ffffff8a",
            },
            {
              time: 1.6333,
              color: "ffffffff",
            },
          ],
        },
        Golden: {
          attachment: [
            {
              time: 0.3667,
              name: null,
            },
          ],
        },
        Nap_close: {
          attachment: [
            {
              name: "Nap_open",
            },
          ],
        },
        Sparkles: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.1,
              color: "ffffff00",
            },
            {
              time: 0.6667,
              color: "ffffffff",
            },
            {
              time: 1,
              color: "ffffff00",
            },
          ],
        },
        Sparkles2: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.5333,
              color: "ffffff00",
            },
            {
              time: 1.0333,
              color: "ffffffff",
            },
            {
              time: 1.4333,
              color: "ffffff00",
            },
          ],
        },
        Sparkles3: {
          rgba: [
            {
              color: "ffffff5c",
            },
            {
              time: 0.3667,
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.9,
              color: "ffffff00",
            },
            {
              time: 1.3,
              color: "ffffffff",
            },
            {
              time: 1.6667,
              color: "ffffff00",
            },
          ],
        },
        Sparkles4: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.7,
              color: "ffffff00",
            },
            {
              time: 1.1667,
              color: "ffffffff",
            },
            {
              time: 1.5,
              color: "ffffff00",
            },
          ],
        },
        Than_close: {
          attachment: [
            {
              name: "Than_open",
            },
          ],
        },
      },
      bones: {
        Anh_Sang: {
          scale: [
            {
              y: 0.963,
            },
          ],
        },
        Bong: {
          translate: [
            {
              y: 0.01,
            },
          ],
        },
        Bubble1: {
          translate: [
            {},
            {
              time: 0.7,
              y: 32.35,
            },
          ],
        },
        Bubble2: {
          translate: [
            {
              y: 0.01,
            },
            {
              time: 0.5667,
            },
            {
              time: 1.4,
              y: 32.35,
            },
          ],
        },
        Bubble3: {
          translate: [
            {},
            {
              time: 0.3,
            },
            {
              time: 1.1333,
              y: 32.35,
            },
          ],
        },
        Bubble4: {
          translate: [
            {
              time: 0.7,
            },
            {
              time: 1.5333,
              y: 32.35,
            },
          ],
        },
        Bubble5: {
          translate: [
            {},
            {
              time: 0.1333,
            },
            {
              time: 0.9333,
              y: 32.35,
            },
          ],
        },
        Bubble6: {
          translate: [
            {
              y: 0.01,
            },
            {
              time: 0.5333,
            },
            {
              time: 1.3333,
              y: 32.35,
            },
          ],
        },
        Bubble7: {
          translate: [
            {},
            {
              time: 0.1,
            },
            {
              time: 0.9333,
              y: 32.35,
            },
          ],
        },
        Nap: {
          translate: [
            {
              x: 1.5,
            },
          ],
        },
        Card: {
          rotate: [
            {
              value: 0.72,
            },
          ],
          translate: [
            {
              x: -15.18,
              y: 6,
            },
          ],
          scale: [
            {
              curve: [0.056, 1.133, 0.139, 1.4, 0.056, 1.133, 0.111, 1.267],
            },
            {
              time: 0.1667,
              x: 1.2,
              y: 1.2,
              curve: [0.222, 0.8, 0.278, 0.4, 0.222, 1.133, 0.278, 1.067],
            },
            {
              time: 0.3333,
              x: 0,
            },
            {
              time: 0.5,
            },
          ],
        },
        Card2: {
          rotate: [
            {
              value: 0.72,
            },
          ],
          translate: [
            {
              x: -16.63,
              y: -107.22,
            },
          ],
          scale: [
            {
              time: 0.1333,
              curve: [0.189, 1.067, 0.244, 1.2, 0.189, 1.067, 0.244, 1.2],
            },
            {
              time: 0.3,
              x: 1.2,
              y: 1.2,
              curve: [0.356, 1.2, 0.411, 0.4, 0.356, 1.2, 0.411, 1.067],
            },
            {
              time: 0.4667,
              x: 0,
            },
            {
              time: 0.6333,
            },
          ],
        },
        Card3: {
          rotate: [
            {
              value: 0.72,
            },
          ],
          translate: [
            {
              x: -14.06,
              y: -14.13,
            },
          ],
          scale: [
            {
              time: 0.4,
              curve: [0.456, 1, 0.511, 1.2, 0.456, 1, 0.511, 1.2],
            },
            {
              time: 0.5667,
              x: 1.2,
              y: 1.2,
              curve: [0.622, 1.2, 0.678, 0.4, 0.622, 1.2, 0.678, 1.067],
            },
            {
              time: 0.7333,
              x: 0,
            },
            {
              time: 0.9,
            },
          ],
        },
        Than: {
          translate: [
            {
              x: 0.15,
              y: -0.95,
            },
          ],
        },
        Sparkle1: {
          rotate: [
            {
              value: 4.09,
            },
            {
              time: 0.1,
              value: 12.94,
            },
            {
              time: 1,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: -30.48,
              y: 217.9,
            },
          ],
        },
        Sparkle2: {
          rotate: [
            {},
            {
              time: 0.5333,
              value: 12.94,
            },
            {
              time: 1.4333,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: -129.03,
              y: 259.03,
            },
          ],
        },
        Sparkle3: {
          rotate: [
            {
              value: -2.73,
            },
            {
              time: 0.3667,
            },
            {
              time: 0.9,
              value: 12.94,
            },
            {
              time: 1.6667,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: 7.33,
              y: 48.22,
            },
          ],
        },
        Sparkle4: {
          rotate: [
            {},
            {
              time: 0.7,
              value: 12.94,
            },
            {
              time: 1.5,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: 177.32,
              y: 116.05,
            },
          ],
        },
        Card_Fly: {
          translate: [
            {
              x: 6.15,
              y: 320.55,
            },
          ],
        },
        bone: {
          translate: [
            {
              x: -0.15,
              y: -0.55,
            },
          ],
        },
      },
      deform: {
        default: {
          Mieng_open: {
            Mieng_open: [
              {
                offset: 1,
                vertices: [
                  -0.03851, 0.00952, -0.03851, 0.00952, -0.03851, 0.00952,
                  -0.03851,
                ],
              },
            ],
          },
          Nap_close: {
            Nap_open: [
              {
                vertices: [
                  -0.47011, -0.01443, -0.47011, -0.01443, -0.47011, -0.01443,
                  -0.47011, -0.01443,
                ],
              },
            ],
          },
        },
      },
      drawOrder: [
        {
          offsets: [
            {
              slot: "Golden3",
              offset: -2,
            },
          ],
        },
      ],
    },
    Open_Flip_Cards_4: {
      slots: {
        Anh_sang_close: {
          attachment: [
            {
              name: null,
            },
          ],
        },
        Bubble: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.4,
              color: "ffffffff",
            },
            {
              time: 0.7,
              color: "ffffff00",
            },
          ],
        },
        Bubble2: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.5667,
              color: "ffffff00",
            },
            {
              time: 1,
              color: "ffffffff",
            },
            {
              time: 1.4,
              color: "ffffff00",
            },
          ],
        },
        Bubble3: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.3,
              color: "ffffff00",
            },
            {
              time: 0.7333,
              color: "ffffffff",
            },
            {
              time: 1.1333,
              color: "ffffff00",
            },
          ],
        },
        Bubble4: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.7,
              color: "ffffff00",
            },
            {
              time: 1.1333,
              color: "ffffffff",
            },
            {
              time: 1.5333,
              color: "ffffff00",
            },
          ],
        },
        Bubble5: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.1333,
              color: "ffffff00",
            },
            {
              time: 0.5333,
              color: "ffffffff",
            },
            {
              time: 0.9333,
              color: "ffffff00",
            },
          ],
        },
        Bubble6: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.5333,
              color: "ffffff00",
            },
            {
              time: 0.9333,
              color: "ffffffff",
            },
            {
              time: 1.3333,
              color: "ffffff00",
            },
          ],
        },
        Bubble7: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.1,
              color: "ffffff00",
            },
            {
              time: 0.5333,
              color: "ffffffff",
            },
            {
              time: 0.9333,
              color: "ffffff00",
            },
          ],
        },
        "Frame 2071857771": {
          attachment: [
            {
              name: null,
            },
            {
              time: 0.5,
              name: "Frame 2071857775",
            },
          ],
        },
        "Frame 2071857772": {
          attachment: [
            {
              name: null,
            },
          ],
        },
        Glow1: {
          rgba: [
            {
              color: "ffffff3e",
            },
            {
              time: 0.6333,
              color: "ffffffff",
            },
            {
              time: 1.2,
              color: "ffffffc0",
            },
            {
              time: 1.6667,
              color: "ffffff26",
            },
          ],
        },
        Glow2: {
          rgba: [
            {
              color: "ffffff6a",
            },
            {
              time: 0.5,
              color: "ffffffff",
              curve: "stepped",
            },
            {
              time: 0.8333,
              color: "ffffffff",
            },
            {
              time: 1.3667,
              color: "ffffff7b",
            },
          ],
        },
        Glow3: {
          rgba: [
            {
              color: "ffffffff",
              curve: "stepped",
            },
            {
              time: 0.4,
              color: "ffffffed",
            },
            {
              time: 1.0667,
              color: "ffffff8a",
            },
            {
              time: 1.6333,
              color: "ffffffff",
            },
          ],
        },
        Golden: {
          attachment: [
            {
              time: 0.3667,
              name: null,
            },
          ],
        },
        Nap_close: {
          attachment: [
            {
              name: "Nap_open",
            },
          ],
        },
        Sparkles: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.1,
              color: "ffffff00",
            },
            {
              time: 0.6667,
              color: "ffffffff",
            },
            {
              time: 1,
              color: "ffffff00",
            },
          ],
        },
        Sparkles2: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.5333,
              color: "ffffff00",
            },
            {
              time: 1.0333,
              color: "ffffffff",
            },
            {
              time: 1.4333,
              color: "ffffff00",
            },
          ],
        },
        Sparkles3: {
          rgba: [
            {
              color: "ffffff5c",
            },
            {
              time: 0.3667,
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.9,
              color: "ffffff00",
            },
            {
              time: 1.3,
              color: "ffffffff",
            },
            {
              time: 1.6667,
              color: "ffffff00",
            },
          ],
        },
        Sparkles4: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.7,
              color: "ffffff00",
            },
            {
              time: 1.1667,
              color: "ffffffff",
            },
            {
              time: 1.5,
              color: "ffffff00",
            },
          ],
        },
        Than_close: {
          attachment: [
            {
              name: "Than_open",
            },
          ],
        },
      },
      bones: {
        Anh_Sang: {
          scale: [
            {
              y: 0.963,
            },
          ],
        },
        Bong: {
          translate: [
            {
              y: 0.01,
            },
          ],
        },
        Bubble1: {
          translate: [
            {},
            {
              time: 0.7,
              y: 32.35,
            },
          ],
        },
        Bubble2: {
          translate: [
            {
              y: 0.01,
            },
            {
              time: 0.5667,
            },
            {
              time: 1.4,
              y: 32.35,
            },
          ],
        },
        Bubble3: {
          translate: [
            {},
            {
              time: 0.3,
            },
            {
              time: 1.1333,
              y: 32.35,
            },
          ],
        },
        Bubble4: {
          translate: [
            {
              time: 0.7,
            },
            {
              time: 1.5333,
              y: 32.35,
            },
          ],
        },
        Bubble5: {
          translate: [
            {},
            {
              time: 0.1333,
            },
            {
              time: 0.9333,
              y: 32.35,
            },
          ],
        },
        Bubble6: {
          translate: [
            {
              y: 0.01,
            },
            {
              time: 0.5333,
            },
            {
              time: 1.3333,
              y: 32.35,
            },
          ],
        },
        Bubble7: {
          translate: [
            {},
            {
              time: 0.1,
            },
            {
              time: 0.9333,
              y: 32.35,
            },
          ],
        },
        Nap: {
          translate: [
            {
              x: 1.5,
            },
          ],
        },
        Card: {
          rotate: [
            {
              value: 0.72,
            },
          ],
          translate: [
            {
              x: -15.18,
              y: 6,
            },
          ],
          scale: [
            {
              curve: [0.056, 1.133, 0.139, 1.4, 0.056, 1.133, 0.111, 1.267],
            },
            {
              time: 0.1667,
              x: 1.2,
              y: 1.2,
              curve: [0.222, 0.8, 0.278, 0.4, 0.222, 1.133, 0.278, 1.067],
            },
            {
              time: 0.3333,
              x: 0,
            },
            {
              time: 0.5,
            },
          ],
        },
        Card2: {
          rotate: [
            {
              value: 0.72,
            },
          ],
          translate: [
            {
              x: -16.63,
              y: -107.22,
            },
          ],
          scale: [
            {
              time: 0.1333,
              curve: [0.189, 1.067, 0.244, 1.2, 0.189, 1.067, 0.244, 1.2],
            },
            {
              time: 0.3,
              x: 1.2,
              y: 1.2,
              curve: [0.356, 1.2, 0.411, 0.4, 0.356, 1.2, 0.411, 1.067],
            },
            {
              time: 0.4667,
              x: 0,
            },
            {
              time: 0.6333,
            },
          ],
        },
        Card3: {
          rotate: [
            {
              value: 0.72,
            },
          ],
          translate: [
            {
              x: -14.06,
              y: -14.13,
            },
          ],
          scale: [
            {
              time: 0.4,
              curve: [0.456, 1, 0.511, 1.2, 0.456, 1, 0.511, 1.2],
            },
            {
              time: 0.5667,
              x: 1.2,
              y: 1.2,
              curve: [0.622, 1.2, 0.678, 0.4, 0.622, 1.2, 0.678, 1.067],
            },
            {
              time: 0.7333,
              x: 0,
            },
            {
              time: 0.9,
            },
          ],
        },
        Than: {
          translate: [
            {
              x: 0.15,
              y: -0.95,
            },
          ],
        },
        Sparkle1: {
          rotate: [
            {
              value: 4.09,
            },
            {
              time: 0.1,
              value: 12.94,
            },
            {
              time: 1,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: -30.48,
              y: 217.9,
            },
          ],
        },
        Sparkle2: {
          rotate: [
            {},
            {
              time: 0.5333,
              value: 12.94,
            },
            {
              time: 1.4333,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: -129.03,
              y: 259.03,
            },
          ],
        },
        Sparkle3: {
          rotate: [
            {
              value: -2.73,
            },
            {
              time: 0.3667,
            },
            {
              time: 0.9,
              value: 12.94,
            },
            {
              time: 1.6667,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: 7.33,
              y: 48.22,
            },
          ],
        },
        Sparkle4: {
          rotate: [
            {},
            {
              time: 0.7,
              value: 12.94,
            },
            {
              time: 1.5,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: 177.32,
              y: 116.05,
            },
          ],
        },
        Card_Fly: {
          translate: [
            {
              x: 6.15,
              y: 320.55,
            },
          ],
        },
        bone: {
          translate: [
            {
              x: -0.15,
              y: -0.55,
            },
          ],
        },
      },
      deform: {
        default: {
          Mieng_open: {
            Mieng_open: [
              {
                offset: 1,
                vertices: [
                  -0.03851, 0.00952, -0.03851, 0.00952, -0.03851, 0.00952,
                  -0.03851,
                ],
              },
            ],
          },
          Nap_close: {
            Nap_open: [
              {
                vertices: [
                  -0.47011, -0.01443, -0.47011, -0.01443, -0.47011, -0.01443,
                  -0.47011, -0.01443,
                ],
              },
            ],
          },
        },
      },
      drawOrder: [
        {
          offsets: [
            {
              slot: "Golden3",
              offset: -2,
            },
          ],
        },
      ],
    },
    Open_Flip_Cards_5: {
      slots: {
        Anh_sang_close: {
          attachment: [
            {
              name: null,
            },
          ],
        },
        Bubble: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.4,
              color: "ffffffff",
            },
            {
              time: 0.7,
              color: "ffffff00",
            },
          ],
        },
        Bubble2: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.5667,
              color: "ffffff00",
            },
            {
              time: 1,
              color: "ffffffff",
            },
            {
              time: 1.4,
              color: "ffffff00",
            },
          ],
        },
        Bubble3: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.3,
              color: "ffffff00",
            },
            {
              time: 0.7333,
              color: "ffffffff",
            },
            {
              time: 1.1333,
              color: "ffffff00",
            },
          ],
        },
        Bubble4: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.7,
              color: "ffffff00",
            },
            {
              time: 1.1333,
              color: "ffffffff",
            },
            {
              time: 1.5333,
              color: "ffffff00",
            },
          ],
        },
        Bubble5: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.1333,
              color: "ffffff00",
            },
            {
              time: 0.5333,
              color: "ffffffff",
            },
            {
              time: 0.9333,
              color: "ffffff00",
            },
          ],
        },
        Bubble6: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.5333,
              color: "ffffff00",
            },
            {
              time: 0.9333,
              color: "ffffffff",
            },
            {
              time: 1.3333,
              color: "ffffff00",
            },
          ],
        },
        Bubble7: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.1,
              color: "ffffff00",
            },
            {
              time: 0.5333,
              color: "ffffffff",
            },
            {
              time: 0.9333,
              color: "ffffff00",
            },
          ],
        },
        "Frame 2071857771": {
          attachment: [
            {
              name: null,
            },
            {
              time: 0.5,
              name: "Frame 2071857776",
            },
          ],
        },
        "Frame 2071857772": {
          attachment: [
            {
              name: null,
            },
          ],
        },
        Glow1: {
          rgba: [
            {
              color: "ffffff3e",
            },
            {
              time: 0.6333,
              color: "ffffffff",
            },
            {
              time: 1.2,
              color: "ffffffc0",
            },
            {
              time: 1.6667,
              color: "ffffff26",
            },
          ],
        },
        Glow2: {
          rgba: [
            {
              color: "ffffff6a",
            },
            {
              time: 0.5,
              color: "ffffffff",
              curve: "stepped",
            },
            {
              time: 0.8333,
              color: "ffffffff",
            },
            {
              time: 1.3667,
              color: "ffffff7b",
            },
          ],
        },
        Glow3: {
          rgba: [
            {
              color: "ffffffff",
              curve: "stepped",
            },
            {
              time: 0.4,
              color: "ffffffed",
            },
            {
              time: 1.0667,
              color: "ffffff8a",
            },
            {
              time: 1.6333,
              color: "ffffffff",
            },
          ],
        },
        Golden: {
          attachment: [
            {
              time: 0.3667,
              name: null,
            },
          ],
        },
        Nap_close: {
          attachment: [
            {
              name: "Nap_open",
            },
          ],
        },
        Sparkles: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.1,
              color: "ffffff00",
            },
            {
              time: 0.6667,
              color: "ffffffff",
            },
            {
              time: 1,
              color: "ffffff00",
            },
          ],
        },
        Sparkles2: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.5333,
              color: "ffffff00",
            },
            {
              time: 1.0333,
              color: "ffffffff",
            },
            {
              time: 1.4333,
              color: "ffffff00",
            },
          ],
        },
        Sparkles3: {
          rgba: [
            {
              color: "ffffff5c",
            },
            {
              time: 0.3667,
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.9,
              color: "ffffff00",
            },
            {
              time: 1.3,
              color: "ffffffff",
            },
            {
              time: 1.6667,
              color: "ffffff00",
            },
          ],
        },
        Sparkles4: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.7,
              color: "ffffff00",
            },
            {
              time: 1.1667,
              color: "ffffffff",
            },
            {
              time: 1.5,
              color: "ffffff00",
            },
          ],
        },
        Than_close: {
          attachment: [
            {
              name: "Than_open",
            },
          ],
        },
      },
      bones: {
        Anh_Sang: {
          scale: [
            {
              y: 0.963,
            },
          ],
        },
        Bong: {
          translate: [
            {
              y: 0.01,
            },
          ],
        },
        Bubble1: {
          translate: [
            {},
            {
              time: 0.7,
              y: 32.35,
            },
          ],
        },
        Bubble2: {
          translate: [
            {
              y: 0.01,
            },
            {
              time: 0.5667,
            },
            {
              time: 1.4,
              y: 32.35,
            },
          ],
        },
        Bubble3: {
          translate: [
            {},
            {
              time: 0.3,
            },
            {
              time: 1.1333,
              y: 32.35,
            },
          ],
        },
        Bubble4: {
          translate: [
            {
              time: 0.7,
            },
            {
              time: 1.5333,
              y: 32.35,
            },
          ],
        },
        Bubble5: {
          translate: [
            {},
            {
              time: 0.1333,
            },
            {
              time: 0.9333,
              y: 32.35,
            },
          ],
        },
        Bubble6: {
          translate: [
            {
              y: 0.01,
            },
            {
              time: 0.5333,
            },
            {
              time: 1.3333,
              y: 32.35,
            },
          ],
        },
        Bubble7: {
          translate: [
            {},
            {
              time: 0.1,
            },
            {
              time: 0.9333,
              y: 32.35,
            },
          ],
        },
        Nap: {
          translate: [
            {
              x: 1.5,
            },
          ],
        },
        Card: {
          rotate: [
            {
              value: 0.72,
            },
          ],
          translate: [
            {
              x: -15.18,
              y: 6,
            },
          ],
          scale: [
            {
              curve: [0.056, 1.133, 0.139, 1.4, 0.056, 1.133, 0.111, 1.267],
            },
            {
              time: 0.1667,
              x: 1.2,
              y: 1.2,
              curve: [0.222, 0.8, 0.278, 0.4, 0.222, 1.133, 0.278, 1.067],
            },
            {
              time: 0.3333,
              x: 0,
            },
            {
              time: 0.5,
            },
          ],
        },
        Card2: {
          rotate: [
            {
              value: 0.72,
            },
          ],
          translate: [
            {
              x: -16.63,
              y: -107.22,
            },
          ],
          scale: [
            {
              time: 0.1333,
              curve: [0.189, 1.067, 0.244, 1.2, 0.189, 1.067, 0.244, 1.2],
            },
            {
              time: 0.3,
              x: 1.2,
              y: 1.2,
              curve: [0.356, 1.2, 0.411, 0.4, 0.356, 1.2, 0.411, 1.067],
            },
            {
              time: 0.4667,
              x: 0,
            },
            {
              time: 0.6333,
            },
          ],
        },
        Card3: {
          rotate: [
            {
              value: 0.72,
            },
          ],
          translate: [
            {
              x: -14.06,
              y: -14.13,
            },
          ],
          scale: [
            {
              time: 0.4,
              curve: [0.456, 1, 0.511, 1.2, 0.456, 1, 0.511, 1.2],
            },
            {
              time: 0.5667,
              x: 1.2,
              y: 1.2,
              curve: [0.622, 1.2, 0.678, 0.4, 0.622, 1.2, 0.678, 1.067],
            },
            {
              time: 0.7333,
              x: 0,
            },
            {
              time: 0.9,
            },
          ],
        },
        Than: {
          translate: [
            {
              x: 0.15,
              y: -0.95,
            },
          ],
        },
        Sparkle1: {
          rotate: [
            {
              value: 4.09,
            },
            {
              time: 0.1,
              value: 12.94,
            },
            {
              time: 1,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: -30.48,
              y: 217.9,
            },
          ],
        },
        Sparkle2: {
          rotate: [
            {},
            {
              time: 0.5333,
              value: 12.94,
            },
            {
              time: 1.4333,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: -129.03,
              y: 259.03,
            },
          ],
        },
        Sparkle3: {
          rotate: [
            {
              value: -2.73,
            },
            {
              time: 0.3667,
            },
            {
              time: 0.9,
              value: 12.94,
            },
            {
              time: 1.6667,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: 7.33,
              y: 48.22,
            },
          ],
        },
        Sparkle4: {
          rotate: [
            {},
            {
              time: 0.7,
              value: 12.94,
            },
            {
              time: 1.5,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: 177.32,
              y: 116.05,
            },
          ],
        },
        Card_Fly: {
          translate: [
            {
              x: 6.15,
              y: 320.55,
            },
          ],
        },
        bone: {
          translate: [
            {
              x: -0.15,
              y: -0.55,
            },
          ],
        },
      },
      deform: {
        default: {
          Mieng_open: {
            Mieng_open: [
              {
                offset: 1,
                vertices: [
                  -0.03851, 0.00952, -0.03851, 0.00952, -0.03851, 0.00952,
                  -0.03851,
                ],
              },
            ],
          },
          Nap_close: {
            Nap_open: [
              {
                vertices: [
                  -0.47011, -0.01443, -0.47011, -0.01443, -0.47011, -0.01443,
                  -0.47011, -0.01443,
                ],
              },
            ],
          },
        },
      },
      drawOrder: [
        {
          offsets: [
            {
              slot: "Golden3",
              offset: -2,
            },
          ],
        },
      ],
    },
    Open_Flip_Cards_6: {
      slots: {
        Anh_sang_close: {
          attachment: [
            {
              name: null,
            },
          ],
        },
        Bubble: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.4,
              color: "ffffffff",
            },
            {
              time: 0.7,
              color: "ffffff00",
            },
          ],
        },
        Bubble2: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.5667,
              color: "ffffff00",
            },
            {
              time: 1,
              color: "ffffffff",
            },
            {
              time: 1.4,
              color: "ffffff00",
            },
          ],
        },
        Bubble3: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.3,
              color: "ffffff00",
            },
            {
              time: 0.7333,
              color: "ffffffff",
            },
            {
              time: 1.1333,
              color: "ffffff00",
            },
          ],
        },
        Bubble4: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.7,
              color: "ffffff00",
            },
            {
              time: 1.1333,
              color: "ffffffff",
            },
            {
              time: 1.5333,
              color: "ffffff00",
            },
          ],
        },
        Bubble5: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.1333,
              color: "ffffff00",
            },
            {
              time: 0.5333,
              color: "ffffffff",
            },
            {
              time: 0.9333,
              color: "ffffff00",
            },
          ],
        },
        Bubble6: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.5333,
              color: "ffffff00",
            },
            {
              time: 0.9333,
              color: "ffffffff",
            },
            {
              time: 1.3333,
              color: "ffffff00",
            },
          ],
        },
        Bubble7: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.1,
              color: "ffffff00",
            },
            {
              time: 0.5333,
              color: "ffffffff",
            },
            {
              time: 0.9333,
              color: "ffffff00",
            },
          ],
        },
        "Frame 2071857771": {
          attachment: [
            {
              name: null,
            },
            {
              time: 0.5,
              name: "Frame 2071857777",
            },
          ],
        },
        "Frame 2071857772": {
          attachment: [
            {
              name: null,
            },
          ],
        },
        Glow1: {
          rgba: [
            {
              color: "ffffff3e",
            },
            {
              time: 0.6333,
              color: "ffffffff",
            },
            {
              time: 1.2,
              color: "ffffffc0",
            },
            {
              time: 1.6667,
              color: "ffffff26",
            },
          ],
        },
        Glow2: {
          rgba: [
            {
              color: "ffffff6a",
            },
            {
              time: 0.5,
              color: "ffffffff",
              curve: "stepped",
            },
            {
              time: 0.8333,
              color: "ffffffff",
            },
            {
              time: 1.3667,
              color: "ffffff7b",
            },
          ],
        },
        Glow3: {
          rgba: [
            {
              color: "ffffffff",
              curve: "stepped",
            },
            {
              time: 0.4,
              color: "ffffffed",
            },
            {
              time: 1.0667,
              color: "ffffff8a",
            },
            {
              time: 1.6333,
              color: "ffffffff",
            },
          ],
        },
        Golden: {
          attachment: [
            {
              time: 0.3667,
              name: null,
            },
          ],
        },
        Nap_close: {
          attachment: [
            {
              name: "Nap_open",
            },
          ],
        },
        Sparkles: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.1,
              color: "ffffff00",
            },
            {
              time: 0.6667,
              color: "ffffffff",
            },
            {
              time: 1,
              color: "ffffff00",
            },
          ],
        },
        Sparkles2: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.5333,
              color: "ffffff00",
            },
            {
              time: 1.0333,
              color: "ffffffff",
            },
            {
              time: 1.4333,
              color: "ffffff00",
            },
          ],
        },
        Sparkles3: {
          rgba: [
            {
              color: "ffffff5c",
            },
            {
              time: 0.3667,
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.9,
              color: "ffffff00",
            },
            {
              time: 1.3,
              color: "ffffffff",
            },
            {
              time: 1.6667,
              color: "ffffff00",
            },
          ],
        },
        Sparkles4: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.7,
              color: "ffffff00",
            },
            {
              time: 1.1667,
              color: "ffffffff",
            },
            {
              time: 1.5,
              color: "ffffff00",
            },
          ],
        },
        Than_close: {
          attachment: [
            {
              name: "Than_open",
            },
          ],
        },
      },
      bones: {
        Anh_Sang: {
          scale: [
            {
              y: 0.963,
            },
          ],
        },
        Bong: {
          translate: [
            {
              y: 0.01,
            },
          ],
        },
        Bubble1: {
          translate: [
            {},
            {
              time: 0.7,
              y: 32.35,
            },
          ],
        },
        Bubble2: {
          translate: [
            {
              y: 0.01,
            },
            {
              time: 0.5667,
            },
            {
              time: 1.4,
              y: 32.35,
            },
          ],
        },
        Bubble3: {
          translate: [
            {},
            {
              time: 0.3,
            },
            {
              time: 1.1333,
              y: 32.35,
            },
          ],
        },
        Bubble4: {
          translate: [
            {
              time: 0.7,
            },
            {
              time: 1.5333,
              y: 32.35,
            },
          ],
        },
        Bubble5: {
          translate: [
            {},
            {
              time: 0.1333,
            },
            {
              time: 0.9333,
              y: 32.35,
            },
          ],
        },
        Bubble6: {
          translate: [
            {
              y: 0.01,
            },
            {
              time: 0.5333,
            },
            {
              time: 1.3333,
              y: 32.35,
            },
          ],
        },
        Bubble7: {
          translate: [
            {},
            {
              time: 0.1,
            },
            {
              time: 0.9333,
              y: 32.35,
            },
          ],
        },
        Nap: {
          translate: [
            {
              x: 1.5,
            },
          ],
        },
        Card: {
          rotate: [
            {
              value: 0.72,
            },
          ],
          translate: [
            {
              x: -15.18,
              y: 6,
            },
          ],
          scale: [
            {
              curve: [0.056, 1.133, 0.139, 1.4, 0.056, 1.133, 0.111, 1.267],
            },
            {
              time: 0.1667,
              x: 1.2,
              y: 1.2,
              curve: [0.222, 0.8, 0.278, 0.4, 0.222, 1.133, 0.278, 1.067],
            },
            {
              time: 0.3333,
              x: 0,
            },
            {
              time: 0.5,
            },
          ],
        },
        Card2: {
          rotate: [
            {
              value: 0.72,
            },
          ],
          translate: [
            {
              x: -16.63,
              y: -107.22,
            },
          ],
          scale: [
            {
              time: 0.1333,
              curve: [0.189, 1.067, 0.244, 1.2, 0.189, 1.067, 0.244, 1.2],
            },
            {
              time: 0.3,
              x: 1.2,
              y: 1.2,
              curve: [0.356, 1.2, 0.411, 0.4, 0.356, 1.2, 0.411, 1.067],
            },
            {
              time: 0.4667,
              x: 0,
            },
            {
              time: 0.6333,
            },
          ],
        },
        Card3: {
          rotate: [
            {
              value: 0.72,
            },
          ],
          translate: [
            {
              x: -14.06,
              y: -14.13,
            },
          ],
          scale: [
            {
              time: 0.4,
              curve: [0.456, 1, 0.511, 1.2, 0.456, 1, 0.511, 1.2],
            },
            {
              time: 0.5667,
              x: 1.2,
              y: 1.2,
              curve: [0.622, 1.2, 0.678, 0.4, 0.622, 1.2, 0.678, 1.067],
            },
            {
              time: 0.7333,
              x: 0,
            },
            {
              time: 0.9,
            },
          ],
        },
        Than: {
          translate: [
            {
              x: 0.15,
              y: -0.95,
            },
          ],
        },
        Sparkle1: {
          rotate: [
            {
              value: 4.09,
            },
            {
              time: 0.1,
              value: 12.94,
            },
            {
              time: 1,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: -30.48,
              y: 217.9,
            },
          ],
        },
        Sparkle2: {
          rotate: [
            {},
            {
              time: 0.5333,
              value: 12.94,
            },
            {
              time: 1.4333,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: -129.03,
              y: 259.03,
            },
          ],
        },
        Sparkle3: {
          rotate: [
            {
              value: -2.73,
            },
            {
              time: 0.3667,
            },
            {
              time: 0.9,
              value: 12.94,
            },
            {
              time: 1.6667,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: 7.33,
              y: 48.22,
            },
          ],
        },
        Sparkle4: {
          rotate: [
            {},
            {
              time: 0.7,
              value: 12.94,
            },
            {
              time: 1.5,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: 177.32,
              y: 116.05,
            },
          ],
        },
        Card_Fly: {
          translate: [
            {
              x: 6.15,
              y: 320.55,
            },
          ],
        },
        bone: {
          translate: [
            {
              x: -0.15,
              y: -0.55,
            },
          ],
        },
      },
      deform: {
        default: {
          Mieng_open: {
            Mieng_open: [
              {
                offset: 1,
                vertices: [
                  -0.03851, 0.00952, -0.03851, 0.00952, -0.03851, 0.00952,
                  -0.03851,
                ],
              },
            ],
          },
          Nap_close: {
            Nap_open: [
              {
                vertices: [
                  -0.47011, -0.01443, -0.47011, -0.01443, -0.47011, -0.01443,
                  -0.47011, -0.01443,
                ],
              },
            ],
          },
        },
      },
      drawOrder: [
        {
          offsets: [
            {
              slot: "Golden3",
              offset: -2,
            },
          ],
        },
      ],
    },
    Open_Flip_Cards_7: {
      slots: {
        Anh_sang_close: {
          attachment: [
            {
              name: null,
            },
          ],
        },
        Bubble: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.4,
              color: "ffffffff",
            },
            {
              time: 0.7,
              color: "ffffff00",
            },
          ],
        },
        Bubble2: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.5667,
              color: "ffffff00",
            },
            {
              time: 1,
              color: "ffffffff",
            },
            {
              time: 1.4,
              color: "ffffff00",
            },
          ],
        },
        Bubble3: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.3,
              color: "ffffff00",
            },
            {
              time: 0.7333,
              color: "ffffffff",
            },
            {
              time: 1.1333,
              color: "ffffff00",
            },
          ],
        },
        Bubble4: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.7,
              color: "ffffff00",
            },
            {
              time: 1.1333,
              color: "ffffffff",
            },
            {
              time: 1.5333,
              color: "ffffff00",
            },
          ],
        },
        Bubble5: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.1333,
              color: "ffffff00",
            },
            {
              time: 0.5333,
              color: "ffffffff",
            },
            {
              time: 0.9333,
              color: "ffffff00",
            },
          ],
        },
        Bubble6: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.5333,
              color: "ffffff00",
            },
            {
              time: 0.9333,
              color: "ffffffff",
            },
            {
              time: 1.3333,
              color: "ffffff00",
            },
          ],
        },
        Bubble7: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.1,
              color: "ffffff00",
            },
            {
              time: 0.5333,
              color: "ffffffff",
            },
            {
              time: 0.9333,
              color: "ffffff00",
            },
          ],
        },
        "Frame 2071857771": {
          attachment: [
            {
              name: null,
            },
            {
              time: 0.5,
              name: "Ticket_hoi_suc",
            },
          ],
        },
        "Frame 2071857772": {
          attachment: [
            {
              name: null,
            },
          ],
        },
        Glow1: {
          rgba: [
            {
              color: "ffffff3e",
            },
            {
              time: 0.6333,
              color: "ffffffff",
            },
            {
              time: 1.2,
              color: "ffffffc0",
            },
            {
              time: 1.6667,
              color: "ffffff26",
            },
          ],
        },
        Glow2: {
          rgba: [
            {
              color: "ffffff6a",
            },
            {
              time: 0.5,
              color: "ffffffff",
              curve: "stepped",
            },
            {
              time: 0.8333,
              color: "ffffffff",
            },
            {
              time: 1.3667,
              color: "ffffff7b",
            },
          ],
        },
        Glow3: {
          rgba: [
            {
              color: "ffffffff",
              curve: "stepped",
            },
            {
              time: 0.4,
              color: "ffffffed",
            },
            {
              time: 1.0667,
              color: "ffffff8a",
            },
            {
              time: 1.6333,
              color: "ffffffff",
            },
          ],
        },
        Golden: {
          attachment: [
            {
              time: 0.3667,
              name: null,
            },
          ],
        },
        Nap_close: {
          attachment: [
            {
              name: "Nap_open",
            },
          ],
        },
        Sparkles: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.1,
              color: "ffffff00",
            },
            {
              time: 0.6667,
              color: "ffffffff",
            },
            {
              time: 1,
              color: "ffffff00",
            },
          ],
        },
        Sparkles2: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.5333,
              color: "ffffff00",
            },
            {
              time: 1.0333,
              color: "ffffffff",
            },
            {
              time: 1.4333,
              color: "ffffff00",
            },
          ],
        },
        Sparkles3: {
          rgba: [
            {
              color: "ffffff5c",
            },
            {
              time: 0.3667,
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.9,
              color: "ffffff00",
            },
            {
              time: 1.3,
              color: "ffffffff",
            },
            {
              time: 1.6667,
              color: "ffffff00",
            },
          ],
        },
        Sparkles4: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.7,
              color: "ffffff00",
            },
            {
              time: 1.1667,
              color: "ffffffff",
            },
            {
              time: 1.5,
              color: "ffffff00",
            },
          ],
        },
        Than_close: {
          attachment: [
            {
              name: "Than_open",
            },
          ],
        },
      },
      bones: {
        Anh_Sang: {
          scale: [
            {
              y: 0.963,
            },
          ],
        },
        Bong: {
          translate: [
            {
              y: 0.01,
            },
          ],
        },
        Bubble1: {
          translate: [
            {},
            {
              time: 0.7,
              y: 32.35,
            },
          ],
        },
        Bubble2: {
          translate: [
            {
              y: 0.01,
            },
            {
              time: 0.5667,
            },
            {
              time: 1.4,
              y: 32.35,
            },
          ],
        },
        Bubble3: {
          translate: [
            {},
            {
              time: 0.3,
            },
            {
              time: 1.1333,
              y: 32.35,
            },
          ],
        },
        Bubble4: {
          translate: [
            {
              time: 0.7,
            },
            {
              time: 1.5333,
              y: 32.35,
            },
          ],
        },
        Bubble5: {
          translate: [
            {},
            {
              time: 0.1333,
            },
            {
              time: 0.9333,
              y: 32.35,
            },
          ],
        },
        Bubble6: {
          translate: [
            {
              y: 0.01,
            },
            {
              time: 0.5333,
            },
            {
              time: 1.3333,
              y: 32.35,
            },
          ],
        },
        Bubble7: {
          translate: [
            {},
            {
              time: 0.1,
            },
            {
              time: 0.9333,
              y: 32.35,
            },
          ],
        },
        Nap: {
          translate: [
            {
              x: 1.5,
            },
          ],
        },
        Card: {
          rotate: [
            {
              value: 0.72,
            },
          ],
          translate: [
            {
              x: -15.18,
              y: 6,
            },
          ],
          scale: [
            {
              curve: [0.056, 1.133, 0.139, 1.4, 0.056, 1.133, 0.111, 1.267],
            },
            {
              time: 0.1667,
              x: 1.2,
              y: 1.2,
              curve: [0.222, 0.8, 0.278, 0.4, 0.222, 1.133, 0.278, 1.067],
            },
            {
              time: 0.3333,
              x: 0,
            },
            {
              time: 0.5,
            },
          ],
        },
        Card2: {
          rotate: [
            {
              value: 0.72,
            },
          ],
          translate: [
            {
              x: -16.63,
              y: -107.22,
            },
          ],
          scale: [
            {
              time: 0.1333,
              curve: [0.189, 1.067, 0.244, 1.2, 0.189, 1.067, 0.244, 1.2],
            },
            {
              time: 0.3,
              x: 1.2,
              y: 1.2,
              curve: [0.356, 1.2, 0.411, 0.4, 0.356, 1.2, 0.411, 1.067],
            },
            {
              time: 0.4667,
              x: 0,
            },
            {
              time: 0.6333,
            },
          ],
        },
        Card3: {
          rotate: [
            {
              value: 0.72,
            },
          ],
          translate: [
            {
              x: -14.06,
              y: -14.13,
            },
          ],
          scale: [
            {
              time: 0.4,
              curve: [0.456, 1, 0.511, 1.2, 0.456, 1, 0.511, 1.2],
            },
            {
              time: 0.5667,
              x: 1.2,
              y: 1.2,
              curve: [0.622, 1.2, 0.678, 0.4, 0.622, 1.2, 0.678, 1.067],
            },
            {
              time: 0.7333,
              x: 0,
            },
            {
              time: 0.9,
            },
          ],
        },
        Than: {
          translate: [
            {
              x: 0.15,
              y: -0.95,
            },
          ],
        },
        Sparkle1: {
          rotate: [
            {
              value: 4.09,
            },
            {
              time: 0.1,
              value: 12.94,
            },
            {
              time: 1,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: -30.48,
              y: 217.9,
            },
          ],
        },
        Sparkle2: {
          rotate: [
            {},
            {
              time: 0.5333,
              value: 12.94,
            },
            {
              time: 1.4333,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: -129.03,
              y: 259.03,
            },
          ],
        },
        Sparkle3: {
          rotate: [
            {
              value: -2.73,
            },
            {
              time: 0.3667,
            },
            {
              time: 0.9,
              value: 12.94,
            },
            {
              time: 1.6667,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: 7.33,
              y: 48.22,
            },
          ],
        },
        Sparkle4: {
          rotate: [
            {},
            {
              time: 0.7,
              value: 12.94,
            },
            {
              time: 1.5,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: 177.32,
              y: 116.05,
            },
          ],
        },
        Card_Fly: {
          translate: [
            {
              x: 6.15,
              y: 320.55,
            },
          ],
        },
        bone: {
          translate: [
            {
              x: -0.15,
              y: -0.55,
            },
          ],
        },
      },
      deform: {
        default: {
          Mieng_open: {
            Mieng_open: [
              {
                offset: 1,
                vertices: [
                  -0.03851, 0.00952, -0.03851, 0.00952, -0.03851, 0.00952,
                  -0.03851,
                ],
              },
            ],
          },
          Nap_close: {
            Nap_open: [
              {
                vertices: [
                  -0.47011, -0.01443, -0.47011, -0.01443, -0.47011, -0.01443,
                  -0.47011, -0.01443,
                ],
              },
            ],
          },
        },
      },
      drawOrder: [
        {
          offsets: [
            {
              slot: "Golden3",
              offset: -2,
            },
          ],
        },
      ],
    },
    Open_Idle: {
      slots: {
        Anh_sang_close: {
          attachment: [
            {
              name: null,
            },
          ],
        },
        Bubble: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.4,
              color: "ffffffff",
            },
            {
              time: 0.7,
              color: "ffffff00",
            },
          ],
        },
        Bubble2: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.5667,
              color: "ffffff00",
            },
            {
              time: 1,
              color: "ffffffff",
            },
            {
              time: 1.4,
              color: "ffffff00",
            },
          ],
        },
        Bubble3: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.3,
              color: "ffffff00",
            },
            {
              time: 0.7333,
              color: "ffffffff",
            },
            {
              time: 1.1333,
              color: "ffffff00",
            },
          ],
        },
        Bubble4: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.7,
              color: "ffffff00",
            },
            {
              time: 1.1333,
              color: "ffffffff",
            },
            {
              time: 1.5333,
              color: "ffffff00",
            },
          ],
        },
        Bubble5: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.1333,
              color: "ffffff00",
            },
            {
              time: 0.5333,
              color: "ffffffff",
            },
            {
              time: 0.9333,
              color: "ffffff00",
            },
          ],
        },
        Bubble6: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.5333,
              color: "ffffff00",
            },
            {
              time: 0.9333,
              color: "ffffffff",
            },
            {
              time: 1.3333,
              color: "ffffff00",
            },
          ],
        },
        Bubble7: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.1,
              color: "ffffff00",
            },
            {
              time: 0.5333,
              color: "ffffffff",
            },
            {
              time: 0.9333,
              color: "ffffff00",
            },
          ],
        },
        "Frame 2071857771": {
          attachment: [
            {
              name: null,
            },
          ],
        },
        "Frame 2071857772": {
          attachment: [
            {
              name: "Frame 2071857771",
            },
          ],
        },
        Glow1: {
          rgba: [
            {
              color: "ffffff3e",
            },
            {
              time: 0.6333,
              color: "ffffffff",
            },
            {
              time: 1.2,
              color: "ffffffc0",
            },
            {
              time: 1.6667,
              color: "ffffff26",
            },
          ],
        },
        Glow2: {
          rgba: [
            {
              color: "ffffff6a",
            },
            {
              time: 0.5,
              color: "ffffffff",
              curve: "stepped",
            },
            {
              time: 0.8333,
              color: "ffffffff",
            },
            {
              time: 1.3667,
              color: "ffffff7b",
            },
          ],
        },
        Glow3: {
          rgba: [
            {
              color: "ffffffff",
              curve: "stepped",
            },
            {
              time: 0.4,
              color: "ffffffed",
            },
            {
              time: 1.0667,
              color: "ffffff8a",
            },
            {
              time: 1.6333,
              color: "ffffffff",
            },
          ],
        },
        Golden: {
          attachment: [
            {
              name: null,
            },
          ],
        },
        Nap_close: {
          attachment: [
            {
              name: "Nap_open",
            },
          ],
        },
        Sparkles: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.1,
              color: "ffffff00",
            },
            {
              time: 0.6667,
              color: "ffffffff",
            },
            {
              time: 1,
              color: "ffffff00",
            },
          ],
        },
        Sparkles2: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.5333,
              color: "ffffff00",
            },
            {
              time: 1.0333,
              color: "ffffffff",
            },
            {
              time: 1.4333,
              color: "ffffff00",
            },
          ],
        },
        Sparkles3: {
          rgba: [
            {
              color: "ffffff5c",
            },
            {
              time: 0.3667,
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.9,
              color: "ffffff00",
            },
            {
              time: 1.3,
              color: "ffffffff",
            },
            {
              time: 1.6667,
              color: "ffffff00",
            },
          ],
        },
        Sparkles4: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.7,
              color: "ffffff00",
            },
            {
              time: 1.1667,
              color: "ffffffff",
            },
            {
              time: 1.5,
              color: "ffffff00",
            },
          ],
        },
        Than_close: {
          attachment: [
            {
              name: "Than_open",
            },
          ],
        },
      },
      bones: {
        Anh_Sang: {
          scale: [
            {
              y: 0.963,
            },
          ],
        },
        Bong: {
          translate: [
            {
              y: 0.01,
            },
          ],
        },
        Bubble1: {
          translate: [
            {},
            {
              time: 0.7,
              y: 32.35,
            },
          ],
        },
        Bubble2: {
          translate: [
            {
              y: 0.01,
            },
            {
              time: 0.5667,
            },
            {
              time: 1.4,
              y: 32.35,
            },
          ],
        },
        Bubble3: {
          translate: [
            {},
            {
              time: 0.3,
            },
            {
              time: 1.1333,
              y: 32.35,
            },
          ],
        },
        Bubble4: {
          translate: [
            {
              time: 0.7,
            },
            {
              time: 1.5333,
              y: 32.35,
            },
          ],
        },
        Bubble5: {
          translate: [
            {},
            {
              time: 0.1333,
            },
            {
              time: 0.9333,
              y: 32.35,
            },
          ],
        },
        Bubble6: {
          translate: [
            {
              y: 0.01,
            },
            {
              time: 0.5333,
            },
            {
              time: 1.3333,
              y: 32.35,
            },
          ],
        },
        Bubble7: {
          translate: [
            {},
            {
              time: 0.1,
            },
            {
              time: 0.9333,
              y: 32.35,
            },
          ],
        },
        Nap: {
          translate: [
            {
              x: 1.5,
            },
          ],
        },
        Card: {
          rotate: [
            {
              value: 0.72,
            },
          ],
          translate: [
            {
              x: -15.18,
              y: 6,
            },
          ],
        },
        Card2: {
          rotate: [
            {
              value: 0.72,
            },
          ],
          translate: [
            {
              x: -16.63,
              y: -113.22,
            },
          ],
        },
        Card3: {
          rotate: [
            {
              value: 0.72,
            },
          ],
          translate: [
            {
              x: -14.06,
              y: -14.13,
            },
          ],
        },
        Than: {
          translate: [
            {
              x: 0.15,
              y: -0.95,
            },
          ],
        },
        Sparkle1: {
          rotate: [
            {
              value: 4.09,
            },
            {
              time: 0.1,
              value: 12.94,
            },
            {
              time: 1,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: -30.48,
              y: 217.9,
            },
          ],
        },
        Sparkle2: {
          rotate: [
            {},
            {
              time: 0.5333,
              value: 12.94,
            },
            {
              time: 1.4333,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: -129.03,
              y: 259.03,
            },
          ],
        },
        Sparkle3: {
          rotate: [
            {
              value: -2.73,
            },
            {
              time: 0.3667,
            },
            {
              time: 0.9,
              value: 12.94,
            },
            {
              time: 1.6667,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: 7.33,
              y: 48.22,
            },
          ],
        },
        Sparkle4: {
          rotate: [
            {},
            {
              time: 0.7,
              value: 12.94,
            },
            {
              time: 1.5,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: 177.32,
              y: 116.05,
            },
          ],
        },
        Card_Fly: {
          translate: [
            {
              x: 6.15,
              y: 320.55,
            },
          ],
        },
        bone: {
          translate: [
            {
              x: -0.15,
              y: -0.55,
            },
          ],
        },
      },
      deform: {
        default: {
          Mieng_open: {
            Mieng_open: [
              {
                offset: 1,
                vertices: [
                  -0.03851, 0.00952, -0.03851, 0.00952, -0.03851, 0.00952,
                  -0.03851,
                ],
              },
            ],
          },
          Nap_close: {
            Nap_open: [
              {
                vertices: [
                  -0.47011, -0.01443, -0.47011, -0.01443, -0.47011, -0.01443,
                  -0.47011, -0.01443,
                ],
              },
            ],
          },
        },
      },
      drawOrder: [
        {
          offsets: [
            {
              slot: "Golden3",
              offset: -2,
            },
          ],
        },
      ],
    },
    Open_Idle2: {
      slots: {
        Anh_sang_close: {
          attachment: [
            {
              name: null,
            },
          ],
        },
        Bubble: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.4,
              color: "ffffffff",
            },
            {
              time: 0.7,
              color: "ffffff00",
            },
          ],
        },
        Bubble2: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.5667,
              color: "ffffff00",
            },
            {
              time: 1,
              color: "ffffffff",
            },
            {
              time: 1.4,
              color: "ffffff00",
            },
          ],
        },
        Bubble3: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.3,
              color: "ffffff00",
            },
            {
              time: 0.7333,
              color: "ffffffff",
            },
            {
              time: 1.1333,
              color: "ffffff00",
            },
          ],
        },
        Bubble4: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.7,
              color: "ffffff00",
            },
            {
              time: 1.1333,
              color: "ffffffff",
            },
            {
              time: 1.5333,
              color: "ffffff00",
            },
          ],
        },
        Bubble5: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.1333,
              color: "ffffff00",
            },
            {
              time: 0.5333,
              color: "ffffffff",
            },
            {
              time: 0.9333,
              color: "ffffff00",
            },
          ],
        },
        Bubble6: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.5333,
              color: "ffffff00",
            },
            {
              time: 0.9333,
              color: "ffffffff",
            },
            {
              time: 1.3333,
              color: "ffffff00",
            },
          ],
        },
        Bubble7: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.1,
              color: "ffffff00",
            },
            {
              time: 0.5333,
              color: "ffffffff",
            },
            {
              time: 0.9333,
              color: "ffffff00",
            },
          ],
        },
        "Frame 2071857771": {
          attachment: [
            {
              name: null,
            },
          ],
        },
        "Frame 2071857772": {
          attachment: [
            {
              name: "Frame 2071857772",
            },
          ],
        },
        Glow1: {
          rgba: [
            {
              color: "ffffff3e",
            },
            {
              time: 0.6333,
              color: "ffffffff",
            },
            {
              time: 1.2,
              color: "ffffffc0",
            },
            {
              time: 1.6667,
              color: "ffffff26",
            },
          ],
        },
        Glow2: {
          rgba: [
            {
              color: "ffffff6a",
            },
            {
              time: 0.5,
              color: "ffffffff",
              curve: "stepped",
            },
            {
              time: 0.8333,
              color: "ffffffff",
            },
            {
              time: 1.3667,
              color: "ffffff7b",
            },
          ],
        },
        Glow3: {
          rgba: [
            {
              color: "ffffffff",
              curve: "stepped",
            },
            {
              time: 0.4,
              color: "ffffffed",
            },
            {
              time: 1.0667,
              color: "ffffff8a",
            },
            {
              time: 1.6333,
              color: "ffffffff",
            },
          ],
        },
        Golden: {
          attachment: [
            {
              name: null,
            },
          ],
        },
        Nap_close: {
          attachment: [
            {
              name: "Nap_open",
            },
          ],
        },
        Sparkles: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.1,
              color: "ffffff00",
            },
            {
              time: 0.6667,
              color: "ffffffff",
            },
            {
              time: 1,
              color: "ffffff00",
            },
          ],
        },
        Sparkles2: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.5333,
              color: "ffffff00",
            },
            {
              time: 1.0333,
              color: "ffffffff",
            },
            {
              time: 1.4333,
              color: "ffffff00",
            },
          ],
        },
        Sparkles3: {
          rgba: [
            {
              color: "ffffff5c",
            },
            {
              time: 0.3667,
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.9,
              color: "ffffff00",
            },
            {
              time: 1.3,
              color: "ffffffff",
            },
            {
              time: 1.6667,
              color: "ffffff00",
            },
          ],
        },
        Sparkles4: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.7,
              color: "ffffff00",
            },
            {
              time: 1.1667,
              color: "ffffffff",
            },
            {
              time: 1.5,
              color: "ffffff00",
            },
          ],
        },
        Than_close: {
          attachment: [
            {
              name: "Than_open",
            },
          ],
        },
      },
      bones: {
        Anh_Sang: {
          scale: [
            {
              y: 0.963,
            },
          ],
        },
        Bong: {
          translate: [
            {
              y: 0.01,
            },
          ],
        },
        Bubble1: {
          translate: [
            {},
            {
              time: 0.7,
              y: 32.35,
            },
          ],
        },
        Bubble2: {
          translate: [
            {
              y: 0.01,
            },
            {
              time: 0.5667,
            },
            {
              time: 1.4,
              y: 32.35,
            },
          ],
        },
        Bubble3: {
          translate: [
            {},
            {
              time: 0.3,
            },
            {
              time: 1.1333,
              y: 32.35,
            },
          ],
        },
        Bubble4: {
          translate: [
            {
              time: 0.7,
            },
            {
              time: 1.5333,
              y: 32.35,
            },
          ],
        },
        Bubble5: {
          translate: [
            {},
            {
              time: 0.1333,
            },
            {
              time: 0.9333,
              y: 32.35,
            },
          ],
        },
        Bubble6: {
          translate: [
            {
              y: 0.01,
            },
            {
              time: 0.5333,
            },
            {
              time: 1.3333,
              y: 32.35,
            },
          ],
        },
        Bubble7: {
          translate: [
            {},
            {
              time: 0.1,
            },
            {
              time: 0.9333,
              y: 32.35,
            },
          ],
        },
        Nap: {
          translate: [
            {
              x: 1.5,
            },
          ],
        },
        Card: {
          rotate: [
            {
              value: 0.72,
            },
          ],
          translate: [
            {
              x: -15.18,
              y: 6,
            },
          ],
        },
        Card2: {
          rotate: [
            {
              value: 0.72,
            },
          ],
          translate: [
            {
              x: -16.63,
              y: -113.22,
            },
          ],
        },
        Card3: {
          rotate: [
            {
              value: 0.72,
            },
          ],
          translate: [
            {
              x: -14.06,
              y: -14.13,
            },
          ],
        },
        Than: {
          translate: [
            {
              x: 0.15,
              y: -0.95,
            },
          ],
        },
        Sparkle1: {
          rotate: [
            {
              value: 4.09,
            },
            {
              time: 0.1,
              value: 12.94,
            },
            {
              time: 1,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: -30.48,
              y: 217.9,
            },
          ],
        },
        Sparkle2: {
          rotate: [
            {},
            {
              time: 0.5333,
              value: 12.94,
            },
            {
              time: 1.4333,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: -129.03,
              y: 259.03,
            },
          ],
        },
        Sparkle3: {
          rotate: [
            {
              value: -2.73,
            },
            {
              time: 0.3667,
            },
            {
              time: 0.9,
              value: 12.94,
            },
            {
              time: 1.6667,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: 7.33,
              y: 48.22,
            },
          ],
        },
        Sparkle4: {
          rotate: [
            {},
            {
              time: 0.7,
              value: 12.94,
            },
            {
              time: 1.5,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: 177.32,
              y: 116.05,
            },
          ],
        },
        Card_Fly: {
          translate: [
            {
              x: 6.15,
              y: 320.55,
            },
          ],
        },
        bone: {
          translate: [
            {
              x: -0.15,
              y: -0.55,
            },
          ],
        },
      },
      deform: {
        default: {
          Mieng_open: {
            Mieng_open: [
              {
                offset: 1,
                vertices: [
                  -0.03851, 0.00952, -0.03851, 0.00952, -0.03851, 0.00952,
                  -0.03851,
                ],
              },
            ],
          },
          Nap_close: {
            Nap_open: [
              {
                vertices: [
                  -0.47011, -0.01443, -0.47011, -0.01443, -0.47011, -0.01443,
                  -0.47011, -0.01443,
                ],
              },
            ],
          },
        },
      },
      drawOrder: [
        {
          offsets: [
            {
              slot: "Golden3",
              offset: -2,
            },
          ],
        },
      ],
    },
    Open_Idle3: {
      slots: {
        Anh_sang_close: {
          attachment: [
            {
              name: null,
            },
          ],
        },
        Bubble: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.4,
              color: "ffffffff",
            },
            {
              time: 0.7,
              color: "ffffff00",
            },
          ],
        },
        Bubble2: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.5667,
              color: "ffffff00",
            },
            {
              time: 1,
              color: "ffffffff",
            },
            {
              time: 1.4,
              color: "ffffff00",
            },
          ],
        },
        Bubble3: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.3,
              color: "ffffff00",
            },
            {
              time: 0.7333,
              color: "ffffffff",
            },
            {
              time: 1.1333,
              color: "ffffff00",
            },
          ],
        },
        Bubble4: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.7,
              color: "ffffff00",
            },
            {
              time: 1.1333,
              color: "ffffffff",
            },
            {
              time: 1.5333,
              color: "ffffff00",
            },
          ],
        },
        Bubble5: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.1333,
              color: "ffffff00",
            },
            {
              time: 0.5333,
              color: "ffffffff",
            },
            {
              time: 0.9333,
              color: "ffffff00",
            },
          ],
        },
        Bubble6: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.5333,
              color: "ffffff00",
            },
            {
              time: 0.9333,
              color: "ffffffff",
            },
            {
              time: 1.3333,
              color: "ffffff00",
            },
          ],
        },
        Bubble7: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.1,
              color: "ffffff00",
            },
            {
              time: 0.5333,
              color: "ffffffff",
            },
            {
              time: 0.9333,
              color: "ffffff00",
            },
          ],
        },
        "Frame 2071857771": {
          attachment: [
            {
              name: null,
            },
          ],
        },
        "Frame 2071857772": {
          attachment: [
            {
              name: "Frame 2071857773",
            },
          ],
        },
        Glow1: {
          rgba: [
            {
              color: "ffffff3e",
            },
            {
              time: 0.6333,
              color: "ffffffff",
            },
            {
              time: 1.2,
              color: "ffffffc0",
            },
            {
              time: 1.6667,
              color: "ffffff26",
            },
          ],
        },
        Glow2: {
          rgba: [
            {
              color: "ffffff6a",
            },
            {
              time: 0.5,
              color: "ffffffff",
              curve: "stepped",
            },
            {
              time: 0.8333,
              color: "ffffffff",
            },
            {
              time: 1.3667,
              color: "ffffff7b",
            },
          ],
        },
        Glow3: {
          rgba: [
            {
              color: "ffffffff",
              curve: "stepped",
            },
            {
              time: 0.4,
              color: "ffffffed",
            },
            {
              time: 1.0667,
              color: "ffffff8a",
            },
            {
              time: 1.6333,
              color: "ffffffff",
            },
          ],
        },
        Golden: {
          attachment: [
            {
              name: null,
            },
          ],
        },
        Nap_close: {
          attachment: [
            {
              name: "Nap_open",
            },
          ],
        },
        Sparkles: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.1,
              color: "ffffff00",
            },
            {
              time: 0.6667,
              color: "ffffffff",
            },
            {
              time: 1,
              color: "ffffff00",
            },
          ],
        },
        Sparkles2: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.5333,
              color: "ffffff00",
            },
            {
              time: 1.0333,
              color: "ffffffff",
            },
            {
              time: 1.4333,
              color: "ffffff00",
            },
          ],
        },
        Sparkles3: {
          rgba: [
            {
              color: "ffffff5c",
            },
            {
              time: 0.3667,
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.9,
              color: "ffffff00",
            },
            {
              time: 1.3,
              color: "ffffffff",
            },
            {
              time: 1.6667,
              color: "ffffff00",
            },
          ],
        },
        Sparkles4: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.7,
              color: "ffffff00",
            },
            {
              time: 1.1667,
              color: "ffffffff",
            },
            {
              time: 1.5,
              color: "ffffff00",
            },
          ],
        },
        Than_close: {
          attachment: [
            {
              name: "Than_open",
            },
          ],
        },
      },
      bones: {
        Anh_Sang: {
          scale: [
            {
              y: 0.963,
            },
          ],
        },
        Bong: {
          translate: [
            {
              y: 0.01,
            },
          ],
        },
        Bubble1: {
          translate: [
            {},
            {
              time: 0.7,
              y: 32.35,
            },
          ],
        },
        Bubble2: {
          translate: [
            {
              y: 0.01,
            },
            {
              time: 0.5667,
            },
            {
              time: 1.4,
              y: 32.35,
            },
          ],
        },
        Bubble3: {
          translate: [
            {},
            {
              time: 0.3,
            },
            {
              time: 1.1333,
              y: 32.35,
            },
          ],
        },
        Bubble4: {
          translate: [
            {
              time: 0.7,
            },
            {
              time: 1.5333,
              y: 32.35,
            },
          ],
        },
        Bubble5: {
          translate: [
            {},
            {
              time: 0.1333,
            },
            {
              time: 0.9333,
              y: 32.35,
            },
          ],
        },
        Bubble6: {
          translate: [
            {
              y: 0.01,
            },
            {
              time: 0.5333,
            },
            {
              time: 1.3333,
              y: 32.35,
            },
          ],
        },
        Bubble7: {
          translate: [
            {},
            {
              time: 0.1,
            },
            {
              time: 0.9333,
              y: 32.35,
            },
          ],
        },
        Nap: {
          translate: [
            {
              x: 1.5,
            },
          ],
        },
        Card: {
          rotate: [
            {
              value: 0.72,
            },
          ],
          translate: [
            {
              x: -15.18,
              y: 6,
            },
          ],
        },
        Card2: {
          rotate: [
            {
              value: 0.72,
            },
          ],
          translate: [
            {
              x: -16.63,
              y: -107.22,
            },
          ],
        },
        Card3: {
          rotate: [
            {
              value: 0.72,
            },
          ],
          translate: [
            {
              x: -14.06,
              y: -14.13,
            },
          ],
        },
        Than: {
          translate: [
            {
              x: 0.15,
              y: -0.95,
            },
          ],
        },
        Sparkle1: {
          rotate: [
            {
              value: 4.09,
            },
            {
              time: 0.1,
              value: 12.94,
            },
            {
              time: 1,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: -30.48,
              y: 217.9,
            },
          ],
        },
        Sparkle2: {
          rotate: [
            {},
            {
              time: 0.5333,
              value: 12.94,
            },
            {
              time: 1.4333,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: -129.03,
              y: 259.03,
            },
          ],
        },
        Sparkle3: {
          rotate: [
            {
              value: -2.73,
            },
            {
              time: 0.3667,
            },
            {
              time: 0.9,
              value: 12.94,
            },
            {
              time: 1.6667,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: 7.33,
              y: 48.22,
            },
          ],
        },
        Sparkle4: {
          rotate: [
            {},
            {
              time: 0.7,
              value: 12.94,
            },
            {
              time: 1.5,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: 177.32,
              y: 116.05,
            },
          ],
        },
        Card_Fly: {
          translate: [
            {
              x: 6.15,
              y: 320.55,
            },
          ],
        },
        bone: {
          translate: [
            {
              x: -0.15,
              y: -0.55,
            },
          ],
        },
      },
      deform: {
        default: {
          Mieng_open: {
            Mieng_open: [
              {
                offset: 1,
                vertices: [
                  -0.03851, 0.00952, -0.03851, 0.00952, -0.03851, 0.00952,
                  -0.03851,
                ],
              },
            ],
          },
          Nap_close: {
            Nap_open: [
              {
                vertices: [
                  -0.47011, -0.01443, -0.47011, -0.01443, -0.47011, -0.01443,
                  -0.47011, -0.01443,
                ],
              },
            ],
          },
        },
      },
      drawOrder: [
        {
          offsets: [
            {
              slot: "Golden3",
              offset: -2,
            },
          ],
        },
      ],
    },
    Open_Idle4: {
      slots: {
        Anh_sang_close: {
          attachment: [
            {
              name: null,
            },
          ],
        },
        Bubble: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.4,
              color: "ffffffff",
            },
            {
              time: 0.7,
              color: "ffffff00",
            },
          ],
        },
        Bubble2: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.5667,
              color: "ffffff00",
            },
            {
              time: 1,
              color: "ffffffff",
            },
            {
              time: 1.4,
              color: "ffffff00",
            },
          ],
        },
        Bubble3: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.3,
              color: "ffffff00",
            },
            {
              time: 0.7333,
              color: "ffffffff",
            },
            {
              time: 1.1333,
              color: "ffffff00",
            },
          ],
        },
        Bubble4: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.7,
              color: "ffffff00",
            },
            {
              time: 1.1333,
              color: "ffffffff",
            },
            {
              time: 1.5333,
              color: "ffffff00",
            },
          ],
        },
        Bubble5: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.1333,
              color: "ffffff00",
            },
            {
              time: 0.5333,
              color: "ffffffff",
            },
            {
              time: 0.9333,
              color: "ffffff00",
            },
          ],
        },
        Bubble6: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.5333,
              color: "ffffff00",
            },
            {
              time: 0.9333,
              color: "ffffffff",
            },
            {
              time: 1.3333,
              color: "ffffff00",
            },
          ],
        },
        Bubble7: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.1,
              color: "ffffff00",
            },
            {
              time: 0.5333,
              color: "ffffffff",
            },
            {
              time: 0.9333,
              color: "ffffff00",
            },
          ],
        },
        "Frame 2071857771": {
          attachment: [
            {
              name: null,
            },
          ],
        },
        Glow1: {
          rgba: [
            {
              color: "ffffff3e",
            },
            {
              time: 0.6333,
              color: "ffffffff",
            },
            {
              time: 1.2,
              color: "ffffffc0",
            },
            {
              time: 1.6667,
              color: "ffffff26",
            },
          ],
        },
        Glow2: {
          rgba: [
            {
              color: "ffffff6a",
            },
            {
              time: 0.5,
              color: "ffffffff",
              curve: "stepped",
            },
            {
              time: 0.8333,
              color: "ffffffff",
            },
            {
              time: 1.3667,
              color: "ffffff7b",
            },
          ],
        },
        Glow3: {
          rgba: [
            {
              color: "ffffffff",
              curve: "stepped",
            },
            {
              time: 0.4,
              color: "ffffffed",
            },
            {
              time: 1.0667,
              color: "ffffff8a",
            },
            {
              time: 1.6333,
              color: "ffffffff",
            },
          ],
        },
        Golden: {
          attachment: [
            {
              name: null,
            },
          ],
        },
        Nap_close: {
          attachment: [
            {
              name: "Nap_open",
            },
          ],
        },
        Sparkles: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.1,
              color: "ffffff00",
            },
            {
              time: 0.6667,
              color: "ffffffff",
            },
            {
              time: 1,
              color: "ffffff00",
            },
          ],
        },
        Sparkles2: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.5333,
              color: "ffffff00",
            },
            {
              time: 1.0333,
              color: "ffffffff",
            },
            {
              time: 1.4333,
              color: "ffffff00",
            },
          ],
        },
        Sparkles3: {
          rgba: [
            {
              color: "ffffff5c",
            },
            {
              time: 0.3667,
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.9,
              color: "ffffff00",
            },
            {
              time: 1.3,
              color: "ffffffff",
            },
            {
              time: 1.6667,
              color: "ffffff00",
            },
          ],
        },
        Sparkles4: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.7,
              color: "ffffff00",
            },
            {
              time: 1.1667,
              color: "ffffffff",
            },
            {
              time: 1.5,
              color: "ffffff00",
            },
          ],
        },
        Than_close: {
          attachment: [
            {
              name: "Than_open",
            },
          ],
        },
      },
      bones: {
        Anh_Sang: {
          scale: [
            {
              y: 0.963,
            },
          ],
        },
        Bong: {
          translate: [
            {
              y: 0.01,
            },
          ],
        },
        Bubble1: {
          translate: [
            {},
            {
              time: 0.7,
              y: 32.35,
            },
          ],
        },
        Bubble2: {
          translate: [
            {
              y: 0.01,
            },
            {
              time: 0.5667,
            },
            {
              time: 1.4,
              y: 32.35,
            },
          ],
        },
        Bubble3: {
          translate: [
            {},
            {
              time: 0.3,
            },
            {
              time: 1.1333,
              y: 32.35,
            },
          ],
        },
        Bubble4: {
          translate: [
            {
              time: 0.7,
            },
            {
              time: 1.5333,
              y: 32.35,
            },
          ],
        },
        Bubble5: {
          translate: [
            {},
            {
              time: 0.1333,
            },
            {
              time: 0.9333,
              y: 32.35,
            },
          ],
        },
        Bubble6: {
          translate: [
            {
              y: 0.01,
            },
            {
              time: 0.5333,
            },
            {
              time: 1.3333,
              y: 32.35,
            },
          ],
        },
        Bubble7: {
          translate: [
            {},
            {
              time: 0.1,
            },
            {
              time: 0.9333,
              y: 32.35,
            },
          ],
        },
        Nap: {
          translate: [
            {
              x: 1.5,
            },
          ],
        },
        Card: {
          rotate: [
            {
              value: 0.72,
            },
          ],
          translate: [
            {
              x: -15.18,
              y: 6,
            },
          ],
        },
        Card2: {
          rotate: [
            {
              value: 0.72,
            },
          ],
          translate: [
            {
              x: -16.63,
              y: -113.22,
            },
          ],
        },
        Card3: {
          rotate: [
            {
              value: 0.72,
            },
          ],
          translate: [
            {
              x: -14.06,
              y: -14.13,
            },
          ],
        },
        Than: {
          translate: [
            {
              x: 0.15,
              y: -0.95,
            },
          ],
        },
        Sparkle1: {
          rotate: [
            {
              value: 4.09,
            },
            {
              time: 0.1,
              value: 12.94,
            },
            {
              time: 1,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: -30.48,
              y: 217.9,
            },
          ],
        },
        Sparkle2: {
          rotate: [
            {},
            {
              time: 0.5333,
              value: 12.94,
            },
            {
              time: 1.4333,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: -129.03,
              y: 259.03,
            },
          ],
        },
        Sparkle3: {
          rotate: [
            {
              value: -2.73,
            },
            {
              time: 0.3667,
            },
            {
              time: 0.9,
              value: 12.94,
            },
            {
              time: 1.6667,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: 7.33,
              y: 48.22,
            },
          ],
        },
        Sparkle4: {
          rotate: [
            {},
            {
              time: 0.7,
              value: 12.94,
            },
            {
              time: 1.5,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: 177.32,
              y: 116.05,
            },
          ],
        },
        Card_Fly: {
          translate: [
            {
              x: 6.15,
              y: 320.55,
            },
          ],
        },
        bone: {
          translate: [
            {
              x: -0.15,
              y: -0.55,
            },
          ],
        },
      },
      deform: {
        default: {
          Mieng_open: {
            Mieng_open: [
              {
                offset: 1,
                vertices: [
                  -0.03851, 0.00952, -0.03851, 0.00952, -0.03851, 0.00952,
                  -0.03851,
                ],
              },
            ],
          },
          Nap_close: {
            Nap_open: [
              {
                vertices: [
                  -0.47011, -0.01443, -0.47011, -0.01443, -0.47011, -0.01443,
                  -0.47011, -0.01443,
                ],
              },
            ],
          },
        },
      },
      drawOrder: [
        {
          offsets: [
            {
              slot: "Golden3",
              offset: -2,
            },
          ],
        },
      ],
    },
    Open_Idle5: {
      slots: {
        Anh_sang_close: {
          attachment: [
            {
              name: null,
            },
          ],
        },
        Bubble: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.4,
              color: "ffffffff",
            },
            {
              time: 0.7,
              color: "ffffff00",
            },
          ],
        },
        Bubble2: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.5667,
              color: "ffffff00",
            },
            {
              time: 1,
              color: "ffffffff",
            },
            {
              time: 1.4,
              color: "ffffff00",
            },
          ],
        },
        Bubble3: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.3,
              color: "ffffff00",
            },
            {
              time: 0.7333,
              color: "ffffffff",
            },
            {
              time: 1.1333,
              color: "ffffff00",
            },
          ],
        },
        Bubble4: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.7,
              color: "ffffff00",
            },
            {
              time: 1.1333,
              color: "ffffffff",
            },
            {
              time: 1.5333,
              color: "ffffff00",
            },
          ],
        },
        Bubble5: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.1333,
              color: "ffffff00",
            },
            {
              time: 0.5333,
              color: "ffffffff",
            },
            {
              time: 0.9333,
              color: "ffffff00",
            },
          ],
        },
        Bubble6: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.5333,
              color: "ffffff00",
            },
            {
              time: 0.9333,
              color: "ffffffff",
            },
            {
              time: 1.3333,
              color: "ffffff00",
            },
          ],
        },
        Bubble7: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.1,
              color: "ffffff00",
            },
            {
              time: 0.5333,
              color: "ffffffff",
            },
            {
              time: 0.9333,
              color: "ffffff00",
            },
          ],
        },
        "Frame 2071857771": {
          attachment: [
            {
              name: null,
            },
          ],
        },
        "Frame 2071857772": {
          attachment: [
            {
              name: "Frame 2071857776",
            },
          ],
        },
        Glow1: {
          rgba: [
            {
              color: "ffffff3e",
            },
            {
              time: 0.6333,
              color: "ffffffff",
            },
            {
              time: 1.2,
              color: "ffffffc0",
            },
            {
              time: 1.6667,
              color: "ffffff26",
            },
          ],
        },
        Glow2: {
          rgba: [
            {
              color: "ffffff6a",
            },
            {
              time: 0.5,
              color: "ffffffff",
              curve: "stepped",
            },
            {
              time: 0.8333,
              color: "ffffffff",
            },
            {
              time: 1.3667,
              color: "ffffff7b",
            },
          ],
        },
        Glow3: {
          rgba: [
            {
              color: "ffffffff",
              curve: "stepped",
            },
            {
              time: 0.4,
              color: "ffffffed",
            },
            {
              time: 1.0667,
              color: "ffffff8a",
            },
            {
              time: 1.6333,
              color: "ffffffff",
            },
          ],
        },
        Golden: {
          attachment: [
            {
              name: null,
            },
          ],
        },
        Nap_close: {
          attachment: [
            {
              name: "Nap_open",
            },
          ],
        },
        Sparkles: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.1,
              color: "ffffff00",
            },
            {
              time: 0.6667,
              color: "ffffffff",
            },
            {
              time: 1,
              color: "ffffff00",
            },
          ],
        },
        Sparkles2: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.5333,
              color: "ffffff00",
            },
            {
              time: 1.0333,
              color: "ffffffff",
            },
            {
              time: 1.4333,
              color: "ffffff00",
            },
          ],
        },
        Sparkles3: {
          rgba: [
            {
              color: "ffffff5c",
            },
            {
              time: 0.3667,
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.9,
              color: "ffffff00",
            },
            {
              time: 1.3,
              color: "ffffffff",
            },
            {
              time: 1.6667,
              color: "ffffff00",
            },
          ],
        },
        Sparkles4: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.7,
              color: "ffffff00",
            },
            {
              time: 1.1667,
              color: "ffffffff",
            },
            {
              time: 1.5,
              color: "ffffff00",
            },
          ],
        },
        Than_close: {
          attachment: [
            {
              name: "Than_open",
            },
          ],
        },
      },
      bones: {
        Anh_Sang: {
          scale: [
            {
              y: 0.963,
            },
          ],
        },
        Bong: {
          translate: [
            {
              y: 0.01,
            },
          ],
        },
        Bubble1: {
          translate: [
            {},
            {
              time: 0.7,
              y: 32.35,
            },
          ],
        },
        Bubble2: {
          translate: [
            {
              y: 0.01,
            },
            {
              time: 0.5667,
            },
            {
              time: 1.4,
              y: 32.35,
            },
          ],
        },
        Bubble3: {
          translate: [
            {},
            {
              time: 0.3,
            },
            {
              time: 1.1333,
              y: 32.35,
            },
          ],
        },
        Bubble4: {
          translate: [
            {
              time: 0.7,
            },
            {
              time: 1.5333,
              y: 32.35,
            },
          ],
        },
        Bubble5: {
          translate: [
            {},
            {
              time: 0.1333,
            },
            {
              time: 0.9333,
              y: 32.35,
            },
          ],
        },
        Bubble6: {
          translate: [
            {
              y: 0.01,
            },
            {
              time: 0.5333,
            },
            {
              time: 1.3333,
              y: 32.35,
            },
          ],
        },
        Bubble7: {
          translate: [
            {},
            {
              time: 0.1,
            },
            {
              time: 0.9333,
              y: 32.35,
            },
          ],
        },
        Nap: {
          translate: [
            {
              x: 1.5,
            },
          ],
        },
        Card: {
          rotate: [
            {
              value: 0.72,
            },
          ],
          translate: [
            {
              x: -15.18,
              y: 6,
            },
          ],
        },
        Card2: {
          rotate: [
            {
              value: 0.72,
            },
          ],
          translate: [
            {
              x: -16.63,
              y: -107.22,
            },
          ],
        },
        Card3: {
          rotate: [
            {
              value: 0.72,
            },
          ],
          translate: [
            {
              x: -14.06,
              y: -14.13,
            },
          ],
        },
        Than: {
          translate: [
            {
              x: 0.15,
              y: -0.95,
            },
          ],
        },
        Sparkle1: {
          rotate: [
            {
              value: 4.09,
            },
            {
              time: 0.1,
              value: 12.94,
            },
            {
              time: 1,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: -30.48,
              y: 217.9,
            },
          ],
        },
        Sparkle2: {
          rotate: [
            {},
            {
              time: 0.5333,
              value: 12.94,
            },
            {
              time: 1.4333,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: -129.03,
              y: 259.03,
            },
          ],
        },
        Sparkle3: {
          rotate: [
            {
              value: -2.73,
            },
            {
              time: 0.3667,
            },
            {
              time: 0.9,
              value: 12.94,
            },
            {
              time: 1.6667,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: 7.33,
              y: 48.22,
            },
          ],
        },
        Sparkle4: {
          rotate: [
            {},
            {
              time: 0.7,
              value: 12.94,
            },
            {
              time: 1.5,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: 177.32,
              y: 116.05,
            },
          ],
        },
        Card_Fly: {
          translate: [
            {
              x: 6.15,
              y: 320.55,
            },
          ],
        },
        bone: {
          translate: [
            {
              x: -0.15,
              y: -0.55,
            },
          ],
        },
      },
      deform: {
        default: {
          Mieng_open: {
            Mieng_open: [
              {
                offset: 1,
                vertices: [
                  -0.03851, 0.00952, -0.03851, 0.00952, -0.03851, 0.00952,
                  -0.03851,
                ],
              },
            ],
          },
          Nap_close: {
            Nap_open: [
              {
                vertices: [
                  -0.47011, -0.01443, -0.47011, -0.01443, -0.47011, -0.01443,
                  -0.47011, -0.01443,
                ],
              },
            ],
          },
        },
      },
      drawOrder: [
        {
          offsets: [
            {
              slot: "Golden3",
              offset: -2,
            },
          ],
        },
      ],
    },
    Open_Idle6: {
      slots: {
        Anh_sang_close: {
          attachment: [
            {
              name: null,
            },
          ],
        },
        Bubble: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.4,
              color: "ffffffff",
            },
            {
              time: 0.7,
              color: "ffffff00",
            },
          ],
        },
        Bubble2: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.5667,
              color: "ffffff00",
            },
            {
              time: 1,
              color: "ffffffff",
            },
            {
              time: 1.4,
              color: "ffffff00",
            },
          ],
        },
        Bubble3: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.3,
              color: "ffffff00",
            },
            {
              time: 0.7333,
              color: "ffffffff",
            },
            {
              time: 1.1333,
              color: "ffffff00",
            },
          ],
        },
        Bubble4: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.7,
              color: "ffffff00",
            },
            {
              time: 1.1333,
              color: "ffffffff",
            },
            {
              time: 1.5333,
              color: "ffffff00",
            },
          ],
        },
        Bubble5: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.1333,
              color: "ffffff00",
            },
            {
              time: 0.5333,
              color: "ffffffff",
            },
            {
              time: 0.9333,
              color: "ffffff00",
            },
          ],
        },
        Bubble6: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.5333,
              color: "ffffff00",
            },
            {
              time: 0.9333,
              color: "ffffffff",
            },
            {
              time: 1.3333,
              color: "ffffff00",
            },
          ],
        },
        Bubble7: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.1,
              color: "ffffff00",
            },
            {
              time: 0.5333,
              color: "ffffffff",
            },
            {
              time: 0.9333,
              color: "ffffff00",
            },
          ],
        },
        "Frame 2071857771": {
          attachment: [
            {
              name: null,
            },
          ],
        },
        "Frame 2071857772": {
          attachment: [
            {
              name: "Frame 2071857774",
            },
          ],
        },
        Glow1: {
          rgba: [
            {
              color: "ffffff3e",
            },
            {
              time: 0.6333,
              color: "ffffffff",
            },
            {
              time: 1.2,
              color: "ffffffc0",
            },
            {
              time: 1.6667,
              color: "ffffff26",
            },
          ],
        },
        Glow2: {
          rgba: [
            {
              color: "ffffff6a",
            },
            {
              time: 0.5,
              color: "ffffffff",
              curve: "stepped",
            },
            {
              time: 0.8333,
              color: "ffffffff",
            },
            {
              time: 1.3667,
              color: "ffffff7b",
            },
          ],
        },
        Glow3: {
          rgba: [
            {
              color: "ffffffff",
              curve: "stepped",
            },
            {
              time: 0.4,
              color: "ffffffed",
            },
            {
              time: 1.0667,
              color: "ffffff8a",
            },
            {
              time: 1.6333,
              color: "ffffffff",
            },
          ],
        },
        Golden: {
          attachment: [
            {
              name: null,
            },
          ],
        },
        Nap_close: {
          attachment: [
            {
              name: "Nap_open",
            },
          ],
        },
        Sparkles: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.1,
              color: "ffffff00",
            },
            {
              time: 0.6667,
              color: "ffffffff",
            },
            {
              time: 1,
              color: "ffffff00",
            },
          ],
        },
        Sparkles2: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.5333,
              color: "ffffff00",
            },
            {
              time: 1.0333,
              color: "ffffffff",
            },
            {
              time: 1.4333,
              color: "ffffff00",
            },
          ],
        },
        Sparkles3: {
          rgba: [
            {
              color: "ffffff5c",
            },
            {
              time: 0.3667,
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.9,
              color: "ffffff00",
            },
            {
              time: 1.3,
              color: "ffffffff",
            },
            {
              time: 1.6667,
              color: "ffffff00",
            },
          ],
        },
        Sparkles4: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.7,
              color: "ffffff00",
            },
            {
              time: 1.1667,
              color: "ffffffff",
            },
            {
              time: 1.5,
              color: "ffffff00",
            },
          ],
        },
        Than_close: {
          attachment: [
            {
              name: "Than_open",
            },
          ],
        },
      },
      bones: {
        Anh_Sang: {
          scale: [
            {
              y: 0.963,
            },
          ],
        },
        Bong: {
          translate: [
            {
              y: 0.01,
            },
          ],
        },
        Bubble1: {
          translate: [
            {},
            {
              time: 0.7,
              y: 32.35,
            },
          ],
        },
        Bubble2: {
          translate: [
            {
              y: 0.01,
            },
            {
              time: 0.5667,
            },
            {
              time: 1.4,
              y: 32.35,
            },
          ],
        },
        Bubble3: {
          translate: [
            {},
            {
              time: 0.3,
            },
            {
              time: 1.1333,
              y: 32.35,
            },
          ],
        },
        Bubble4: {
          translate: [
            {
              time: 0.7,
            },
            {
              time: 1.5333,
              y: 32.35,
            },
          ],
        },
        Bubble5: {
          translate: [
            {},
            {
              time: 0.1333,
            },
            {
              time: 0.9333,
              y: 32.35,
            },
          ],
        },
        Bubble6: {
          translate: [
            {
              y: 0.01,
            },
            {
              time: 0.5333,
            },
            {
              time: 1.3333,
              y: 32.35,
            },
          ],
        },
        Bubble7: {
          translate: [
            {},
            {
              time: 0.1,
            },
            {
              time: 0.9333,
              y: 32.35,
            },
          ],
        },
        Nap: {
          translate: [
            {
              x: 1.5,
            },
          ],
        },
        Card: {
          rotate: [
            {
              value: 0.72,
            },
          ],
          translate: [
            {
              x: -15.18,
              y: 6,
            },
          ],
        },
        Card2: {
          rotate: [
            {
              value: 0.72,
            },
          ],
          translate: [
            {
              x: -16.63,
              y: -107.22,
            },
          ],
        },
        Card3: {
          rotate: [
            {
              value: 0.72,
            },
          ],
          translate: [
            {
              x: -14.06,
              y: -14.13,
            },
          ],
        },
        Than: {
          translate: [
            {
              x: 0.15,
              y: -0.95,
            },
          ],
        },
        Sparkle1: {
          rotate: [
            {
              value: 4.09,
            },
            {
              time: 0.1,
              value: 12.94,
            },
            {
              time: 1,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: -30.48,
              y: 217.9,
            },
          ],
        },
        Sparkle2: {
          rotate: [
            {},
            {
              time: 0.5333,
              value: 12.94,
            },
            {
              time: 1.4333,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: -129.03,
              y: 259.03,
            },
          ],
        },
        Sparkle3: {
          rotate: [
            {
              value: -2.73,
            },
            {
              time: 0.3667,
            },
            {
              time: 0.9,
              value: 12.94,
            },
            {
              time: 1.6667,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: 7.33,
              y: 48.22,
            },
          ],
        },
        Sparkle4: {
          rotate: [
            {},
            {
              time: 0.7,
              value: 12.94,
            },
            {
              time: 1.5,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: 177.32,
              y: 116.05,
            },
          ],
        },
        Card_Fly: {
          translate: [
            {
              x: 6.15,
              y: 320.55,
            },
          ],
        },
        bone: {
          translate: [
            {
              x: -0.15,
              y: -0.55,
            },
          ],
        },
      },
      deform: {
        default: {
          Mieng_open: {
            Mieng_open: [
              {
                offset: 1,
                vertices: [
                  -0.03851, 0.00952, -0.03851, 0.00952, -0.03851, 0.00952,
                  -0.03851,
                ],
              },
            ],
          },
          Nap_close: {
            Nap_open: [
              {
                vertices: [
                  -0.47011, -0.01443, -0.47011, -0.01443, -0.47011, -0.01443,
                  -0.47011, -0.01443,
                ],
              },
            ],
          },
        },
      },
      drawOrder: [
        {
          offsets: [
            {
              slot: "Golden3",
              offset: -2,
            },
          ],
        },
      ],
    },
    Open_Idle7: {
      slots: {
        Anh_sang_close: {
          attachment: [
            {
              name: null,
            },
          ],
        },
        Bubble: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.4,
              color: "ffffffff",
            },
            {
              time: 0.7,
              color: "ffffff00",
            },
          ],
        },
        Bubble2: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.5667,
              color: "ffffff00",
            },
            {
              time: 1,
              color: "ffffffff",
            },
            {
              time: 1.4,
              color: "ffffff00",
            },
          ],
        },
        Bubble3: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.3,
              color: "ffffff00",
            },
            {
              time: 0.7333,
              color: "ffffffff",
            },
            {
              time: 1.1333,
              color: "ffffff00",
            },
          ],
        },
        Bubble4: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.7,
              color: "ffffff00",
            },
            {
              time: 1.1333,
              color: "ffffffff",
            },
            {
              time: 1.5333,
              color: "ffffff00",
            },
          ],
        },
        Bubble5: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.1333,
              color: "ffffff00",
            },
            {
              time: 0.5333,
              color: "ffffffff",
            },
            {
              time: 0.9333,
              color: "ffffff00",
            },
          ],
        },
        Bubble6: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.5333,
              color: "ffffff00",
            },
            {
              time: 0.9333,
              color: "ffffffff",
            },
            {
              time: 1.3333,
              color: "ffffff00",
            },
          ],
        },
        Bubble7: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.1,
              color: "ffffff00",
            },
            {
              time: 0.5333,
              color: "ffffffff",
            },
            {
              time: 0.9333,
              color: "ffffff00",
            },
          ],
        },
        "Frame 2071857771": {
          attachment: [
            {
              name: null,
            },
          ],
        },
        "Frame 2071857772": {
          attachment: [
            {
              name: "Ticket_hoi_suc",
            },
          ],
        },
        Glow1: {
          rgba: [
            {
              color: "ffffff3e",
            },
            {
              time: 0.6333,
              color: "ffffffff",
            },
            {
              time: 1.2,
              color: "ffffffc0",
            },
            {
              time: 1.6667,
              color: "ffffff26",
            },
          ],
        },
        Glow2: {
          rgba: [
            {
              color: "ffffff6a",
            },
            {
              time: 0.5,
              color: "ffffffff",
              curve: "stepped",
            },
            {
              time: 0.8333,
              color: "ffffffff",
            },
            {
              time: 1.3667,
              color: "ffffff7b",
            },
          ],
        },
        Glow3: {
          rgba: [
            {
              color: "ffffffff",
              curve: "stepped",
            },
            {
              time: 0.4,
              color: "ffffffed",
            },
            {
              time: 1.0667,
              color: "ffffff8a",
            },
            {
              time: 1.6333,
              color: "ffffffff",
            },
          ],
        },
        Golden: {
          attachment: [
            {
              name: null,
            },
          ],
        },
        Nap_close: {
          attachment: [
            {
              name: "Nap_open",
            },
          ],
        },
        Sparkles: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.1,
              color: "ffffff00",
            },
            {
              time: 0.6667,
              color: "ffffffff",
            },
            {
              time: 1,
              color: "ffffff00",
            },
          ],
        },
        Sparkles2: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.5333,
              color: "ffffff00",
            },
            {
              time: 1.0333,
              color: "ffffffff",
            },
            {
              time: 1.4333,
              color: "ffffff00",
            },
          ],
        },
        Sparkles3: {
          rgba: [
            {
              color: "ffffff5c",
            },
            {
              time: 0.3667,
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.9,
              color: "ffffff00",
            },
            {
              time: 1.3,
              color: "ffffffff",
            },
            {
              time: 1.6667,
              color: "ffffff00",
            },
          ],
        },
        Sparkles4: {
          rgba: [
            {
              color: "ffffff00",
              curve: "stepped",
            },
            {
              time: 0.7,
              color: "ffffff00",
            },
            {
              time: 1.1667,
              color: "ffffffff",
            },
            {
              time: 1.5,
              color: "ffffff00",
            },
          ],
        },
        Than_close: {
          attachment: [
            {
              name: "Than_open",
            },
          ],
        },
      },
      bones: {
        Anh_Sang: {
          scale: [
            {
              y: 0.963,
            },
          ],
        },
        Bong: {
          translate: [
            {
              y: 0.01,
            },
          ],
        },
        Bubble1: {
          translate: [
            {},
            {
              time: 0.7,
              y: 32.35,
            },
          ],
        },
        Bubble2: {
          translate: [
            {
              y: 0.01,
            },
            {
              time: 0.5667,
            },
            {
              time: 1.4,
              y: 32.35,
            },
          ],
        },
        Bubble3: {
          translate: [
            {},
            {
              time: 0.3,
            },
            {
              time: 1.1333,
              y: 32.35,
            },
          ],
        },
        Bubble4: {
          translate: [
            {
              time: 0.7,
            },
            {
              time: 1.5333,
              y: 32.35,
            },
          ],
        },
        Bubble5: {
          translate: [
            {},
            {
              time: 0.1333,
            },
            {
              time: 0.9333,
              y: 32.35,
            },
          ],
        },
        Bubble6: {
          translate: [
            {
              y: 0.01,
            },
            {
              time: 0.5333,
            },
            {
              time: 1.3333,
              y: 32.35,
            },
          ],
        },
        Bubble7: {
          translate: [
            {},
            {
              time: 0.1,
            },
            {
              time: 0.9333,
              y: 32.35,
            },
          ],
        },
        Nap: {
          translate: [
            {
              x: 1.5,
            },
          ],
        },
        Card: {
          rotate: [
            {
              value: 0.72,
            },
          ],
          translate: [
            {
              x: -15.18,
              y: 6,
            },
          ],
        },
        Card2: {
          rotate: [
            {
              value: 0.72,
            },
          ],
          translate: [
            {
              x: -16.63,
              y: -107.22,
            },
          ],
        },
        Card3: {
          rotate: [
            {
              value: 0.72,
            },
          ],
          translate: [
            {
              x: -14.06,
              y: -14.13,
            },
          ],
        },
        Than: {
          translate: [
            {
              x: 0.15,
              y: -0.95,
            },
          ],
        },
        Sparkle1: {
          rotate: [
            {
              value: 4.09,
            },
            {
              time: 0.1,
              value: 12.94,
            },
            {
              time: 1,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: -30.48,
              y: 217.9,
            },
          ],
        },
        Sparkle2: {
          rotate: [
            {},
            {
              time: 0.5333,
              value: 12.94,
            },
            {
              time: 1.4333,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: -129.03,
              y: 259.03,
            },
          ],
        },
        Sparkle3: {
          rotate: [
            {
              value: -2.73,
            },
            {
              time: 0.3667,
            },
            {
              time: 0.9,
              value: 12.94,
            },
            {
              time: 1.6667,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: 7.33,
              y: 48.22,
            },
          ],
        },
        Sparkle4: {
          rotate: [
            {},
            {
              time: 0.7,
              value: 12.94,
            },
            {
              time: 1.5,
              value: -6.03,
            },
          ],
          translate: [
            {
              x: 177.32,
              y: 116.05,
            },
          ],
        },
        Card_Fly: {
          translate: [
            {
              x: 6.15,
              y: 320.55,
            },
          ],
        },
        bone: {
          translate: [
            {
              x: -0.15,
              y: -0.55,
            },
          ],
        },
      },
      deform: {
        default: {
          Mieng_open: {
            Mieng_open: [
              {
                offset: 1,
                vertices: [
                  -0.03851, 0.00952, -0.03851, 0.00952, -0.03851, 0.00952,
                  -0.03851,
                ],
              },
            ],
          },
          Nap_close: {
            Nap_open: [
              {
                vertices: [
                  -0.47011, -0.01443, -0.47011, -0.01443, -0.47011, -0.01443,
                  -0.47011, -0.01443,
                ],
              },
            ],
          },
        },
      },
      drawOrder: [
        {
          offsets: [
            {
              slot: "Golden3",
              offset: -2,
            },
          ],
        },
      ],
    },
  },
};

let rewardIdsMap = {
  7: [581],
  3: [601],
};

for (let key in rewardIdsMap) {
  let rewardIds = rewardIdsMap[key];

  for (let rewardId of rewardIds) {
    data.animations[`Open_Flip_Cards_${rewardId}`] =
      data.animations[`Open_Flip_Cards_${key}`];
    data.animations[`Open_Idle${rewardId}`] =
      data.animations[`Open_Idle${key}`];
  }
}

console.log(JSON.stringify(data));

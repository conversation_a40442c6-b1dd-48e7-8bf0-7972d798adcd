{"skeleton": {"hash": "hkvio36S+jk", "spine": "4.0.64", "x": -371.74, "y": -0.92, "width": 747.6, "height": 1242.71, "images": "./images/", "audio": ""}, "bones": [{"name": "root"}, {"name": "Setup", "parent": "root"}, {"name": "machine", "parent": "Setup", "length": 233.02, "rotation": 90, "x": 2, "y": 462}, {"name": "glow", "parent": "machine", "length": 75.95, "x": 276, "y": -1.5}, {"name": "machine_button", "parent": "machine", "x": -79.43, "y": -182.43}, {"name": "<PERSON><PERSON>", "parent": "machine_button", "length": 69.43, "x": 41.43, "y": 1.43}, {"name": "gift2", "parent": "glow", "x": 157.66, "y": -35.75}, {"name": "gift3", "parent": "glow", "x": 161.15, "y": 117.97}, {"name": "gift1", "parent": "glow", "x": 304.39, "y": -89.55}, {"name": "gift4", "parent": "glow", "x": 261.77, "y": 12.46}, {"name": "m2", "parent": "glow", "x": 235.59, "y": -109.83}, {"name": "m3", "parent": "glow", "x": 171.93, "y": -157.12}, {"name": "m1", "parent": "glow", "x": 371.21, "y": -12.8}, {"name": "Xu1", "parent": "glow", "x": 339.79, "y": 72.88}, {"name": "Xu2", "parent": "glow", "x": 275.12, "y": 144.63}, {"name": "machine2", "parent": "glow", "x": 252.5, "y": -123.27, "transform": "noScale"}, {"name": "machine3", "parent": "glow", "x": 348.35, "y": 100.41}, {"name": "machine4", "parent": "glow", "x": 261.47, "y": 38.64}, {"name": "machine5", "parent": "glow", "x": 263, "y": 155.45}, {"name": "machine6", "parent": "glow", "x": 195.3, "y": -113.14}, {"name": "Ball_pick", "parent": "Setup", "x": -169.41, "y": 511.32}, {"name": "Ball_pick_1", "parent": "Setup", "x": -169.41, "y": 511.32}, {"name": "m4", "parent": "glow", "x": 171.93, "y": -157.12}, {"name": "Xu3", "parent": "glow", "x": 275.12, "y": 144.63}, {"name": "Xu4", "parent": "glow", "x": 339.79, "y": 72.88}], "slots": [{"name": "Setup", "bone": "Setup", "attachment": "Setup"}, {"name": "<PERSON><PERSON>", "bone": "machine", "attachment": "<PERSON><PERSON>"}, {"name": "Machine", "bone": "machine", "attachment": "Machine"}, {"name": "Buttonset", "bone": "machine_button", "attachment": "Buttonset"}, {"name": "<PERSON><PERSON>", "bone": "<PERSON><PERSON>", "attachment": "<PERSON><PERSON>"}, {"name": "ball6", "bone": "machine2", "attachment": "ball6"}, {"name": "ball4", "bone": "machine3", "attachment": "ball4"}, {"name": "ball3", "bone": "machine6", "attachment": "ball3"}, {"name": "ball2", "bone": "machine5", "attachment": "ball2"}, {"name": "xu1", "bone": "Setup", "attachment": "xu1"}, {"name": "xu3", "bone": "Setup", "attachment": "xu1"}, {"name": "xu4", "bone": "Setup", "attachment": "xu1"}, {"name": "ball1", "bone": "machine4", "attachment": "ball1"}, {"name": "Gift4", "bone": "gift4", "attachment": "Gift4"}, {"name": "Gift3", "bone": "gift3", "attachment": "Gift3"}, {"name": "Gift2", "bone": "gift2", "attachment": "Gift2"}, {"name": "White_Light", "bone": "machine", "attachment": "Yellow_Light"}, {"name": "Gift2 copy", "bone": "Ball_pick", "attachment": "Gift2 copy"}, {"name": "GiftRewardMB", "bone": "Ball_pick_1", "attachment": "GiftRewardMB"}, {"name": "Gift1", "bone": "gift1", "attachment": "Gift1"}, {"name": "Xu2", "bone": "Xu2", "attachment": "Xu2"}, {"name": "Xu3", "bone": "Xu3", "attachment": "Xu2"}, {"name": "Xu1", "bone": "Xu1", "attachment": "Xu1"}, {"name": "Xu4", "bone": "Xu4", "attachment": "Xu1"}, {"name": "m3", "bone": "m3", "attachment": "m3"}, {"name": "m4", "bone": "m4", "attachment": "m3"}, {"name": "m2", "bone": "m2", "attachment": "m2"}, {"name": "m1", "bone": "m1", "attachment": "m1"}, {"name": "Glow", "bone": "glow", "attachment": "Glow"}, {"name": "Glow2", "bone": "glow", "attachment": "Glow"}, {"name": "Glow3", "bone": "glow", "attachment": "Glow"}, {"name": "gift2", "bone": "Setup", "attachment": "gift2"}, {"name": "ball", "bone": "Setup", "attachment": "gift2"}, {"name": "ball5", "bone": "Setup", "attachment": "gift2"}, {"name": "ball7", "bone": "Setup", "attachment": "gift2"}, {"name": "ball8", "bone": "Setup", "attachment": "gift2"}, {"name": "ball9", "bone": "Setup", "attachment": "gift2"}, {"name": "gift4", "bone": "Setup", "attachment": "gift4"}, {"name": "xu2", "bone": "Setup", "attachment": "xu2"}, {"name": "gift3", "bone": "Setup", "attachment": "gift3"}, {"name": "M1", "bone": "Setup", "attachment": "M1"}, {"name": "M2", "bone": "Setup", "attachment": "M1"}, {"name": "M4", "bone": "Setup", "attachment": "M1"}, {"name": "M3", "bone": "Setup", "attachment": "M1"}, {"name": "gift1", "bone": "Setup", "attachment": "gift1"}, {"name": "Asset 24Down", "bone": "Setup", "attachment": "Asset 24Down"}], "path": [{"name": "ball", "order": 12, "bones": ["machine2"], "target": "ball", "position": 0.75}, {"name": "ball5", "order": 13, "bones": ["machine3"], "target": "ball5", "position": 0.425}, {"name": "ball7", "order": 14, "bones": ["machine5"], "target": "ball7", "position": 0.2833}, {"name": "ball8", "order": 15, "bones": ["machine6"], "target": "ball8", "position": -0.1583}, {"name": "ball9", "order": 16, "bones": ["machine4"], "target": "ball9", "position": 0.2667}, {"name": "gift1", "order": 3, "bones": ["gift1"], "target": "gift1", "rotation": 117.29, "position": 0.5}, {"name": "gift2", "bones": ["gift2"], "target": "gift2", "rotation": -121.94, "mixRotate": 0.7255}, {"name": "gift3", "order": 1, "bones": ["gift3"], "target": "gift3", "rotation": -68.52, "position": 0.0357}, {"name": "gift4", "order": 2, "bones": ["gift4"], "target": "gift4", "rotation": -77.81}, {"name": "M1", "order": 9, "bones": ["m1"], "target": "M1", "position": 0.1607}, {"name": "M2", "order": 10, "bones": ["m2"], "target": "xu3", "position": 0.8571}, {"name": "M3", "order": 11, "bones": ["m3"], "target": "M3"}, {"name": "M4", "order": 8, "bones": ["Xu4"], "target": "M4", "position": 0.75}, {"name": "xu1", "order": 4, "bones": ["Xu1"], "target": "xu1", "position": 0.175}, {"name": "xu2", "order": 5, "bones": ["Xu2"], "target": "xu2"}, {"name": "xu3", "order": 7, "bones": ["Xu3"], "target": "M2", "position": 0.575}, {"name": "xu4", "order": 6, "bones": ["m4"], "target": "xu4", "position": 0.3417}], "skins": [{"name": "default", "attachments": {"Asset 24Down": {"Asset 24Down": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [376.57, 0.88, -373.43, 0.88, -373.43, 338.88, 376.57, 338.88], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 750, "height": 338}}, "ball": {"gift2": {"type": "path", "closed": true, "lengths": [250.69, 486.6, 600.5, 741.45], "vertexCount": 12, "vertices": [112.83, 891.89, 35.83, 875.27, -33.23, 860.37, -131.65, 922.12, -141.19, 1017.6, -150.68, 1112.67, -22.49, 1019.5, 60.22, 1009.78, 146.15, 999.69, 143.42, 971.48, 142.31, 951.27, 140.25, 913.84]}}, "ball1": {"ball1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-79.6, -76.61, -79.6, 66.39, 63.4, 66.39, 63.4, -76.61], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 143, "height": 143}}, "ball2": {"ball2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-76.81, -72.08, -76.81, 70.92, 66.19, 70.92, 66.19, -72.08], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 143, "height": 143}}, "ball3": {"ball3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-81.92, -69.94, -81.92, 73.06, 61.08, 73.06, 61.08, -69.94], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 143, "height": 143}}, "ball4": {"ball4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-73.96, -77.99, -73.96, 65.01, 69.04, 65.01, 69.04, -77.99], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 143, "height": 143}}, "ball5": {"gift2": {"type": "path", "closed": true, "lengths": [247.65, 461.39, 644.86, 792.55], "vertexCount": 12, "vertices": [111.2, 891.89, 34.19, 875.27, -34.86, 860.37, -129.2, 922.12, -138.74, 1017.6, -148.24, 1112.67, -54.3, 1088.82, 28.42, 1079.1, 114.34, 1069.01, 148.31, 974.74, 147.2, 954.53, 145.14, 917.1]}}, "ball6": {"ball6": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-82.71, -68.15, -82.71, 74.85, 60.29, 74.85, 60.29, -68.15], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 143, "height": 143}}, "ball7": {"gift2": {"type": "path", "closed": true, "lengths": [232.26, 469.14, 676.96, 848.52], "vertexCount": 12, "vertices": [94.93, 870.19, 18.83, 868.04, -51.79, 866.04, -141.86, 896.82, -151.4, 992.29, -160.89, 1087.36, -54.3, 1099.67, 28.42, 1089.95, 114.34, 1079.86, 162.77, 999.15, 158.05, 949.11, 154.52, 911.79]}}, "ball8": {"gift2": {"type": "path", "closed": true, "lengths": [232.26, 469.14, 655.22, 799.73], "vertexCount": 12, "vertices": [94.93, 870.19, 18.83, 868.04, -51.79, 866.04, -141.86, 896.82, -151.4, 992.29, -160.89, 1087.36, -54.3, 1099.67, 28.42, 1089.95, 114.34, 1079.86, 124.44, 1001.03, 119.71, 950.99, 116.18, 913.67]}}, "ball9": {"gift2": {"type": "path", "closed": true, "lengths": [114.81, 254.66, 393.52, 575.15], "vertexCount": 12, "vertices": [142.6, 878.51, 18.83, 868.04, -12.21, 865.42, -25.78, 905.58, -33.12, 960.17, -45.87, 1054.85, -2.28, 1053.76, 32.57, 1057.39, 94.65, 1063.85, 123.66, 1037.7, 124.77, 981.83, 125.52, 944.35]}}, "Button": {"Button": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-59.46, -113.56, -59.46, 124.44, 114.54, 124.44, 114.54, -113.56], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 238, "height": 174}}, "Buttonset": {"Buttonset": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-32.55, -111.73, -32.55, 126.27, 78.45, 126.27, 78.45, -111.73], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 238, "height": 111}}, "Gift1": {"Gift1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-62.59, -68.02, -62.59, 77.98, 86.41, 77.98, 86.41, -68.02], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 146, "height": 149}}, "gift1": {"gift1": {"type": "path", "closed": true, "lengths": [158.82, 370.34, 484.49, 658.24], "vertexCount": 12, "vertices": [23.14, 947.88, -93.34, 887.34, -125.73, 870.5, -122.82, 962.31, -97.03, 1030.3, -66.7, 1110.29, 39.79, 1092.3, 62.9, 1012.29, 73.92, 974.16, 93.66, 906.96, 73.88, 902.63, 16.47, 890.04]}}, "Gift2": {"Gift2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-77.87, -83.82, -77.87, 95.18, 91.13, 95.18, 91.13, -83.82], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 179, "height": 169}}, "gift2": {"gift2": {"type": "path", "closed": true, "lengths": [250.69, 506.78, 623.54, 764.49], "vertexCount": 12, "vertices": [112.83, 891.89, 35.83, 875.27, -33.23, 860.37, -131.65, 922.12, -141.19, 1017.6, -150.68, 1112.67, 0.4, 1037.53, 83.11, 1027.82, 169.04, 1017.72, 143.42, 971.48, 142.31, 951.27, 140.25, 913.84]}}, "Gift2 copy": {"Gift2 copy": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [86.77, -85.11, -92.19, -81.36, -88.65, 87.6, 90.31, 83.85], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 315, "height": 318}}, "GiftRewardMB": {"GiftRewardMB": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [86.77, -85.11, -92.19, -81.36, -88.65, 87.6, 90.31, 83.85], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 315, "height": 318}}, "Gift3": {"Gift3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-68.36, -78.53, -68.36, 72.47, 87.64, 72.47, 87.64, -78.53], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 151, "height": 156}}, "gift3": {"gift3": {"type": "path", "closed": true, "lengths": [174.05, 450.74, 645.31, 882.01], "vertexCount": 12, "vertices": [23.14, 947.88, -93.34, 887.34, -125.73, 870.5, -173.58, 963.81, -147.79, 1031.8, -117.46, 1111.79, 29.78, 1131.26, 97.72, 1083.09, 151, 1045.31, 150.75, 905.75, 138.26, 900.69, 83.8, 878.58]}}, "Gift4": {"Gift4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-72.97, -75.03, -72.97, 72.97, 75.03, 72.97, 75.03, -75.03], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 348, "height": 350}}, "gift4": {"gift4": {"type": "path", "closed": true, "lengths": [117.12, 237.83, 344.76, 482.16], "vertexCount": 12, "vertices": [28.05, 943.89, -10.15, 955.59, -45.77, 966.51, -59.79, 996.27, -52.04, 1053.45, -47.12, 1089.71, -23.13, 1139.54, 23.89, 1130.57, 78.08, 1120.24, 82.78, 1069.73, 75.49, 1050.84, 58.46, 1006.67]}}, "Glow": {"Glow": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-35.21, -260.56, -35.21, 265.44, 503.79, 265.44, 503.79, -260.56], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 526, "height": 539}}, "Glow2": {"Glow": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-35.21, -260.56, -35.21, 265.44, 503.79, 265.44, 503.79, -260.56], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 526, "height": 539}}, "Glow3": {"Glow": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-35.21, -260.56, -35.21, 265.44, 503.79, 265.44, 503.79, -260.56], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 526, "height": 539}}, "M1": {"M1": {"type": "path", "closed": true, "lengths": [174.05, 450.74, 647.25, 884.13], "vertexCount": 12, "vertices": [23.14, 947.88, -93.34, 887.34, -125.73, 870.5, -173.58, 963.81, -147.79, 1031.8, -117.46, 1111.79, 29.78, 1131.26, 97.72, 1083.09, 151, 1045.31, 158.04, 905.02, 138.26, 900.69, 80.85, 888.1]}}, "m1": {"m1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-41.02, -39.83, -41.02, 43.17, 39.98, 43.17, 39.98, -39.83], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 367, "height": 370}}, "M2": {"M1": {"type": "path", "closed": true, "lengths": [209.95, 475.49, 656.81, 791.5], "vertexCount": 12, "vertices": [73.95, 955.14, -42.52, 894.6, -74.92, 877.76, -156.64, 996.88, -130.85, 1064.87, -100.52, 1144.86, 36.23, 1155.45, 104.18, 1107.29, 157.45, 1069.51, 101.57, 946.96, 81.8, 942.63, 24.38, 930.04]}}, "m2": {"m2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-42.8, -42.76, -42.8, 37.24, 40.2, 37.24, 40.2, -42.76], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 367, "height": 370}}, "M3": {"M1": {"type": "path", "closed": true, "lengths": [311.84, 541.3, 680.99, 847.61], "vertexCount": 12, "vertices": [150.14, 894.32, 125.09, 878.75, 94.08, 859.47, -86, 792.2, -150.14, 931.38, -185.94, 1009.08, -71.38, 1082.55, -3.44, 1034.38, 49.84, 996.61, 118.05, 1049.03, 132.3, 1034.66, 170.81, 995.84]}}, "m3": {"m3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-40.13, -41.44, -40.13, 42.56, 38.87, 42.56, 38.87, -41.44], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 367, "height": 370}}, "M4": {"M1": {"type": "path", "closed": true, "lengths": [209.95, 475.49, 674.08, 855.45], "vertexCount": 12, "vertices": [73.95, 955.14, -42.52, 894.6, -74.92, 877.76, -156.64, 996.88, -130.85, 1064.87, -100.52, 1144.86, 36.23, 1155.45, 104.18, 1107.29, 157.45, 1069.51, 173.14, 967.69, 131.06, 928.65, 87.96, 888.67]}}, "m4": {"m3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-40.13, -41.44, -40.13, 42.56, 38.87, 42.56, 38.87, -41.44], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 367, "height": 370}}, "Machine": {"Machine": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-462.92, -375.23, -462.92, 376.77, 351.08, 376.77, 351.08, -375.23], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 752, "height": 814}}, "Setup": {"Setup": {"type": "clipping", "end": "Setup", "vertexCount": 4, "vertices": [-373.88, 1624.09, -369.61, 0.26, 375.5, -1.92, 376.2, 1624.05], "color": "ce3a3aff"}}, "Tube": {"Tube": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 0, 1, 3], "vertices": [94.08, 91.77, 94.08, 319.77, 575.08, 319.77, 575.08, 91.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 228, "height": 481}}, "White_Light": {"White_Light": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [8.07, 76.62, 8.07, 267.62, 175.07, 267.62, 175.07, 76.62], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 191, "height": 167}, "Yellow_Light": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [8.34, 76.62, 8.34, 267.62, 176.34, 267.62, 176.34, 76.62], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 191, "height": 168}}, "Xu1": {"Xu1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-41.68, -39.53, -41.68, 40.47, 37.32, 40.47, 37.32, -39.53], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 367, "height": 391}}, "xu1": {"xu1": {"type": "path", "closed": true, "lengths": [174.01, 372.69, 636.91, 944.68], "vertexCount": 12, "vertices": [-118.13, 851.48, -159.69, 916.75, -179.3, 947.54, -180.28, 1025.81, -130.69, 1079, -87.36, 1125.47, -27.39, 1132.75, 55.4, 1123.7, 138.09, 1114.65, 157.16, 894.79, 137.38, 890.46, 79.97, 877.87]}}, "Xu2": {"Xu2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-44.34, -45.28, -44.34, 41.72, 45.66, 41.72, 45.66, -45.28], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 367, "height": 391}}, "xu2": {"xu2": {"type": "path", "closed": true, "lengths": [113.2, 246.41, 404.76, 574.76], "vertexCount": 12, "vertices": [-75.85, 954.71, -114.05, 966.42, -149.67, 977.33, -158.18, 1011.83, -129.06, 1061.65, -113.51, 1088.25, -64.21, 1130.6, -17.2, 1121.64, 37, 1111.3, 62.36, 1050.29, 43.96, 994.68, 29.09, 949.74]}}, "Xu3": {"Xu2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-44.34, -45.28, -44.34, 41.72, 45.66, 41.72, 45.66, -45.28], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 367, "height": 391}}, "xu3": {"xu1": {"type": "path", "closed": true, "lengths": [204.42, 407.7, 669.55, 826.77], "vertexCount": 12, "vertices": [-12.69, 857.96, -54.25, 923.23, -73.85, 954.02, -166.47, 1050.96, -116.88, 1104.15, -73.55, 1150.62, -3.81, 1110.81, 78.98, 1101.76, 161.67, 1092.71, 95.89, 866.15, 76.12, 861.81, 18.7, 849.23]}}, "Xu4": {"Xu1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-41.68, -39.53, -41.68, 40.47, 37.32, 40.47, 37.32, -39.53], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 367, "height": 391}}, "xu4": {"xu1": {"type": "path", "closed": true, "lengths": [194.29, 401.03, 598.59, 820.81], "vertexCount": 12, "vertices": [-45.65, 858.75, -87.2, 924.01, -106.81, 954.81, -166.47, 1050.96, -116.88, 1104.15, -73.55, 1150.62, -0.68, 1129.65, 82.12, 1120.59, 164.8, 1111.54, 164.95, 1012.11, 109.86, 967.75, 64.08, 930.89]}}}}], "animations": {"1_Spin": {"slots": {"Glow2": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00", "curve": [0.278, 1, 0.389, 1, 0.278, 1, 0.389, 1, 0.278, 1, 0.389, 1, 0.278, 0.33, 0.389, 1]}, {"time": 0.5, "color": "ffffffff", "curve": [0.611, 1, 0.722, 1, 0.611, 1, 0.722, 1, 0.611, 1, 0.722, 1, 0.611, 1, 0.722, 0.34]}, {"time": 0.8333, "color": "ffffff56", "curve": [0.944, 1, 1.056, 1, 0.944, 1, 1.056, 1, 0.944, 1, 1.056, 1, 0.944, 0.34, 1.056, 1]}, {"time": 1.1667, "color": "ffffffff", "curve": [1.278, 1, 1.389, 1, 1.278, 1, 1.389, 1, 1.278, 1, 1.389, 1, 1.278, 1, 1.389, 0.34]}, {"time": 1.5, "color": "ffffff56", "curve": [1.611, 1, 1.722, 1, 1.611, 1, 1.722, 1, 1.611, 1, 1.722, 1, 1.611, 0.34, 1.722, 1]}, {"time": 1.8333, "color": "ffffffff", "curve": [1.944, 1, 2.056, 1, 1.944, 1, 2.056, 1, 1.944, 1, 2.056, 1, 1.944, 1, 2.056, 0.34]}, {"time": 2.1667, "color": "ffffff56", "curve": [2.278, 1, 2.389, 1, 2.278, 1, 2.389, 1, 2.278, 1, 2.389, 1, 2.278, 0.34, 2.389, 1]}, {"time": 2.5, "color": "ffffffff", "curve": [2.611, 1, 2.722, 1, 2.611, 1, 2.722, 1, 2.611, 1, 2.722, 1, 2.611, 1, 2.722, 0.52]}, {"time": 2.8333, "color": "ffffff56", "curve": [2.933, 1, 3.033, 1, 2.933, 1, 3.033, 1, 2.933, 1, 3.033, 1, 2.933, 0.18, 3.033, 0]}, {"time": 3.1333, "color": "ffffff00"}]}, "Glow3": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00", "curve": [0.278, 1, 0.389, 1, 0.278, 1, 0.389, 1, 0.278, 1, 0.389, 1, 0.278, 0.33, 0.389, 1]}, {"time": 0.5, "color": "ffffffff", "curve": [0.611, 1, 0.722, 1, 0.611, 1, 0.722, 1, 0.611, 1, 0.722, 1, 0.611, 1, 0.722, 0.34]}, {"time": 0.8333, "color": "ffffff56", "curve": [0.944, 1, 1.056, 1, 0.944, 1, 1.056, 1, 0.944, 1, 1.056, 1, 0.944, 0.34, 1.056, 1]}, {"time": 1.1667, "color": "ffffffff", "curve": [1.278, 1, 1.389, 1, 1.278, 1, 1.389, 1, 1.278, 1, 1.389, 1, 1.278, 1, 1.389, 0.34]}, {"time": 1.5, "color": "ffffff56", "curve": [1.611, 1, 1.722, 1, 1.611, 1, 1.722, 1, 1.611, 1, 1.722, 1, 1.611, 0.34, 1.722, 1]}, {"time": 1.8333, "color": "ffffffff", "curve": [1.944, 1, 2.056, 1, 1.944, 1, 2.056, 1, 1.944, 1, 2.056, 1, 1.944, 1, 2.056, 0.34]}, {"time": 2.1667, "color": "ffffff56", "curve": [2.278, 1, 2.389, 1, 2.278, 1, 2.389, 1, 2.278, 1, 2.389, 1, 2.278, 0.34, 2.389, 1]}, {"time": 2.5, "color": "ffffffff", "curve": [2.611, 1, 2.722, 1, 2.611, 1, 2.722, 1, 2.611, 1, 2.722, 1, 2.611, 1, 2.722, 0.52]}, {"time": 2.8333, "color": "ffffff56", "curve": [2.933, 1, 3.033, 1, 2.933, 1, 3.033, 1, 2.933, 1, 3.033, 1, 2.933, 0.18, 3.033, 0]}, {"time": 3.1333, "color": "ffffff00"}]}, "White_Light": {"attachment": [{"name": null}]}}, "bones": {"Ball_pick": {"scale": [{"time": 0, "x": 0, "y": 0}]}, "Ball_pick_1": {"scale": [{"time": 0, "x": 0, "y": 0}]}, "Button": {"translate": [{"curve": [0.056, -3.02, 0.111, -9.05, 0.056, 0, 0.111, 0]}, {"time": 0.1667, "x": -9.05, "curve": [0.233, -9.05, 0.3, 0, 0.233, 0, 0.3, 0]}, {"time": 0.3667}], "scale": [{"curve": [0.056, 0.927, 0.111, 0.78, 0.056, 1, 0.111, 1]}, {"time": 0.1667, "x": 0.78, "curve": [0.233, 0.78, 0.3, 1, 0.233, 1, 0.3, 1]}, {"time": 0.3667}]}, "glow": {"scale": [{"time": 0.1667, "curve": [0.244, 1.016, 0.322, 1.041, 0.244, 1, 0.322, 1]}, {"time": 0.4, "x": 1.024, "curve": [0.478, 1.008, 0.556, 1, 0.478, 1, 0.556, 1]}, {"time": 0.6333, "curve": [0.711, 1, 0.789, 1.024, 0.711, 1, 0.789, 1]}, {"time": 0.8667, "x": 1.024, "curve": [0.944, 1.024, 1.022, 1, 0.944, 1, 1.022, 1]}, {"time": 1.1, "curve": [1.178, 1, 1.256, 1.041, 1.178, 1, 1.256, 1]}, {"time": 1.3333, "x": 1.024, "curve": [1.411, 1.008, 1.489, 1, 1.411, 1, 1.489, 1]}, {"time": 1.5667, "curve": [1.644, 1, 1.722, 1.041, 1.644, 1, 1.722, 1]}, {"time": 1.8, "x": 1.024, "curve": [1.878, 1.008, 1.956, 1, 1.878, 1, 1.956, 1]}, {"time": 2.0333, "curve": [2.111, 1, 2.189, 1.041, 2.111, 1, 2.189, 1]}, {"time": 2.2667, "x": 1.024, "curve": [2.344, 1.008, 2.422, 0.984, 2.344, 1, 2.422, 1]}, {"time": 2.5, "curve": [2.578, 1.016, 2.656, 1.024, 2.578, 1, 2.656, 1]}, {"time": 2.7333, "x": 1.024}, {"time": 3}]}, "machine": {"scale": [{"time": 0.1667, "curve": [0.244, 1.016, 0.322, 1.041, 0.244, 1, 0.322, 1]}, {"time": 0.4, "x": 1.024, "curve": [0.478, 1.008, 0.556, 1, 0.478, 1, 0.556, 1]}, {"time": 0.6333, "curve": [0.711, 1, 0.789, 1.024, 0.711, 1, 0.789, 1]}, {"time": 0.8667, "x": 1.024, "curve": [0.944, 1.024, 1.022, 1, 0.944, 1, 1.022, 1]}, {"time": 1.1, "curve": [1.178, 1, 1.256, 1.041, 1.178, 1, 1.256, 1]}, {"time": 1.3333, "x": 1.024, "curve": [1.411, 1.008, 1.489, 1, 1.411, 1, 1.489, 1]}, {"time": 1.5667, "curve": [1.644, 1, 1.722, 1.041, 1.644, 1, 1.722, 1]}, {"time": 1.8, "x": 1.024, "curve": [1.878, 1.008, 1.956, 1, 1.878, 1, 1.956, 1]}, {"time": 2.0333, "curve": [2.111, 1, 2.189, 1.041, 2.111, 1, 2.189, 1]}, {"time": 2.2667, "x": 1.024, "curve": [2.344, 1.008, 2.422, 0.984, 2.344, 1, 2.422, 1]}, {"time": 2.5, "curve": [2.578, 1.016, 2.656, 1.024, 2.578, 1, 2.656, 1]}, {"time": 2.7333, "x": 1.024}, {"time": 3}]}}, "path": {"M1": {"position": [{"time": 0.2, "value": 0.1607, "curve": [1.156, 1.1071, 2.111, 3]}, {"time": 3.0667, "value": 3}]}, "M2": {"position": [{"time": 0.2, "value": 0.8571, "curve": [1.044, 1.5047, 1.889, 2.4545]}, {"time": 2.7333, "value": 2.8, "curve": [2.856, 2.85, 2.978, 2.85]}, {"time": 3.1, "value": 2.85}], "mix": [{"time": 2.3333}, {"time": 2.7333, "mixRotate": 0}]}, "M3": {"position": [{"time": 0.2, "curve": [0.933, -0.95, 1.667, -2.19]}, {"time": 2.4, "value": -2.85, "curve": [2.567, -3, 2.8, -2.9505]}, {"time": 2.9, "value": -3, "curve": [2.978, -3.0385, 3.056, -3]}, {"time": 3.1333, "value": -3}]}, "M4": {"position": [{"time": 0.2, "value": 0.75, "curve": [0.989, 1.6667, 1.778, 2.6839]}, {"time": 2.5667, "value": 3.5, "curve": [2.744, 3.6839, 2.922, 3.75]}, {"time": 3.1, "value": 3.75}]}, "ball": {"position": [{"time": 0.2, "value": 0.75, "curve": [1.022, 1.7667, 1.844, 3.8]}, {"time": 2.6667, "value": 3.8}]}, "ball5": {"position": [{"time": 0.2, "value": 0.425, "curve": [1.067, -0.9333, 1.933, -2.3722]}, {"time": 2.8, "value": -3.65, "curve": [2.911, -3.8138, 3.022, -3.9]}, {"time": 3.1333, "value": -3.9}]}, "ball7": {"position": [{"time": 0.2, "value": 0.2833, "curve": [1.044, 1.5222, 1.889, 2.8581]}, {"time": 2.7333, "value": 4, "curve": [2.867, 4.1803, 3.044, 4.25]}, {"time": 3.1333, "value": 4.25}]}, "ball8": {"position": [{"time": 0.2, "value": -0.1583, "curve": [1.033, -1.1055, 1.867, -2.1501]}, {"time": 2.7, "value": -3, "curve": [2.844, -3.1473, 2.989, -3.15]}, {"time": 3.1333, "value": -3.15}]}, "ball9": {"position": [{"time": 0.2, "value": 0.2667, "curve": [1.176, 1.7592, 1.966, 2.6138]}, {"time": 2.4333, "value": 3, "curve": [2.688, 3.2049, 2.849, 3.25]}, {"time": 2.9, "value": 3.25}]}, "gift1": {"position": [{"time": 0.2, "value": 0.5, "curve": [1.011, 1.4333, 1.822, 2.4152]}, {"time": 2.6333, "value": 3.3, "curve": [2.8, 3.4818, 2.978, 3.7]}, {"time": 3.1333, "value": 3.7}]}, "gift2": {"position": [{"time": 0.2, "curve": [0.911, 1.1667, 1.622, 2.4841]}, {"time": 2.3333, "value": 3.5, "curve": [2.556, 3.8175, 2.778, 4]}, {"time": 3, "value": 4}]}, "gift3": {"position": [{"time": 0.2, "value": 0.0357, "curve": [0.967, -1.1429, 1.733, -2.4188]}, {"time": 2.5, "value": -3.5, "curve": [2.678, -3.7507, 2.856, -3.96]}, {"time": 3.0333, "value": -3.96}], "mix": [{"time": 0.2}, {"time": 2.5, "mixRotate": 0}, {"time": 3.1}]}, "gift4": {"position": [{"time": 0.2}, {"time": 2.6333, "value": 3.7}, {"time": 2.9667, "value": 4}], "mix": [{"time": 2.3333}, {"time": 2.6, "mixRotate": 0}]}, "xu1": {"position": [{"value": 0.1809, "curve": "stepped"}, {"time": 0.2, "value": 0.1809, "curve": [1.022, 2.1809, 2.311, 3]}, {"time": 3.1333, "value": 3}]}, "xu2": {"position": [{"time": 0.2, "curve": [1, 1.1667, 1.8, 2.3966]}, {"time": 2.6, "value": 3.5, "curve": [2.767, 3.7299, 2.933, 4]}, {"time": 3.1, "value": 4}]}, "xu3": {"position": [{"time": 0.2, "value": 0.575, "curve": [1.078, 1.5833, 1.955, 2.9416]}, {"time": 2.8333, "value": 3.6, "curve": [2.922, 3.6667, 3.011, 3.7]}, {"time": 3.1, "value": 3.7}]}, "xu4": {"position": [{"time": 0.2, "value": 0.3417, "curve": [0.978, 1.4945, 1.756, 2.7038]}, {"time": 2.5333, "value": 3.8, "curve": [2.644, 3.9566, 2.756, 4.1]}, {"time": 2.8667, "value": 4.1}]}}}, "2_Roll": {"slots": {"Glow2": {"rgba": [{"color": "ffffff00"}]}, "Glow3": {"rgba": [{"color": "ffffff00"}]}, "White_Light": {"attachment": [{"name": null}]}}, "bones": {"Ball_pick": {"rotate": [{"curve": [0.067, 32.79, 0.2, 15.97]}, {"time": 0.2667, "value": 49.19, "curve": [0.422, 126.71, 0.744, 134.41]}, {"time": 0.9, "value": 165.48, "curve": [0.989, 183.23, 1.144, 192.1]}, {"time": 1.2333, "value": 192.1}], "translate": [{"x": -56.23, "y": 498.23, "curve": [0.111, -56.23, 0.222, -107.49, 0.111, 498.23, 0.222, 498.23]}, {"time": 0.3333, "x": -107.49, "y": 487.08, "curve": [0.522, -107.49, 0.711, -107.49, 0.522, 468.14, 0.711, 80.89]}, {"time": 0.9, "x": -107.49, "y": 80.89, "curve": [1.011, -107.49, 1.122, 0.2, 1.011, 80.89, 1.122, 80.89]}, {"time": 1.2333, "x": 0.2, "y": 80.89}], "scale": [{"curve": [0.089, 1, 0.178, 2.84, 0.089, 1, 0.178, 2.84]}, {"time": 0.2667, "x": 3.76, "y": 3.76}]}}, "path": {"M1": {"position": [{"value": 3}]}, "M2": {"position": [{"value": 2.85}], "mix": [{"mixRotate": 0}]}, "M3": {"position": [{"value": -3}]}, "M4": {"position": [{"value": 3.75}]}, "ball": {"position": [{"value": 3.8}]}, "ball5": {"position": [{"value": -3.9}]}, "ball7": {"position": [{"value": 4.25}]}, "ball8": {"position": [{"value": -3.15}]}, "ball9": {"position": [{"value": 3.25}]}, "gift1": {"position": [{"value": 3.7}]}, "gift2": {"position": [{"value": 4}]}, "gift3": {"position": [{"value": -3.96}]}, "gift4": {"position": [{"value": 4}], "mix": [{"mixRotate": 0}]}, "xu1": {"position": [{"value": 3}]}, "xu2": {"position": [{"value": 4}]}, "xu3": {"position": [{"value": 3.7}]}, "xu4": {"position": [{"value": 4.1}]}}, "deform": {"default": {"Gift2 copy": {"Gift2 copy": [{"vertices": [-80.20601, 78.96686, 83.44247, 75.53888, 80.20601, -78.96729, -83.44249, -75.53931]}]}}}, "drawOrder": [{"offsets": [{"slot": "Gift2 copy", "offset": -16}]}]}, "3_Open": {"slots": {"Gift2 copy": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.2, "color": "ffffff00", "curve": [0.267, 1, 0.333, 1, 0.267, 1, 0.333, 1, 0.267, 1, 0.333, 1, 0.267, 0, 0.333, 1]}, {"time": 0.4, "color": "ffffffff"}], "attachment": [{"name": null}, {"time": 0.2, "name": "Gift2 copy"}]}, "Glow2": {"rgba": [{"color": "ffffff00"}]}, "Glow3": {"rgba": [{"color": "ffffff00"}]}, "White_Light": {"attachment": [{"name": null}, {"time": 0.0667, "name": "White_Light"}, {"time": 0.2667, "name": "Yellow_Light"}, {"time": 0.4667, "name": "White_Light"}, {"time": 0.6667, "name": "Yellow_Light"}, {"time": 0.8667, "name": "White_Light"}, {"time": 1.0667, "name": "Yellow_Light"}, {"time": 1.2667, "name": "White_Light"}, {"time": 1.4667, "name": "Yellow_Light"}, {"time": 1.6667, "name": "White_Light"}, {"time": 1.8667, "name": "Yellow_Light"}, {"time": 2.0667, "name": "White_Light"}, {"time": 2.2667, "name": "Yellow_Light"}, {"time": 2.4667, "name": "White_Light"}]}}, "bones": {"glow": {"translate": [{"curve": [0.067, 2.58, 0.133, 7.75, 0.067, 0, 0.133, 0]}, {"time": 0.2, "x": 7.75, "curve": [0.278, 7.75, 0.356, 2.58, 0.278, 0, 0.356, 0]}, {"time": 0.4333}], "scale": [{}, {"time": 0.2, "x": 1.014, "curve": [0.267, 1.009, 0.333, 1.005, 0.267, 1, 0.333, 1]}, {"time": 0.4}]}, "machine": {"translate": [{}, {"time": 0.2, "y": -9.2}, {"time": 0.4333}], "scale": [{}, {"time": 0.2, "x": 0.982, "curve": [0.278, 0.988, 0.356, 1, 0.278, 1, 0.356, 1]}, {"time": 0.4333}]}, "machine5": {"translate": [{"x": -28.94, "y": -3.98}]}, "Xu1": {"translate": [{"x": -161.04, "y": 90.31}]}, "gift3": {"translate": [{"x": -0.44, "y": 10.2}]}, "m1": {"translate": [{"x": -221.87, "y": 109.64}]}, "gift2": {"translate": [{"x": -20.39, "y": 3.42}]}, "machine3": {"translate": [{"x": -200.14, "y": -53.47}]}, "m2": {"translate": [{"x": -116.56, "y": 70.2}]}, "m3": {"translate": [{"x": -31.18, "y": 35.53}]}, "machine6": {"translate": [{"x": -6.42, "y": 1.91}]}, "gift1": {"translate": [{"x": -119.21, "y": 10.74}]}, "machine2": {"translate": [{"x": -31.88, "y": -15.85}]}, "machine4": {"translate": [{"x": -9.87, "y": 0.25}]}, "gift4": {"translate": [{"x": -44.17, "y": 1.19}]}, "Xu2": {"translate": [{"x": -46.7, "y": -27.08}]}, "Ball_pick": {"rotate": [{"time": 0.0667, "value": 188.77, "curve": [0.489, 126.44, 1.078, 1.79]}, {"time": 1.3333, "value": 1.79}], "translate": [{"curve": [0.444, 0, 1.056, 0, 0.444, -21.46, 1.056, -64.37]}, {"time": 1.3333, "y": -64.37, "curve": "stepped"}, {"time": 1.4667, "y": -64.37, "curve": [1.522, 0, 1.578, 0, 1.522, -64.37, 1.578, -70.97]}, {"time": 1.6333, "y": -70.97, "curve": [1.7, 0, 1.767, 0, 1.7, -70.97, 1.767, -66.57]}, {"time": 1.8333, "y": -64.37}], "scale": [{"time": 0.2, "x": 0.554, "y": 0.554, "curve": [0.578, 0.703, 1.111, 1, 0.578, 0.703, 1.111, 1]}, {"time": 1.3333, "curve": "stepped"}, {"time": 1.4667, "curve": [1.522, 1, 1.578, 1, 1.522, 1, 1.578, 0.918]}, {"time": 1.6333, "y": 0.918, "curve": [1.7, 1, 1.767, 1, 1.7, 0.918, 1.767, 0.973]}, {"time": 1.8333}]}, "m4": {"translate": [{"x": -31.18, "y": 35.53}]}, "Xu3": {"translate": [{"x": -46.7, "y": -27.08}]}, "Xu4": {"translate": [{"x": -161.04, "y": 90.31}]}}, "path": {"M1": {"position": [{"value": 3}]}, "M2": {"position": [{"value": 2.85}], "mix": [{"mixRotate": 0}]}, "M3": {"position": [{"value": -3}]}, "M4": {"position": [{"value": 3.75}]}, "ball": {"position": [{"value": 3.8}]}, "ball5": {"position": [{"value": -3.9}]}, "ball7": {"position": [{"value": 4.25}]}, "ball8": {"position": [{"value": -3.15}]}, "ball9": {"position": [{"value": 3.25}]}, "gift1": {"position": [{"value": 3.7}]}, "gift2": {"position": [{"value": 4}]}, "gift3": {"position": [{"value": -3.96}]}, "gift4": {"position": [{"value": 4}], "mix": [{"mixRotate": 0}]}, "xu1": {"position": [{"value": 3}]}, "xu2": {"position": [{"value": 4}]}, "xu3": {"position": [{"value": 3.7}]}, "xu4": {"position": [{"value": 4.1}]}}, "deform": {"default": {"ball": {"gift2": [{}, {"time": 0.2, "offset": 1, "vertices": [-7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101]}, {"time": 0.3667}]}, "ball5": {"gift2": [{}, {"time": 0.2, "offset": 1, "vertices": [-7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101]}, {"time": 0.3667}]}, "ball7": {"gift2": [{}, {"time": 0.2, "offset": 1, "vertices": [-7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101]}, {"time": 0.3667}]}, "ball8": {"gift2": [{}, {"time": 0.2, "offset": 1, "vertices": [-7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101]}, {"time": 0.3667}]}, "ball9": {"gift2": [{}, {"time": 0.2, "offset": 1, "vertices": [-7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101]}, {"time": 0.3667}]}, "gift1": {"gift1": [{}, {"time": 0.2, "offset": 1, "vertices": [-7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101]}, {"time": 0.3667}]}, "gift2": {"gift2": [{}, {"time": 0.2, "offset": 1, "vertices": [-7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101]}, {"time": 0.3667}]}, "gift3": {"gift3": [{}, {"time": 0.2, "offset": 1, "vertices": [-7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101]}, {"time": 0.3667}]}, "gift4": {"gift4": [{}, {"time": 0.2, "offset": 1, "vertices": [-7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101]}, {"time": 0.3667}]}, "M1": {"M1": [{}, {"time": 0.2, "offset": 1, "vertices": [-7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101]}, {"time": 0.3667}]}, "M2": {"M1": [{}, {"time": 0.2, "offset": 1, "vertices": [-7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101]}, {"time": 0.3667}]}, "M3": {"M1": [{}, {"time": 0.2, "offset": 1, "vertices": [-7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101]}, {"time": 0.3667}]}, "M4": {"M1": [{}, {"time": 0.2, "offset": 1, "vertices": [-7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101]}, {"time": 0.3667}]}, "xu1": {"xu1": [{}, {"time": 0.2, "offset": 1, "vertices": [-7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101]}, {"time": 0.3667}]}, "xu2": {"xu2": [{}, {"time": 0.2, "offset": 1, "vertices": [-7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101]}, {"time": 0.3667}]}, "xu3": {"xu1": [{}, {"time": 0.2, "offset": 1, "vertices": [-7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101]}, {"time": 0.3667}]}, "xu4": {"xu1": [{}, {"time": 0.2, "offset": 1, "vertices": [-7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101]}, {"time": 0.3667}]}}}}, "4_Loop": {"slots": {"Glow2": {"rgba": [{"color": "ffffff00"}]}, "Glow3": {"rgba": [{"color": "ffffff00"}]}, "White_Light": {"attachment": [{"name": "White_Light"}, {"time": 0.2, "name": "Yellow_Light"}, {"time": 0.4, "name": "White_Light"}]}}, "bones": {"machine5": {"translate": [{"x": -28.94, "y": -3.98}]}, "Xu1": {"translate": [{"x": -161.04, "y": 90.31}]}, "gift3": {"translate": [{"x": -0.44, "y": 10.2}]}, "m1": {"translate": [{"x": -221.87, "y": 109.64}]}, "gift2": {"translate": [{"x": -20.39, "y": 3.42}]}, "machine3": {"translate": [{"x": -200.14, "y": -53.47}]}, "m2": {"translate": [{"x": -116.56, "y": 70.2}]}, "m3": {"translate": [{"x": -31.18, "y": 35.53}]}, "machine6": {"translate": [{"x": -6.42, "y": 1.91}]}, "gift1": {"translate": [{"x": -119.21, "y": 10.74}]}, "machine2": {"translate": [{"x": -31.88, "y": -15.85}]}, "machine4": {"translate": [{"x": -9.87, "y": 0.25}]}, "gift4": {"translate": [{"x": -44.17, "y": 1.19}]}, "Xu2": {"translate": [{"x": -46.7, "y": -27.08}]}, "Ball_pick": {"scale": [{"time": 0, "x": 0, "y": 0}]}, "Ball_pick_1": {"scale": [{"time": 0, "x": 0, "y": 0}]}, "m4": {"translate": [{"x": -31.18, "y": 35.53}]}, "Xu3": {"translate": [{"x": -46.7, "y": -27.08}]}, "Xu4": {"translate": [{"x": -161.04, "y": 90.31}]}}, "path": {"M1": {"position": [{"value": 3}]}, "M2": {"position": [{"value": 2.85}], "mix": [{"mixRotate": 0}]}, "M3": {"position": [{"value": -3}]}, "M4": {"position": [{"value": 3.75}]}, "ball": {"position": [{"value": 3.8}]}, "ball5": {"position": [{"value": -3.9}]}, "ball7": {"position": [{"value": 4.25}]}, "ball8": {"position": [{"value": -3.15}]}, "ball9": {"position": [{"value": 3.25}]}, "gift1": {"position": [{"value": 3.7}]}, "gift2": {"position": [{"value": 4}]}, "gift3": {"position": [{"value": -3.96}]}, "gift4": {"position": [{"value": 4}], "mix": [{"mixRotate": 0}]}, "xu1": {"position": [{"value": 3}]}, "xu2": {"position": [{"value": 4}]}, "xu3": {"position": [{"value": 3.7}]}, "xu4": {"position": [{"value": 4.1}]}}}, "3_Open_101": {"slots": {"Gift2 copy": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.2, "color": "ffffff00", "curve": [0.267, 1, 0.333, 1, 0.267, 1, 0.333, 1, 0.267, 1, 0.333, 1, 0.267, 0, 0.333, 1]}, {"time": 0.4, "color": "ffffffff"}], "attachment": [{"name": null}, {"time": 0.2, "name": "Gift2 copy"}]}, "Glow2": {"rgba": [{"color": "ffffff00"}]}, "Glow3": {"rgba": [{"color": "ffffff00"}]}, "White_Light": {"attachment": [{"name": null}, {"time": 0.0667, "name": "White_Light"}, {"time": 0.2667, "name": "Yellow_Light"}, {"time": 0.4667, "name": "White_Light"}, {"time": 0.6667, "name": "Yellow_Light"}, {"time": 0.8667, "name": "White_Light"}, {"time": 1.0667, "name": "Yellow_Light"}, {"time": 1.2667, "name": "White_Light"}, {"time": 1.4667, "name": "Yellow_Light"}, {"time": 1.6667, "name": "White_Light"}, {"time": 1.8667, "name": "Yellow_Light"}, {"time": 2.0667, "name": "White_Light"}, {"time": 2.2667, "name": "Yellow_Light"}, {"time": 2.4667, "name": "White_Light"}]}}, "bones": {"Ball_pick_1": {"scale": [{"time": 0, "x": 0, "y": 0}]}, "glow": {"translate": [{"curve": [0.067, 2.58, 0.133, 7.75, 0.067, 0, 0.133, 0]}, {"time": 0.2, "x": 7.75, "curve": [0.278, 7.75, 0.356, 2.58, 0.278, 0, 0.356, 0]}, {"time": 0.4333}], "scale": [{}, {"time": 0.2, "x": 1.014, "curve": [0.267, 1.009, 0.333, 1.005, 0.267, 1, 0.333, 1]}, {"time": 0.4}]}, "machine": {"translate": [{}, {"time": 0.2, "y": -9.2}, {"time": 0.4333}], "scale": [{}, {"time": 0.2, "x": 0.982, "curve": [0.278, 0.988, 0.356, 1, 0.278, 1, 0.356, 1]}, {"time": 0.4333}]}, "machine5": {"translate": [{"x": -28.94, "y": -3.98}]}, "Xu1": {"translate": [{"x": -161.04, "y": 90.31}]}, "gift3": {"translate": [{"x": -0.44, "y": 10.2}]}, "m1": {"translate": [{"x": -221.87, "y": 109.64}]}, "gift2": {"translate": [{"x": -20.39, "y": 3.42}]}, "machine3": {"translate": [{"x": -200.14, "y": -53.47}]}, "m2": {"translate": [{"x": -116.56, "y": 70.2}]}, "m3": {"translate": [{"x": -31.18, "y": 35.53}]}, "machine6": {"translate": [{"x": -6.42, "y": 1.91}]}, "gift1": {"translate": [{"x": -119.21, "y": 10.74}]}, "machine2": {"translate": [{"x": -31.88, "y": -15.85}]}, "machine4": {"translate": [{"x": -9.87, "y": 0.25}]}, "gift4": {"translate": [{"x": -44.17, "y": 1.19}]}, "Xu2": {"translate": [{"x": -46.7, "y": -27.08}]}, "Ball_pick": {"rotate": [{"time": 0.0667, "value": 188.77, "curve": [0.489, 126.44, 1.078, 1.79]}, {"time": 1.3333, "value": 1.79}], "translate": [{"curve": [0.444, 0, 1.056, 0, 0.444, -21.46, 1.056, -64.37]}, {"time": 1.3333, "y": -64.37, "curve": "stepped"}, {"time": 1.4667, "y": -64.37, "curve": [1.522, 0, 1.578, 0, 1.522, -64.37, 1.578, -70.97]}, {"time": 1.6333, "y": -70.97, "curve": [1.7, 0, 1.767, 0, 1.7, -70.97, 1.767, -66.57]}, {"time": 1.8333, "y": -64.37}], "scale": [{"time": 0.2, "x": 0.554, "y": 0.554, "curve": [0.578, 0.703, 1.111, 1, 0.578, 0.703, 1.111, 1]}, {"time": 1.3333, "curve": "stepped"}, {"time": 1.4667, "curve": [1.522, 1, 1.578, 1, 1.522, 1, 1.578, 0.918]}, {"time": 1.6333, "y": 0.918, "curve": [1.7, 1, 1.767, 1, 1.7, 0.918, 1.767, 0.973]}, {"time": 1.8333}]}, "m4": {"translate": [{"x": -31.18, "y": 35.53}]}, "Xu3": {"translate": [{"x": -46.7, "y": -27.08}]}, "Xu4": {"translate": [{"x": -161.04, "y": 90.31}]}}, "path": {"M1": {"position": [{"value": 3}]}, "M2": {"position": [{"value": 2.85}], "mix": [{"mixRotate": 0}]}, "M3": {"position": [{"value": -3}]}, "M4": {"position": [{"value": 3.75}]}, "ball": {"position": [{"value": 3.8}]}, "ball5": {"position": [{"value": -3.9}]}, "ball7": {"position": [{"value": 4.25}]}, "ball8": {"position": [{"value": -3.15}]}, "ball9": {"position": [{"value": 3.25}]}, "gift1": {"position": [{"value": 3.7}]}, "gift2": {"position": [{"value": 4}]}, "gift3": {"position": [{"value": -3.96}]}, "gift4": {"position": [{"value": 4}], "mix": [{"mixRotate": 0}]}, "xu1": {"position": [{"value": 3}]}, "xu2": {"position": [{"value": 4}]}, "xu3": {"position": [{"value": 3.7}]}, "xu4": {"position": [{"value": 4.1}]}}, "deform": {"default": {"ball": {"gift2": [{}, {"time": 0.2, "offset": 1, "vertices": [-7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101]}, {"time": 0.3667}]}, "ball5": {"gift2": [{}, {"time": 0.2, "offset": 1, "vertices": [-7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101]}, {"time": 0.3667}]}, "ball7": {"gift2": [{}, {"time": 0.2, "offset": 1, "vertices": [-7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101]}, {"time": 0.3667}]}, "ball8": {"gift2": [{}, {"time": 0.2, "offset": 1, "vertices": [-7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101]}, {"time": 0.3667}]}, "ball9": {"gift2": [{}, {"time": 0.2, "offset": 1, "vertices": [-7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101]}, {"time": 0.3667}]}, "gift1": {"gift1": [{}, {"time": 0.2, "offset": 1, "vertices": [-7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101]}, {"time": 0.3667}]}, "gift2": {"gift2": [{}, {"time": 0.2, "offset": 1, "vertices": [-7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101]}, {"time": 0.3667}]}, "gift3": {"gift3": [{}, {"time": 0.2, "offset": 1, "vertices": [-7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101]}, {"time": 0.3667}]}, "gift4": {"gift4": [{}, {"time": 0.2, "offset": 1, "vertices": [-7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101]}, {"time": 0.3667}]}, "M1": {"M1": [{}, {"time": 0.2, "offset": 1, "vertices": [-7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101]}, {"time": 0.3667}]}, "M2": {"M1": [{}, {"time": 0.2, "offset": 1, "vertices": [-7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101]}, {"time": 0.3667}]}, "M3": {"M1": [{}, {"time": 0.2, "offset": 1, "vertices": [-7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101]}, {"time": 0.3667}]}, "M4": {"M1": [{}, {"time": 0.2, "offset": 1, "vertices": [-7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101]}, {"time": 0.3667}]}, "xu1": {"xu1": [{}, {"time": 0.2, "offset": 1, "vertices": [-7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101]}, {"time": 0.3667}]}, "xu2": {"xu2": [{}, {"time": 0.2, "offset": 1, "vertices": [-7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101]}, {"time": 0.3667}]}, "xu3": {"xu1": [{}, {"time": 0.2, "offset": 1, "vertices": [-7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101]}, {"time": 0.3667}]}, "xu4": {"xu1": [{}, {"time": 0.2, "offset": 1, "vertices": [-7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101]}, {"time": 0.3667}]}}}}, "3_Open_121": {"slots": {"GiftRewardMB": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.2, "color": "ffffff00", "curve": [0.267, 1, 0.333, 1, 0.267, 1, 0.333, 1, 0.267, 1, 0.333, 1, 0.267, 0, 0.333, 1]}, {"time": 0.4, "color": "ffffffff"}], "attachment": [{"name": null}, {"time": 0.2, "name": "GiftRewardMB"}]}, "Glow2": {"rgba": [{"color": "ffffff00"}]}, "Glow3": {"rgba": [{"color": "ffffff00"}]}, "White_Light": {"attachment": [{"name": null}, {"time": 0.0667, "name": "White_Light"}, {"time": 0.2667, "name": "Yellow_Light"}, {"time": 0.4667, "name": "White_Light"}, {"time": 0.6667, "name": "Yellow_Light"}, {"time": 0.8667, "name": "White_Light"}, {"time": 1.0667, "name": "Yellow_Light"}, {"time": 1.2667, "name": "White_Light"}, {"time": 1.4667, "name": "Yellow_Light"}, {"time": 1.6667, "name": "White_Light"}, {"time": 1.8667, "name": "Yellow_Light"}, {"time": 2.0667, "name": "White_Light"}, {"time": 2.2667, "name": "Yellow_Light"}, {"time": 2.4667, "name": "White_Light"}]}}, "bones": {"Ball_pick": {"scale": [{"time": 0, "x": 0, "y": 0}]}, "glow": {"translate": [{"curve": [0.067, 2.58, 0.133, 7.75, 0.067, 0, 0.133, 0]}, {"time": 0.2, "x": 7.75, "curve": [0.278, 7.75, 0.356, 2.58, 0.278, 0, 0.356, 0]}, {"time": 0.4333}], "scale": [{}, {"time": 0.2, "x": 1.014, "curve": [0.267, 1.009, 0.333, 1.005, 0.267, 1, 0.333, 1]}, {"time": 0.4}]}, "machine": {"translate": [{}, {"time": 0.2, "y": -9.2}, {"time": 0.4333}], "scale": [{}, {"time": 0.2, "x": 0.982, "curve": [0.278, 0.988, 0.356, 1, 0.278, 1, 0.356, 1]}, {"time": 0.4333}]}, "machine5": {"translate": [{"x": -28.94, "y": -3.98}]}, "Xu1": {"translate": [{"x": -161.04, "y": 90.31}]}, "gift3": {"translate": [{"x": -0.44, "y": 10.2}]}, "m1": {"translate": [{"x": -221.87, "y": 109.64}]}, "gift2": {"translate": [{"x": -20.39, "y": 3.42}]}, "machine3": {"translate": [{"x": -200.14, "y": -53.47}]}, "m2": {"translate": [{"x": -116.56, "y": 70.2}]}, "m3": {"translate": [{"x": -31.18, "y": 35.53}]}, "machine6": {"translate": [{"x": -6.42, "y": 1.91}]}, "gift1": {"translate": [{"x": -119.21, "y": 10.74}]}, "machine2": {"translate": [{"x": -31.88, "y": -15.85}]}, "machine4": {"translate": [{"x": -9.87, "y": 0.25}]}, "gift4": {"translate": [{"x": -44.17, "y": 1.19}]}, "Xu2": {"translate": [{"x": -46.7, "y": -27.08}]}, "Ball_pick_1": {"rotate": [{"time": 0.0667, "value": 188.77, "curve": [0.489, 126.44, 1.078, 1.79]}, {"time": 1.3333, "value": 1.79}], "translate": [{"curve": [0.444, 0, 1.056, 0, 0.444, -21.46, 1.056, -64.37]}, {"time": 1.3333, "y": -64.37, "curve": "stepped"}, {"time": 1.4667, "y": -64.37, "curve": [1.522, 0, 1.578, 0, 1.522, -64.37, 1.578, -70.97]}, {"time": 1.6333, "y": -70.97, "curve": [1.7, 0, 1.767, 0, 1.7, -70.97, 1.767, -66.57]}, {"time": 1.8333, "y": -64.37}], "scale": [{"time": 0.2, "x": 0.554, "y": 0.554, "curve": [0.578, 0.703, 1.111, 1, 0.578, 0.703, 1.111, 1]}, {"time": 1.3333, "curve": "stepped"}, {"time": 1.4667, "curve": [1.522, 1, 1.578, 1, 1.522, 1, 1.578, 0.918]}, {"time": 1.6333, "y": 0.918, "curve": [1.7, 1, 1.767, 1, 1.7, 0.918, 1.767, 0.973]}, {"time": 1.8333}]}, "m4": {"translate": [{"x": -31.18, "y": 35.53}]}, "Xu3": {"translate": [{"x": -46.7, "y": -27.08}]}, "Xu4": {"translate": [{"x": -161.04, "y": 90.31}]}}, "path": {"M1": {"position": [{"value": 3}]}, "M2": {"position": [{"value": 2.85}], "mix": [{"mixRotate": 0}]}, "M3": {"position": [{"value": -3}]}, "M4": {"position": [{"value": 3.75}]}, "ball": {"position": [{"value": 3.8}]}, "ball5": {"position": [{"value": -3.9}]}, "ball7": {"position": [{"value": 4.25}]}, "ball8": {"position": [{"value": -3.15}]}, "ball9": {"position": [{"value": 3.25}]}, "gift1": {"position": [{"value": 3.7}]}, "gift2": {"position": [{"value": 4}]}, "gift3": {"position": [{"value": -3.96}]}, "gift4": {"position": [{"value": 4}], "mix": [{"mixRotate": 0}]}, "xu1": {"position": [{"value": 3}]}, "xu2": {"position": [{"value": 4}]}, "xu3": {"position": [{"value": 3.7}]}, "xu4": {"position": [{"value": 4.1}]}}, "deform": {"default": {"ball": {"gift2": [{}, {"time": 0.2, "offset": 1, "vertices": [-7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101]}, {"time": 0.3667}]}, "ball5": {"gift2": [{}, {"time": 0.2, "offset": 1, "vertices": [-7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101]}, {"time": 0.3667}]}, "ball7": {"gift2": [{}, {"time": 0.2, "offset": 1, "vertices": [-7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101]}, {"time": 0.3667}]}, "ball8": {"gift2": [{}, {"time": 0.2, "offset": 1, "vertices": [-7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101]}, {"time": 0.3667}]}, "ball9": {"gift2": [{}, {"time": 0.2, "offset": 1, "vertices": [-7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101]}, {"time": 0.3667}]}, "gift1": {"gift1": [{}, {"time": 0.2, "offset": 1, "vertices": [-7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101]}, {"time": 0.3667}]}, "gift2": {"gift2": [{}, {"time": 0.2, "offset": 1, "vertices": [-7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101]}, {"time": 0.3667}]}, "gift3": {"gift3": [{}, {"time": 0.2, "offset": 1, "vertices": [-7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101]}, {"time": 0.3667}]}, "gift4": {"gift4": [{}, {"time": 0.2, "offset": 1, "vertices": [-7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101]}, {"time": 0.3667}]}, "M1": {"M1": [{}, {"time": 0.2, "offset": 1, "vertices": [-7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101]}, {"time": 0.3667}]}, "M2": {"M1": [{}, {"time": 0.2, "offset": 1, "vertices": [-7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101]}, {"time": 0.3667}]}, "M3": {"M1": [{}, {"time": 0.2, "offset": 1, "vertices": [-7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101]}, {"time": 0.3667}]}, "M4": {"M1": [{}, {"time": 0.2, "offset": 1, "vertices": [-7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101]}, {"time": 0.3667}]}, "xu1": {"xu1": [{}, {"time": 0.2, "offset": 1, "vertices": [-7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101]}, {"time": 0.3667}]}, "xu2": {"xu2": [{}, {"time": 0.2, "offset": 1, "vertices": [-7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101]}, {"time": 0.3667}]}, "xu3": {"xu1": [{}, {"time": 0.2, "offset": 1, "vertices": [-7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101]}, {"time": 0.3667}]}, "xu4": {"xu1": [{}, {"time": 0.2, "offset": 1, "vertices": [-7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101, 0, -7.47101]}, {"time": 0.3667}]}}}}, "3_Open_1": {"slots": {"Glow2": {"rgba": [{"color": "ffffff00"}]}, "Glow3": {"rgba": [{"color": "ffffff00"}]}, "White_Light": {"attachment": [{"name": "White_Light"}, {"time": 0.2, "name": "Yellow_Light"}, {"time": 0.4, "name": "White_Light"}]}}, "bones": {"machine5": {"translate": [{"x": -28.94, "y": -3.98}]}, "Xu1": {"translate": [{"x": -161.04, "y": 90.31}]}, "gift3": {"translate": [{"x": -0.44, "y": 10.2}]}, "m1": {"translate": [{"x": -221.87, "y": 109.64}]}, "gift2": {"translate": [{"x": -20.39, "y": 3.42}]}, "machine3": {"translate": [{"x": -200.14, "y": -53.47}]}, "m2": {"translate": [{"x": -116.56, "y": 70.2}]}, "m3": {"translate": [{"x": -31.18, "y": 35.53}]}, "machine6": {"translate": [{"x": -6.42, "y": 1.91}]}, "gift1": {"translate": [{"x": -119.21, "y": 10.74}]}, "machine2": {"translate": [{"x": -31.88, "y": -15.85}]}, "machine4": {"translate": [{"x": -9.87, "y": 0.25}]}, "gift4": {"translate": [{"x": -44.17, "y": 1.19}]}, "Xu2": {"translate": [{"x": -46.7, "y": -27.08}]}, "Ball_pick": {"scale": [{"time": 0, "x": 0, "y": 0}]}, "Ball_pick_1": {"scale": [{"time": 0, "x": 0, "y": 0}]}, "m4": {"translate": [{"x": -31.18, "y": 35.53}]}, "Xu3": {"translate": [{"x": -46.7, "y": -27.08}]}, "Xu4": {"translate": [{"x": -161.04, "y": 90.31}]}}, "path": {"M1": {"position": [{"value": 3}]}, "M2": {"position": [{"value": 2.85}], "mix": [{"mixRotate": 0}]}, "M3": {"position": [{"value": -3}]}, "M4": {"position": [{"value": 3.75}]}, "ball": {"position": [{"value": 3.8}]}, "ball5": {"position": [{"value": -3.9}]}, "ball7": {"position": [{"value": 4.25}]}, "ball8": {"position": [{"value": -3.15}]}, "ball9": {"position": [{"value": 3.25}]}, "gift1": {"position": [{"value": 3.7}]}, "gift2": {"position": [{"value": 4}]}, "gift3": {"position": [{"value": -3.96}]}, "gift4": {"position": [{"value": 4}], "mix": [{"mixRotate": 0}]}, "xu1": {"position": [{"value": 3}]}, "xu2": {"position": [{"value": 4}]}, "xu3": {"position": [{"value": 3.7}]}, "xu4": {"position": [{"value": 4.1}]}}}}}
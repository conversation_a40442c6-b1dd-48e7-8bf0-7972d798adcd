"use strict";
var spine = (() => {
  var xt = Object.defineProperty,
    $s = Object.getOwnPropertyDescriptor,
    er = Object.getOwnPropertyNames,
    tr = Object.prototype.hasOwnProperty,
    ir = (e, t, i) =>
      t in e
        ? xt(e, t, { enumerable: !0, configurable: !0, writable: !0, value: i })
        : (e[t] = i),
    sr = (e, t) => {
      for (var i in t) xt(e, i, { get: t[i], enumerable: !0 });
    },
    rr = (e, t, i, s) => {
      if ((t && typeof t == "object") || typeof t == "function")
        for (let n of er(t))
          !tr.call(e, n) &&
            n !== i &&
            xt(e, n, {
              get: () => t[n],
              enumerable: !(s = $s(t, n)) || s.enumerable,
            });
      return e;
    },
    nr = (e) => rr(xt({}, "__esModule", { value: !0 }), e),
    B = (e, t, i) => (ir(e, typeof t != "symbol" ? t + "" : t, i), i),
    ts = {};
  sr(ts, {
    AlphaTimeline: () => li,
    Animation: () => yt,
    AnimationState: () => Ci,
    AnimationStateAdapter: () => dr,
    AnimationStateData: () => ps,
    AssetManager: () => zi,
    AssetManagerBase: () => Cs,
    AtlasAttachmentLoader: () => As,
    Attachment: () => Ht,
    AttachmentTimeline: () => He,
    BinaryInput: () => Rs,
    BlendMode: () => Lt,
    Bone: () => Fi,
    BoneData: () => Ri,
    BoundingBoxAttachment: () => Et,
    CURRENT: () => gs,
    CameraController: () => Hr,
    ClippingAttachment: () => tt,
    Color: () => V,
    Color2Attribute: () => Ps,
    ColorAttribute: () => Vt,
    ConstraintData: () => ct,
    CurveTimeline: () => Fe,
    CurveTimeline1: () => ke,
    CurveTimeline2: () => At,
    DebugUtils: () => or,
    DeformTimeline: () => di,
    Downloader: () => Yi,
    DrawOrderTimeline: () => je,
    Event: () => Li,
    EventData: () => Xi,
    EventQueue: () => fs,
    EventTimeline: () => ht,
    EventType: () => Ae,
    FIRST: () => Si,
    FakeTexture: () => cr,
    GLTexture: () => ft,
    HOLD_FIRST: () => kt,
    HOLD_MIX: () => ms,
    HOLD_SUBSEQUENT: () => Ti,
    IkConstraint: () => Ss,
    IkConstraintData: () => Pi,
    IkConstraintTimeline: () => ci,
    Inherit: () => dt,
    InheritTimeline: () => ri,
    Input: () => mt,
    IntSet: () => ar,
    Interpolation: () => is,
    LoadingScreen: () => Ws,
    M00: () => O,
    M01: () => z,
    M02: () => q,
    M03: () => D,
    M10: () => G,
    M11: () => N,
    M12: () => H,
    M13: () => U,
    M20: () => j,
    M21: () => Z,
    M22: () => _,
    M23: () => W,
    M30: () => Q,
    M31: () => $,
    M32: () => ee,
    M33: () => K,
    ManagedWebGLRenderingContext: () => pe,
    MathUtils: () => X,
    Matrix4: () => Be,
    Mesh: () => qi,
    MeshAttachment: () => Ue,
    MixBlend: () => ls,
    MixDirection: () => os,
    OrthoCamera: () => Ls,
    PathAttachment: () => Ze,
    PathConstraint: () => it,
    PathConstraintData: () => Bi,
    PathConstraintMixTimeline: () => gi,
    PathConstraintPositionTimeline: () => fi,
    PathConstraintSpacingTimeline: () => mi,
    Physics: () => Es,
    PhysicsConstraintDampingTimeline: () => xi,
    PhysicsConstraintGravityTimeline: () => yi,
    PhysicsConstraintInertiaTimeline: () => pi,
    PhysicsConstraintMassTimeline: () => bi,
    PhysicsConstraintMixTimeline: () => Ai,
    PhysicsConstraintResetTimeline: () => Ct,
    PhysicsConstraintStrengthTimeline: () => wi,
    PhysicsConstraintTimeline: () => De,
    PhysicsConstraintWindTimeline: () => vi,
    PointAttachment: () => Mi,
    PolygonBatcher: () => rt,
    Pool: () => lt,
    Position2Attribute: () => Bt,
    Position3Attribute: () => jr,
    PositionMode: () => Mt,
    Pow: () => ss,
    PowOut: () => lr,
    RGB2Timeline: () => hi,
    RGBA2Timeline: () => oi,
    RGBATimeline: () => ni,
    RGBTimeline: () => ai,
    RegionAttachment: () => le,
    ResizeMode: () => Ut,
    RotateMode: () => Ft,
    RotateTimeline: () => ot,
    SETUP: () => ki,
    SUBSEQUENT: () => Tt,
    ScaleTimeline: () => Qt,
    ScaleXTimeline: () => $t,
    ScaleYTimeline: () => ei,
    SceneRenderer: () => Ji,
    SequenceTimeline: () => et,
    Shader: () => we,
    ShapeRenderer: () => Hi,
    ShapeType: () => ye,
    ShearTimeline: () => ti,
    ShearXTimeline: () => ii,
    ShearYTimeline: () => si,
    Skeleton: () => ut,
    SkeletonBinary: () => Ms,
    SkeletonBounds: () => Fs,
    SkeletonClipping: () => Xt,
    SkeletonData: () => Oi,
    SkeletonDebugRenderer: () => Ot,
    SkeletonJson: () => Ys,
    SkeletonRenderer: () => Zi,
    Skin: () => Yt,
    SkinEntry: () => Di,
    Slot: () => Ts,
    SlotData: () => Ni,
    SpacingMode: () => Rt,
    SpineCanvas: () => en,
    SpinePlayer: () => tn,
    SpinePlayerEditor: () => Hs,
    StringSet: () => Gt,
    TexCoordAttribute: () => Gi,
    Texture: () => Ei,
    TextureAtlas: () => bs,
    TextureAtlasPage: () => vs,
    TextureAtlasRegion: () => Ii,
    TextureFilter: () => It,
    TextureRegion: () => xs,
    TextureWrap: () => ws,
    TimeKeeper: () => bt,
    Timeline: () => me,
    Touch: () => Xs,
    TrackEntry: () => us,
    TransformConstraint: () => ks,
    TransformConstraintData: () => Ui,
    TransformConstraintTimeline: () => ui,
    TranslateTimeline: () => Zt,
    TranslateXTimeline: () => Jt,
    TranslateYTimeline: () => Kt,
    Triangulator: () => Ce,
    Utils: () => P,
    Vector2: () => Me,
    Vector3: () => Se,
    VertexAttachment: () => Re,
    VertexAttribute: () => st,
    VertexAttributeType: () => Je,
    WindowedMean: () => hr,
  });
  var ar = class {
      array = new Array();
      add(e) {
        let t = this.contains(e);
        return (this.array[e | 0] = e | 0), !t;
      }
      contains(e) {
        return this.array[e | 0] != null;
      }
      remove(e) {
        this.array[e | 0] = void 0;
      }
      clear() {
        this.array.length = 0;
      }
    },
    Gt = class {
      entries = {};
      size = 0;
      add(e) {
        let t = this.entries[e];
        return (this.entries[e] = !0), t ? !1 : (this.size++, !0);
      }
      addAll(e) {
        let t = this.size;
        for (var i = 0, s = e.length; i < s; i++) this.add(e[i]);
        return t != this.size;
      }
      contains(e) {
        return this.entries[e];
      }
      clear() {
        (this.entries = {}), (this.size = 0);
      }
    },
    Ge = class {
      constructor(e = 0, t = 0, i = 0, s = 0) {
        (this.r = e), (this.g = t), (this.b = i), (this.a = s);
      }
      set(e, t, i, s) {
        return (
          (this.r = e), (this.g = t), (this.b = i), (this.a = s), this.clamp()
        );
      }
      setFromColor(e) {
        return (
          (this.r = e.r), (this.g = e.g), (this.b = e.b), (this.a = e.a), this
        );
      }
      setFromString(e) {
        return (
          (e = e.charAt(0) == "#" ? e.substr(1) : e),
          (this.r = parseInt(e.substr(0, 2), 16) / 255),
          (this.g = parseInt(e.substr(2, 2), 16) / 255),
          (this.b = parseInt(e.substr(4, 2), 16) / 255),
          (this.a = e.length != 8 ? 1 : parseInt(e.substr(6, 2), 16) / 255),
          this
        );
      }
      add(e, t, i, s) {
        return (
          (this.r += e),
          (this.g += t),
          (this.b += i),
          (this.a += s),
          this.clamp()
        );
      }
      clamp() {
        return (
          this.r < 0 ? (this.r = 0) : this.r > 1 && (this.r = 1),
          this.g < 0 ? (this.g = 0) : this.g > 1 && (this.g = 1),
          this.b < 0 ? (this.b = 0) : this.b > 1 && (this.b = 1),
          this.a < 0 ? (this.a = 0) : this.a > 1 && (this.a = 1),
          this
        );
      }
      static rgba8888ToColor(e, t) {
        (e.r = ((t & 4278190080) >>> 24) / 255),
          (e.g = ((t & 16711680) >>> 16) / 255),
          (e.b = ((t & 65280) >>> 8) / 255),
          (e.a = (t & 255) / 255);
      }
      static rgb888ToColor(e, t) {
        (e.r = ((t & 16711680) >>> 16) / 255),
          (e.g = ((t & 65280) >>> 8) / 255),
          (e.b = (t & 255) / 255);
      }
      toRgb888() {
        const e = (t) => ("0" + (t * 255).toString(16)).slice(-2);
        return Number("0x" + e(this.r) + e(this.g) + e(this.b));
      }
      static fromString(e) {
        return new Ge().setFromString(e);
      }
    },
    V = Ge;
  B(V, "WHITE", new Ge(1, 1, 1, 1)),
    B(V, "RED", new Ge(1, 0, 0, 1)),
    B(V, "GREEN", new Ge(0, 1, 0, 1)),
    B(V, "BLUE", new Ge(0, 0, 1, 1)),
    B(V, "MAGENTA", new Ge(1, 0, 1, 1));
  var Ie = class {
      static clamp(e, t, i) {
        return e < t ? t : e > i ? i : e;
      }
      static cosDeg(e) {
        return Math.cos(e * Ie.degRad);
      }
      static sinDeg(e) {
        return Math.sin(e * Ie.degRad);
      }
      static atan2Deg(e, t) {
        return Math.atan2(e, t) * Ie.degRad;
      }
      static signum(e) {
        return e > 0 ? 1 : e < 0 ? -1 : 0;
      }
      static toInt(e) {
        return e > 0 ? Math.floor(e) : Math.ceil(e);
      }
      static cbrt(e) {
        let t = Math.pow(Math.abs(e), 0.3333333333333333);
        return e < 0 ? -t : t;
      }
      static randomTriangular(e, t) {
        return Ie.randomTriangularWith(e, t, (e + t) * 0.5);
      }
      static randomTriangularWith(e, t, i) {
        let s = Math.random(),
          n = t - e;
        return s <= (i - e) / n
          ? e + Math.sqrt(s * n * (i - e))
          : t - Math.sqrt((1 - s) * n * (t - i));
      }
      static isPowerOfTwo(e) {
        return e && (e & (e - 1)) === 0;
      }
    },
    X = Ie;
  B(X, "PI", 3.1415927),
    B(X, "PI2", Ie.PI * 2),
    B(X, "invPI2", 1 / Ie.PI2),
    B(X, "radiansToDegrees", 180 / Ie.PI),
    B(X, "radDeg", Ie.radiansToDegrees),
    B(X, "degreesToRadians", Ie.PI / 180),
    B(X, "degRad", Ie.degreesToRadians);
  var is = class {
      apply(e, t, i) {
        return e + (t - e) * this.applyInternal(i);
      }
    },
    ss = class extends is {
      power = 2;
      constructor(e) {
        super(), (this.power = e);
      }
      applyInternal(e) {
        return e <= 0.5
          ? Math.pow(e * 2, this.power) / 2
          : Math.pow((e - 1) * 2, this.power) / (this.power % 2 == 0 ? -2 : 2) +
              1;
      }
    },
    lr = class extends ss {
      constructor(e) {
        super(e);
      }
      applyInternal(e) {
        return Math.pow(e - 1, this.power) * (this.power % 2 == 0 ? -1 : 1) + 1;
      }
    },
    $e = class {
      static arrayCopy(e, t, i, s, n) {
        for (let d = t, a = s; d < t + n; d++, a++) i[a] = e[d];
      }
      static arrayFill(e, t, i, s) {
        for (let n = t; n < i; n++) e[n] = s;
      }
      static setArraySize(e, t, i = 0) {
        let s = e.length;
        if (s == t) return e;
        if (((e.length = t), s < t)) for (let n = s; n < t; n++) e[n] = i;
        return e;
      }
      static ensureArrayCapacity(e, t, i = 0) {
        return e.length >= t ? e : $e.setArraySize(e, t, i);
      }
      static newArray(e, t) {
        let i = new Array(e);
        for (let s = 0; s < e; s++) i[s] = t;
        return i;
      }
      static newFloatArray(e) {
        if ($e.SUPPORTS_TYPED_ARRAYS) return new Float32Array(e);
        {
          let t = new Array(e);
          for (let i = 0; i < t.length; i++) t[i] = 0;
          return t;
        }
      }
      static newShortArray(e) {
        if ($e.SUPPORTS_TYPED_ARRAYS) return new Int16Array(e);
        {
          let t = new Array(e);
          for (let i = 0; i < t.length; i++) t[i] = 0;
          return t;
        }
      }
      static toFloatArray(e) {
        return $e.SUPPORTS_TYPED_ARRAYS ? new Float32Array(e) : e;
      }
      static toSinglePrecision(e) {
        return $e.SUPPORTS_TYPED_ARRAYS ? Math.fround(e) : e;
      }
      static webkit602BugfixHelper(e, t) {}
      static contains(e, t, i = !0) {
        for (var s = 0; s < e.length; s++) if (e[s] == t) return !0;
        return !1;
      }
      static enumValue(e, t) {
        return e[t[0].toUpperCase() + t.slice(1)];
      }
    },
    P = $e;
  B(P, "SUPPORTS_TYPED_ARRAYS", typeof Float32Array < "u");
  var or = class {
      static logBones(e) {
        for (let t = 0; t < e.bones.length; t++) {
          let i = e.bones[t];
          console.log(
            i.data.name +
              ", " +
              i.a +
              ", " +
              i.b +
              ", " +
              i.c +
              ", " +
              i.d +
              ", " +
              i.worldX +
              ", " +
              i.worldY
          );
        }
      }
    },
    lt = class {
      items = new Array();
      instantiator;
      constructor(e) {
        this.instantiator = e;
      }
      obtain() {
        return this.items.length > 0 ? this.items.pop() : this.instantiator();
      }
      free(e) {
        e.reset && e.reset(), this.items.push(e);
      }
      freeAll(e) {
        for (let t = 0; t < e.length; t++) this.free(e[t]);
      }
      clear() {
        this.items.length = 0;
      }
    },
    Me = class {
      constructor(e = 0, t = 0) {
        (this.x = e), (this.y = t);
      }
      set(e, t) {
        return (this.x = e), (this.y = t), this;
      }
      length() {
        let e = this.x,
          t = this.y;
        return Math.sqrt(e * e + t * t);
      }
      normalize() {
        let e = this.length();
        return e != 0 && ((this.x /= e), (this.y /= e)), this;
      }
    },
    bt = class {
      maxDelta = 0.064;
      framesPerSecond = 0;
      delta = 0;
      totalTime = 0;
      lastTime = Date.now() / 1e3;
      frameCount = 0;
      frameTime = 0;
      update() {
        let e = Date.now() / 1e3;
        (this.delta = e - this.lastTime),
          (this.frameTime += this.delta),
          (this.totalTime += this.delta),
          this.delta > this.maxDelta && (this.delta = this.maxDelta),
          (this.lastTime = e),
          this.frameCount++,
          this.frameTime > 1 &&
            ((this.framesPerSecond = this.frameCount / this.frameTime),
            (this.frameTime = 0),
            (this.frameCount = 0));
      }
    },
    hr = class {
      values;
      addedValues = 0;
      lastValue = 0;
      mean = 0;
      dirty = !0;
      constructor(e = 32) {
        this.values = new Array(e);
      }
      hasEnoughData() {
        return this.addedValues >= this.values.length;
      }
      addValue(e) {
        this.addedValues < this.values.length && this.addedValues++,
          (this.values[this.lastValue++] = e),
          this.lastValue > this.values.length - 1 && (this.lastValue = 0),
          (this.dirty = !0);
      }
      getMean() {
        if (this.hasEnoughData()) {
          if (this.dirty) {
            let e = 0;
            for (let t = 0; t < this.values.length; t++) e += this.values[t];
            (this.mean = e / this.values.length), (this.dirty = !1);
          }
          return this.mean;
        }
        return 0;
      }
    },
    Ht = class {
      name;
      constructor(e) {
        if (!e) throw new Error("name cannot be null.");
        this.name = e;
      }
    },
    rs = class extends Ht {
      id = rs.nextID++;
      bones = null;
      vertices = [];
      worldVerticesLength = 0;
      timelineAttachment = this;
      constructor(e) {
        super(e);
      }
      computeWorldVertices(e, t, i, s, n, d) {
        i = n + (i >> 1) * d;
        let a = e.bone.skeleton,
          r = e.deform,
          o = this.vertices,
          l = this.bones;
        if (!l) {
          r.length > 0 && (o = r);
          let f = e.bone,
            m = f.worldX,
            g = f.worldY,
            b = f.a,
            x = f.b,
            p = f.c,
            w = f.d;
          for (let y = t, v = n; v < i; y += 2, v += d) {
            let A = o[y],
              C = o[y + 1];
            (s[v] = A * b + C * x + m), (s[v + 1] = A * p + C * w + g);
          }
          return;
        }
        let h = 0,
          u = 0;
        for (let f = 0; f < t; f += 2) {
          let m = l[h];
          (h += m + 1), (u += m);
        }
        let c = a.bones;
        if (r.length == 0)
          for (let f = n, m = u * 3; f < i; f += d) {
            let g = 0,
              b = 0,
              x = l[h++];
            for (x += h; h < x; h++, m += 3) {
              let p = c[l[h]],
                w = o[m],
                y = o[m + 1],
                v = o[m + 2];
              (g += (w * p.a + y * p.b + p.worldX) * v),
                (b += (w * p.c + y * p.d + p.worldY) * v);
            }
            (s[f] = g), (s[f + 1] = b);
          }
        else {
          let f = r;
          for (let m = n, g = u * 3, b = u << 1; m < i; m += d) {
            let x = 0,
              p = 0,
              w = l[h++];
            for (w += h; h < w; h++, g += 3, b += 2) {
              let y = c[l[h]],
                v = o[g] + f[b],
                A = o[g + 1] + f[b + 1],
                C = o[g + 2];
              (x += (v * y.a + A * y.b + y.worldX) * C),
                (p += (v * y.c + A * y.d + y.worldY) * C);
            }
            (s[m] = x), (s[m + 1] = p);
          }
        }
      }
      copyTo(e) {
        this.bones
          ? ((e.bones = new Array(this.bones.length)),
            P.arrayCopy(this.bones, 0, e.bones, 0, this.bones.length))
          : (e.bones = null),
          this.vertices &&
            ((e.vertices = P.newFloatArray(this.vertices.length)),
            P.arrayCopy(this.vertices, 0, e.vertices, 0, this.vertices.length)),
          (e.worldVerticesLength = this.worldVerticesLength),
          (e.timelineAttachment = this.timelineAttachment);
      }
    },
    Re = rs;
  B(Re, "nextID", 0);
  var vt = class {
      id = vt.nextID();
      regions;
      start = 0;
      digits = 0;
      setupIndex = 0;
      constructor(e) {
        this.regions = new Array(e);
      }
      copy() {
        let e = new vt(this.regions.length);
        return (
          P.arrayCopy(this.regions, 0, e.regions, 0, this.regions.length),
          (e.start = this.start),
          (e.digits = this.digits),
          (e.setupIndex = this.setupIndex),
          e
        );
      }
      apply(e, t) {
        let i = e.sequenceIndex;
        i == -1 && (i = this.setupIndex),
          i >= this.regions.length && (i = this.regions.length - 1);
        let s = this.regions[i];
        t.region != s && ((t.region = s), t.updateRegion());
      }
      getPath(e, t) {
        let i = e,
          s = (this.start + t).toString();
        for (let n = this.digits - s.length; n > 0; n--) i += "0";
        return (i += s), i;
      }
      static nextID() {
        return vt._nextID++;
      }
    },
    jt = vt;
  B(jt, "_nextID", 0);
  var ns = ((e) => (
      (e[(e.hold = 0)] = "hold"),
      (e[(e.once = 1)] = "once"),
      (e[(e.loop = 2)] = "loop"),
      (e[(e.pingpong = 3)] = "pingpong"),
      (e[(e.onceReverse = 4)] = "onceReverse"),
      (e[(e.loopReverse = 5)] = "loopReverse"),
      (e[(e.pingpongReverse = 6)] = "pingpongReverse"),
      e
    ))(ns || {}),
    as = [0, 1, 2, 3, 4, 5, 6],
    yt = class {
      name;
      timelines = [];
      timelineIds = new Gt();
      duration;
      constructor(e, t, i) {
        if (!e) throw new Error("name cannot be null.");
        (this.name = e), this.setTimelines(t), (this.duration = i);
      }
      setTimelines(e) {
        if (!e) throw new Error("timelines cannot be null.");
        (this.timelines = e), this.timelineIds.clear();
        for (var t = 0; t < e.length; t++)
          this.timelineIds.addAll(e[t].getPropertyIds());
      }
      hasTimeline(e) {
        for (let t = 0; t < e.length; t++)
          if (this.timelineIds.contains(e[t])) return !0;
        return !1;
      }
      apply(e, t, i, s, n, d, a, r) {
        if (!e) throw new Error("skeleton cannot be null.");
        s &&
          this.duration != 0 &&
          ((i %= this.duration), t > 0 && (t %= this.duration));
        let o = this.timelines;
        for (let l = 0, h = o.length; l < h; l++)
          o[l].apply(e, t, i, n, d, a, r);
      }
    },
    ls = ((e) => (
      (e[(e.setup = 0)] = "setup"),
      (e[(e.first = 1)] = "first"),
      (e[(e.replace = 2)] = "replace"),
      (e[(e.add = 3)] = "add"),
      e
    ))(ls || {}),
    os = ((e) => (
      (e[(e.mixIn = 0)] = "mixIn"), (e[(e.mixOut = 1)] = "mixOut"), e
    ))(os || {}),
    re = {
      rotate: 0,
      x: 1,
      y: 2,
      scaleX: 3,
      scaleY: 4,
      shearX: 5,
      shearY: 6,
      inherit: 7,
      rgb: 8,
      alpha: 9,
      rgb2: 10,
      attachment: 11,
      deform: 12,
      event: 13,
      drawOrder: 14,
      ikConstraint: 15,
      transformConstraint: 16,
      pathConstraintPosition: 17,
      pathConstraintSpacing: 18,
      pathConstraintMix: 19,
      physicsConstraintInertia: 20,
      physicsConstraintStrength: 21,
      physicsConstraintDamping: 22,
      physicsConstraintMass: 23,
      physicsConstraintWind: 24,
      physicsConstraintGravity: 25,
      physicsConstraintMix: 26,
      physicsConstraintReset: 27,
      sequence: 28,
    },
    me = class {
      propertyIds;
      frames;
      constructor(e, t) {
        (this.propertyIds = t),
          (this.frames = P.newFloatArray(e * this.getFrameEntries()));
      }
      getPropertyIds() {
        return this.propertyIds;
      }
      getFrameEntries() {
        return 1;
      }
      getFrameCount() {
        return this.frames.length / this.getFrameEntries();
      }
      getDuration() {
        return this.frames[this.frames.length - this.getFrameEntries()];
      }
      static search1(e, t) {
        let i = e.length;
        for (let s = 1; s < i; s++) if (e[s] > t) return s - 1;
        return i - 1;
      }
      static search(e, t, i) {
        let s = e.length;
        for (let n = i; n < s; n += i) if (e[n] > t) return n - i;
        return s - i;
      }
    },
    Fe = class extends me {
      curves;
      constructor(e, t, i) {
        super(e, i),
          (this.curves = P.newFloatArray(e + t * 18)),
          (this.curves[e - 1] = 1);
      }
      setLinear(e) {
        this.curves[e] = 0;
      }
      setStepped(e) {
        this.curves[e] = 1;
      }
      shrink(e) {
        let t = this.getFrameCount() + e * 18;
        if (this.curves.length > t) {
          let i = P.newFloatArray(t);
          P.arrayCopy(this.curves, 0, i, 0, t), (this.curves = i);
        }
      }
      setBezier(e, t, i, s, n, d, a, r, o, l, h) {
        let u = this.curves,
          c = this.getFrameCount() + e * 18;
        i == 0 && (u[t] = 2 + c);
        let f = (s - d * 2 + r) * 0.03,
          m = (n - a * 2 + o) * 0.03,
          g = ((d - r) * 3 - s + l) * 0.006,
          b = ((a - o) * 3 - n + h) * 0.006,
          x = f * 2 + g,
          p = m * 2 + b,
          w = (d - s) * 0.3 + f + g * 0.16666667,
          y = (a - n) * 0.3 + m + b * 0.16666667,
          v = s + w,
          A = n + y;
        for (let C = c + 18; c < C; c += 2)
          (u[c] = v),
            (u[c + 1] = A),
            (w += x),
            (y += p),
            (x += g),
            (p += b),
            (v += w),
            (A += y);
      }
      getBezierValue(e, t, i, s) {
        let n = this.curves;
        if (n[s] > e) {
          let o = this.frames[t],
            l = this.frames[t + i];
          return l + ((e - o) / (n[s] - o)) * (n[s + 1] - l);
        }
        let d = s + 18;
        for (s += 2; s < d; s += 2)
          if (n[s] >= e) {
            let o = n[s - 2],
              l = n[s - 1];
            return l + ((e - o) / (n[s] - o)) * (n[s + 1] - l);
          }
        t += this.getFrameEntries();
        let a = n[d - 2],
          r = n[d - 1];
        return r + ((e - a) / (this.frames[t] - a)) * (this.frames[t + i] - r);
      }
    },
    ke = class extends Fe {
      constructor(e, t, i) {
        super(e, t, [i]);
      }
      getFrameEntries() {
        return 2;
      }
      setFrame(e, t, i) {
        (e <<= 1), (this.frames[e] = t), (this.frames[e + 1] = i);
      }
      getCurveValue(e) {
        let t = this.frames,
          i = t.length - 2;
        for (let n = 2; n <= i; n += 2)
          if (t[n] > e) {
            i = n - 2;
            break;
          }
        let s = this.curves[i >> 1];
        switch (s) {
          case 0:
            let n = t[i],
              d = t[i + 1];
            return d + ((e - n) / (t[i + 2] - n)) * (t[i + 2 + 1] - d);
          case 1:
            return t[i + 1];
        }
        return this.getBezierValue(e, i, 1, s - 2);
      }
      getRelativeValue(e, t, i, s, n) {
        if (e < this.frames[0]) {
          switch (i) {
            case 0:
              return n;
            case 1:
              return s + (n - s) * t;
          }
          return s;
        }
        let d = this.getCurveValue(e);
        switch (i) {
          case 0:
            return n + d * t;
          case 1:
          case 2:
            d += n - s;
        }
        return s + d * t;
      }
      getAbsoluteValue(e, t, i, s, n) {
        if (e < this.frames[0]) {
          switch (i) {
            case 0:
              return n;
            case 1:
              return s + (n - s) * t;
          }
          return s;
        }
        let d = this.getCurveValue(e);
        return i == 0 ? n + (d - n) * t : s + (d - s) * t;
      }
      getAbsoluteValue2(e, t, i, s, n, d) {
        if (e < this.frames[0]) {
          switch (i) {
            case 0:
              return n;
            case 1:
              return s + (n - s) * t;
          }
          return s;
        }
        return i == 0 ? n + (d - n) * t : s + (d - s) * t;
      }
      getScaleValue(e, t, i, s, n, d) {
        const a = this.frames;
        if (e < a[0]) {
          switch (i) {
            case 0:
              return d;
            case 1:
              return n + (d - n) * t;
          }
          return n;
        }
        let r = this.getCurveValue(e) * d;
        if (t == 1) return i == 3 ? n + r - d : r;
        if (s == 1)
          switch (i) {
            case 0:
              return d + (Math.abs(r) * X.signum(d) - d) * t;
            case 1:
            case 2:
              return n + (Math.abs(r) * X.signum(n) - n) * t;
          }
        else {
          let o = 0;
          switch (i) {
            case 0:
              return (o = Math.abs(d) * X.signum(r)), o + (r - o) * t;
            case 1:
            case 2:
              return (o = Math.abs(n) * X.signum(r)), o + (r - o) * t;
          }
        }
        return n + (r - d) * t;
      }
    },
    At = class extends Fe {
      constructor(e, t, i, s) {
        super(e, t, [i, s]);
      }
      getFrameEntries() {
        return 3;
      }
      setFrame(e, t, i, s) {
        (e *= 3),
          (this.frames[e] = t),
          (this.frames[e + 1] = i),
          (this.frames[e + 2] = s);
      }
    },
    ot = class extends ke {
      boneIndex = 0;
      constructor(e, t, i) {
        super(e, t, re.rotate + "|" + i), (this.boneIndex = i);
      }
      apply(e, t, i, s, n, d, a) {
        let r = e.bones[this.boneIndex];
        r.active &&
          (r.rotation = this.getRelativeValue(
            i,
            n,
            d,
            r.rotation,
            r.data.rotation
          ));
      }
    },
    Zt = class extends At {
      boneIndex = 0;
      constructor(e, t, i) {
        super(e, t, re.x + "|" + i, re.y + "|" + i), (this.boneIndex = i);
      }
      apply(e, t, i, s, n, d, a) {
        let r = e.bones[this.boneIndex];
        if (!r.active) return;
        let o = this.frames;
        if (i < o[0]) {
          switch (d) {
            case 0:
              (r.x = r.data.x), (r.y = r.data.y);
              return;
            case 1:
              (r.x += (r.data.x - r.x) * n), (r.y += (r.data.y - r.y) * n);
          }
          return;
        }
        let l = 0,
          h = 0,
          u = me.search(o, i, 3),
          c = this.curves[u / 3];
        switch (c) {
          case 0:
            let f = o[u];
            (l = o[u + 1]), (h = o[u + 2]);
            let m = (i - f) / (o[u + 3] - f);
            (l += (o[u + 3 + 1] - l) * m), (h += (o[u + 3 + 2] - h) * m);
            break;
          case 1:
            (l = o[u + 1]), (h = o[u + 2]);
            break;
          default:
            (l = this.getBezierValue(i, u, 1, c - 2)),
              (h = this.getBezierValue(i, u, 2, c + 18 - 2));
        }
        switch (d) {
          case 0:
            (r.x = r.data.x + l * n), (r.y = r.data.y + h * n);
            break;
          case 1:
          case 2:
            (r.x += (r.data.x + l - r.x) * n),
              (r.y += (r.data.y + h - r.y) * n);
            break;
          case 3:
            (r.x += l * n), (r.y += h * n);
        }
      }
    },
    Jt = class extends ke {
      boneIndex = 0;
      constructor(e, t, i) {
        super(e, t, re.x + "|" + i), (this.boneIndex = i);
      }
      apply(e, t, i, s, n, d, a) {
        let r = e.bones[this.boneIndex];
        r.active && (r.x = this.getRelativeValue(i, n, d, r.x, r.data.x));
      }
    },
    Kt = class extends ke {
      boneIndex = 0;
      constructor(e, t, i) {
        super(e, t, re.y + "|" + i), (this.boneIndex = i);
      }
      apply(e, t, i, s, n, d, a) {
        let r = e.bones[this.boneIndex];
        r.active && (r.y = this.getRelativeValue(i, n, d, r.y, r.data.y));
      }
    },
    Qt = class extends At {
      boneIndex = 0;
      constructor(e, t, i) {
        super(e, t, re.scaleX + "|" + i, re.scaleY + "|" + i),
          (this.boneIndex = i);
      }
      apply(e, t, i, s, n, d, a) {
        let r = e.bones[this.boneIndex];
        if (!r.active) return;
        let o = this.frames;
        if (i < o[0]) {
          switch (d) {
            case 0:
              (r.scaleX = r.data.scaleX), (r.scaleY = r.data.scaleY);
              return;
            case 1:
              (r.scaleX += (r.data.scaleX - r.scaleX) * n),
                (r.scaleY += (r.data.scaleY - r.scaleY) * n);
          }
          return;
        }
        let l,
          h,
          u = me.search(o, i, 3),
          c = this.curves[u / 3];
        switch (c) {
          case 0:
            let f = o[u];
            (l = o[u + 1]), (h = o[u + 2]);
            let m = (i - f) / (o[u + 3] - f);
            (l += (o[u + 3 + 1] - l) * m), (h += (o[u + 3 + 2] - h) * m);
            break;
          case 1:
            (l = o[u + 1]), (h = o[u + 2]);
            break;
          default:
            (l = this.getBezierValue(i, u, 1, c - 2)),
              (h = this.getBezierValue(i, u, 2, c + 18 - 2));
        }
        if (((l *= r.data.scaleX), (h *= r.data.scaleY), n == 1))
          d == 3
            ? ((r.scaleX += l - r.data.scaleX), (r.scaleY += h - r.data.scaleY))
            : ((r.scaleX = l), (r.scaleY = h));
        else {
          let f = 0,
            m = 0;
          if (a == 1)
            switch (d) {
              case 0:
                (f = r.data.scaleX),
                  (m = r.data.scaleY),
                  (r.scaleX = f + (Math.abs(l) * X.signum(f) - f) * n),
                  (r.scaleY = m + (Math.abs(h) * X.signum(m) - m) * n);
                break;
              case 1:
              case 2:
                (f = r.scaleX),
                  (m = r.scaleY),
                  (r.scaleX = f + (Math.abs(l) * X.signum(f) - f) * n),
                  (r.scaleY = m + (Math.abs(h) * X.signum(m) - m) * n);
                break;
              case 3:
                (r.scaleX += (l - r.data.scaleX) * n),
                  (r.scaleY += (h - r.data.scaleY) * n);
            }
          else
            switch (d) {
              case 0:
                (f = Math.abs(r.data.scaleX) * X.signum(l)),
                  (m = Math.abs(r.data.scaleY) * X.signum(h)),
                  (r.scaleX = f + (l - f) * n),
                  (r.scaleY = m + (h - m) * n);
                break;
              case 1:
              case 2:
                (f = Math.abs(r.scaleX) * X.signum(l)),
                  (m = Math.abs(r.scaleY) * X.signum(h)),
                  (r.scaleX = f + (l - f) * n),
                  (r.scaleY = m + (h - m) * n);
                break;
              case 3:
                (r.scaleX += (l - r.data.scaleX) * n),
                  (r.scaleY += (h - r.data.scaleY) * n);
            }
        }
      }
    },
    $t = class extends ke {
      boneIndex = 0;
      constructor(e, t, i) {
        super(e, t, re.scaleX + "|" + i), (this.boneIndex = i);
      }
      apply(e, t, i, s, n, d, a) {
        let r = e.bones[this.boneIndex];
        r.active &&
          (r.scaleX = this.getScaleValue(i, n, d, a, r.scaleX, r.data.scaleX));
      }
    },
    ei = class extends ke {
      boneIndex = 0;
      constructor(e, t, i) {
        super(e, t, re.scaleY + "|" + i), (this.boneIndex = i);
      }
      apply(e, t, i, s, n, d, a) {
        let r = e.bones[this.boneIndex];
        r.active &&
          (r.scaleY = this.getScaleValue(i, n, d, a, r.scaleY, r.data.scaleY));
      }
    },
    ti = class extends At {
      boneIndex = 0;
      constructor(e, t, i) {
        super(e, t, re.shearX + "|" + i, re.shearY + "|" + i),
          (this.boneIndex = i);
      }
      apply(e, t, i, s, n, d, a) {
        let r = e.bones[this.boneIndex];
        if (!r.active) return;
        let o = this.frames;
        if (i < o[0]) {
          switch (d) {
            case 0:
              (r.shearX = r.data.shearX), (r.shearY = r.data.shearY);
              return;
            case 1:
              (r.shearX += (r.data.shearX - r.shearX) * n),
                (r.shearY += (r.data.shearY - r.shearY) * n);
          }
          return;
        }
        let l = 0,
          h = 0,
          u = me.search(o, i, 3),
          c = this.curves[u / 3];
        switch (c) {
          case 0:
            let f = o[u];
            (l = o[u + 1]), (h = o[u + 2]);
            let m = (i - f) / (o[u + 3] - f);
            (l += (o[u + 3 + 1] - l) * m), (h += (o[u + 3 + 2] - h) * m);
            break;
          case 1:
            (l = o[u + 1]), (h = o[u + 2]);
            break;
          default:
            (l = this.getBezierValue(i, u, 1, c - 2)),
              (h = this.getBezierValue(i, u, 2, c + 18 - 2));
        }
        switch (d) {
          case 0:
            (r.shearX = r.data.shearX + l * n),
              (r.shearY = r.data.shearY + h * n);
            break;
          case 1:
          case 2:
            (r.shearX += (r.data.shearX + l - r.shearX) * n),
              (r.shearY += (r.data.shearY + h - r.shearY) * n);
            break;
          case 3:
            (r.shearX += l * n), (r.shearY += h * n);
        }
      }
    },
    ii = class extends ke {
      boneIndex = 0;
      constructor(e, t, i) {
        super(e, t, re.shearX + "|" + i), (this.boneIndex = i);
      }
      apply(e, t, i, s, n, d, a) {
        let r = e.bones[this.boneIndex];
        r.active &&
          (r.shearX = this.getRelativeValue(i, n, d, r.shearX, r.data.shearX));
      }
    },
    si = class extends ke {
      boneIndex = 0;
      constructor(e, t, i) {
        super(e, t, re.shearY + "|" + i), (this.boneIndex = i);
      }
      apply(e, t, i, s, n, d, a) {
        let r = e.bones[this.boneIndex];
        r.active &&
          (r.shearY = this.getRelativeValue(i, n, d, r.shearY, r.data.shearY));
      }
    },
    ri = class extends me {
      boneIndex = 0;
      constructor(e, t) {
        super(e, [re.inherit + "|" + t]), (this.boneIndex = t);
      }
      getFrameEntries() {
        return 2;
      }
      setFrame(e, t, i) {
        (e *= 2), (this.frames[e] = t), (this.frames[e + 1] = i);
      }
      apply(e, t, i, s, n, d, a) {
        let r = e.bones[this.boneIndex];
        if (!r.active) return;
        if (a == 1) {
          d == 0 && (r.inherit = r.data.inherit);
          return;
        }
        let o = this.frames;
        if (i < o[0]) {
          (d == 0 || d == 1) && (r.inherit = r.data.inherit);
          return;
        }
        r.inherit = this.frames[me.search(o, i, 2) + 1];
      }
    },
    ni = class extends Fe {
      slotIndex = 0;
      constructor(e, t, i) {
        super(e, t, [re.rgb + "|" + i, re.alpha + "|" + i]),
          (this.slotIndex = i);
      }
      getFrameEntries() {
        return 5;
      }
      setFrame(e, t, i, s, n, d) {
        (e *= 5),
          (this.frames[e] = t),
          (this.frames[e + 1] = i),
          (this.frames[e + 2] = s),
          (this.frames[e + 3] = n),
          (this.frames[e + 4] = d);
      }
      apply(e, t, i, s, n, d, a) {
        let r = e.slots[this.slotIndex];
        if (!r.bone.active) return;
        let o = this.frames,
          l = r.color;
        if (i < o[0]) {
          let b = r.data.color;
          switch (d) {
            case 0:
              l.setFromColor(b);
              return;
            case 1:
              l.add(
                (b.r - l.r) * n,
                (b.g - l.g) * n,
                (b.b - l.b) * n,
                (b.a - l.a) * n
              );
          }
          return;
        }
        let h = 0,
          u = 0,
          c = 0,
          f = 0,
          m = me.search(o, i, 5),
          g = this.curves[m / 5];
        switch (g) {
          case 0:
            let b = o[m];
            (h = o[m + 1]), (u = o[m + 2]), (c = o[m + 3]), (f = o[m + 4]);
            let x = (i - b) / (o[m + 5] - b);
            (h += (o[m + 5 + 1] - h) * x),
              (u += (o[m + 5 + 2] - u) * x),
              (c += (o[m + 5 + 3] - c) * x),
              (f += (o[m + 5 + 4] - f) * x);
            break;
          case 1:
            (h = o[m + 1]), (u = o[m + 2]), (c = o[m + 3]), (f = o[m + 4]);
            break;
          default:
            (h = this.getBezierValue(i, m, 1, g - 2)),
              (u = this.getBezierValue(i, m, 2, g + 18 - 2)),
              (c = this.getBezierValue(i, m, 3, g + 18 * 2 - 2)),
              (f = this.getBezierValue(i, m, 4, g + 18 * 3 - 2));
        }
        n == 1
          ? l.set(h, u, c, f)
          : (d == 0 && l.setFromColor(r.data.color),
            l.add((h - l.r) * n, (u - l.g) * n, (c - l.b) * n, (f - l.a) * n));
      }
    },
    ai = class extends Fe {
      slotIndex = 0;
      constructor(e, t, i) {
        super(e, t, [re.rgb + "|" + i]), (this.slotIndex = i);
      }
      getFrameEntries() {
        return 4;
      }
      setFrame(e, t, i, s, n) {
        (e <<= 2),
          (this.frames[e] = t),
          (this.frames[e + 1] = i),
          (this.frames[e + 2] = s),
          (this.frames[e + 3] = n);
      }
      apply(e, t, i, s, n, d, a) {
        let r = e.slots[this.slotIndex];
        if (!r.bone.active) return;
        let o = this.frames,
          l = r.color;
        if (i < o[0]) {
          let g = r.data.color;
          switch (d) {
            case 0:
              (l.r = g.r), (l.g = g.g), (l.b = g.b);
              return;
            case 1:
              (l.r += (g.r - l.r) * n),
                (l.g += (g.g - l.g) * n),
                (l.b += (g.b - l.b) * n);
          }
          return;
        }
        let h = 0,
          u = 0,
          c = 0,
          f = me.search(o, i, 4),
          m = this.curves[f >> 2];
        switch (m) {
          case 0:
            let g = o[f];
            (h = o[f + 1]), (u = o[f + 2]), (c = o[f + 3]);
            let b = (i - g) / (o[f + 4] - g);
            (h += (o[f + 4 + 1] - h) * b),
              (u += (o[f + 4 + 2] - u) * b),
              (c += (o[f + 4 + 3] - c) * b);
            break;
          case 1:
            (h = o[f + 1]), (u = o[f + 2]), (c = o[f + 3]);
            break;
          default:
            (h = this.getBezierValue(i, f, 1, m - 2)),
              (u = this.getBezierValue(i, f, 2, m + 18 - 2)),
              (c = this.getBezierValue(i, f, 3, m + 18 * 2 - 2));
        }
        if (n == 1) (l.r = h), (l.g = u), (l.b = c);
        else {
          if (d == 0) {
            let g = r.data.color;
            (l.r = g.r), (l.g = g.g), (l.b = g.b);
          }
          (l.r += (h - l.r) * n),
            (l.g += (u - l.g) * n),
            (l.b += (c - l.b) * n);
        }
      }
    },
    li = class extends ke {
      slotIndex = 0;
      constructor(e, t, i) {
        super(e, t, re.alpha + "|" + i), (this.slotIndex = i);
      }
      apply(e, t, i, s, n, d, a) {
        let r = e.slots[this.slotIndex];
        if (!r.bone.active) return;
        let o = r.color;
        if (i < this.frames[0]) {
          let h = r.data.color;
          switch (d) {
            case 0:
              o.a = h.a;
              return;
            case 1:
              o.a += (h.a - o.a) * n;
          }
          return;
        }
        let l = this.getCurveValue(i);
        n == 1
          ? (o.a = l)
          : (d == 0 && (o.a = r.data.color.a), (o.a += (l - o.a) * n));
      }
    },
    oi = class extends Fe {
      slotIndex = 0;
      constructor(e, t, i) {
        super(e, t, [re.rgb + "|" + i, re.alpha + "|" + i, re.rgb2 + "|" + i]),
          (this.slotIndex = i);
      }
      getFrameEntries() {
        return 8;
      }
      setFrame(e, t, i, s, n, d, a, r, o) {
        (e <<= 3),
          (this.frames[e] = t),
          (this.frames[e + 1] = i),
          (this.frames[e + 2] = s),
          (this.frames[e + 3] = n),
          (this.frames[e + 4] = d),
          (this.frames[e + 5] = a),
          (this.frames[e + 6] = r),
          (this.frames[e + 7] = o);
      }
      apply(e, t, i, s, n, d, a) {
        let r = e.slots[this.slotIndex];
        if (!r.bone.active) return;
        let o = this.frames,
          l = r.color,
          h = r.darkColor;
        if (i < o[0]) {
          let y = r.data.color,
            v = r.data.darkColor;
          switch (d) {
            case 0:
              l.setFromColor(y), (h.r = v.r), (h.g = v.g), (h.b = v.b);
              return;
            case 1:
              l.add(
                (y.r - l.r) * n,
                (y.g - l.g) * n,
                (y.b - l.b) * n,
                (y.a - l.a) * n
              ),
                (h.r += (v.r - h.r) * n),
                (h.g += (v.g - h.g) * n),
                (h.b += (v.b - h.b) * n);
          }
          return;
        }
        let u = 0,
          c = 0,
          f = 0,
          m = 0,
          g = 0,
          b = 0,
          x = 0,
          p = me.search(o, i, 8),
          w = this.curves[p >> 3];
        switch (w) {
          case 0:
            let y = o[p];
            (u = o[p + 1]),
              (c = o[p + 2]),
              (f = o[p + 3]),
              (m = o[p + 4]),
              (g = o[p + 5]),
              (b = o[p + 6]),
              (x = o[p + 7]);
            let v = (i - y) / (o[p + 8] - y);
            (u += (o[p + 8 + 1] - u) * v),
              (c += (o[p + 8 + 2] - c) * v),
              (f += (o[p + 8 + 3] - f) * v),
              (m += (o[p + 8 + 4] - m) * v),
              (g += (o[p + 8 + 5] - g) * v),
              (b += (o[p + 8 + 6] - b) * v),
              (x += (o[p + 8 + 7] - x) * v);
            break;
          case 1:
            (u = o[p + 1]),
              (c = o[p + 2]),
              (f = o[p + 3]),
              (m = o[p + 4]),
              (g = o[p + 5]),
              (b = o[p + 6]),
              (x = o[p + 7]);
            break;
          default:
            (u = this.getBezierValue(i, p, 1, w - 2)),
              (c = this.getBezierValue(i, p, 2, w + 18 - 2)),
              (f = this.getBezierValue(i, p, 3, w + 18 * 2 - 2)),
              (m = this.getBezierValue(i, p, 4, w + 18 * 3 - 2)),
              (g = this.getBezierValue(i, p, 5, w + 18 * 4 - 2)),
              (b = this.getBezierValue(i, p, 6, w + 18 * 5 - 2)),
              (x = this.getBezierValue(i, p, 7, w + 18 * 6 - 2));
        }
        if (n == 1) l.set(u, c, f, m), (h.r = g), (h.g = b), (h.b = x);
        else {
          if (d == 0) {
            l.setFromColor(r.data.color);
            let y = r.data.darkColor;
            (h.r = y.r), (h.g = y.g), (h.b = y.b);
          }
          l.add((u - l.r) * n, (c - l.g) * n, (f - l.b) * n, (m - l.a) * n),
            (h.r += (g - h.r) * n),
            (h.g += (b - h.g) * n),
            (h.b += (x - h.b) * n);
        }
      }
    },
    hi = class extends Fe {
      slotIndex = 0;
      constructor(e, t, i) {
        super(e, t, [re.rgb + "|" + i, re.rgb2 + "|" + i]),
          (this.slotIndex = i);
      }
      getFrameEntries() {
        return 7;
      }
      setFrame(e, t, i, s, n, d, a, r) {
        (e *= 7),
          (this.frames[e] = t),
          (this.frames[e + 1] = i),
          (this.frames[e + 2] = s),
          (this.frames[e + 3] = n),
          (this.frames[e + 4] = d),
          (this.frames[e + 5] = a),
          (this.frames[e + 6] = r);
      }
      apply(e, t, i, s, n, d, a) {
        let r = e.slots[this.slotIndex];
        if (!r.bone.active) return;
        let o = this.frames,
          l = r.color,
          h = r.darkColor;
        if (i < o[0]) {
          let y = r.data.color,
            v = r.data.darkColor;
          switch (d) {
            case 0:
              (l.r = y.r),
                (l.g = y.g),
                (l.b = y.b),
                (h.r = v.r),
                (h.g = v.g),
                (h.b = v.b);
              return;
            case 1:
              (l.r += (y.r - l.r) * n),
                (l.g += (y.g - l.g) * n),
                (l.b += (y.b - l.b) * n),
                (h.r += (v.r - h.r) * n),
                (h.g += (v.g - h.g) * n),
                (h.b += (v.b - h.b) * n);
          }
          return;
        }
        let u = 0,
          c = 0,
          f = 0,
          m = 0,
          g = 0,
          b = 0,
          x = 0,
          p = me.search(o, i, 7),
          w = this.curves[p / 7];
        switch (w) {
          case 0:
            let y = o[p];
            (u = o[p + 1]),
              (c = o[p + 2]),
              (f = o[p + 3]),
              (g = o[p + 4]),
              (b = o[p + 5]),
              (x = o[p + 6]);
            let v = (i - y) / (o[p + 7] - y);
            (u += (o[p + 7 + 1] - u) * v),
              (c += (o[p + 7 + 2] - c) * v),
              (f += (o[p + 7 + 3] - f) * v),
              (g += (o[p + 7 + 4] - g) * v),
              (b += (o[p + 7 + 5] - b) * v),
              (x += (o[p + 7 + 6] - x) * v);
            break;
          case 1:
            (u = o[p + 1]),
              (c = o[p + 2]),
              (f = o[p + 3]),
              (g = o[p + 4]),
              (b = o[p + 5]),
              (x = o[p + 6]);
            break;
          default:
            (u = this.getBezierValue(i, p, 1, w - 2)),
              (c = this.getBezierValue(i, p, 2, w + 18 - 2)),
              (f = this.getBezierValue(i, p, 3, w + 18 * 2 - 2)),
              (g = this.getBezierValue(i, p, 4, w + 18 * 3 - 2)),
              (b = this.getBezierValue(i, p, 5, w + 18 * 4 - 2)),
              (x = this.getBezierValue(i, p, 6, w + 18 * 5 - 2));
        }
        if (n == 1)
          (l.r = u), (l.g = c), (l.b = f), (h.r = g), (h.g = b), (h.b = x);
        else {
          if (d == 0) {
            let y = r.data.color,
              v = r.data.darkColor;
            (l.r = y.r),
              (l.g = y.g),
              (l.b = y.b),
              (h.r = v.r),
              (h.g = v.g),
              (h.b = v.b);
          }
          (l.r += (u - l.r) * n),
            (l.g += (c - l.g) * n),
            (l.b += (f - l.b) * n),
            (h.r += (g - h.r) * n),
            (h.g += (b - h.g) * n),
            (h.b += (x - h.b) * n);
        }
      }
    },
    He = class extends me {
      slotIndex = 0;
      attachmentNames;
      constructor(e, t) {
        super(e, [re.attachment + "|" + t]),
          (this.slotIndex = t),
          (this.attachmentNames = new Array(e));
      }
      getFrameCount() {
        return this.frames.length;
      }
      setFrame(e, t, i) {
        (this.frames[e] = t), (this.attachmentNames[e] = i);
      }
      apply(e, t, i, s, n, d, a) {
        let r = e.slots[this.slotIndex];
        if (r.bone.active) {
          if (a == 1) {
            d == 0 && this.setAttachment(e, r, r.data.attachmentName);
            return;
          }
          if (i < this.frames[0]) {
            (d == 0 || d == 1) &&
              this.setAttachment(e, r, r.data.attachmentName);
            return;
          }
          this.setAttachment(
            e,
            r,
            this.attachmentNames[me.search1(this.frames, i)]
          );
        }
      }
      setAttachment(e, t, i) {
        t.setAttachment(i ? e.getAttachment(this.slotIndex, i) : null);
      }
    },
    di = class extends Fe {
      slotIndex = 0;
      attachment;
      vertices;
      constructor(e, t, i, s) {
        super(e, t, [re.deform + "|" + i + "|" + s.id]),
          (this.slotIndex = i),
          (this.attachment = s),
          (this.vertices = new Array(e));
      }
      getFrameCount() {
        return this.frames.length;
      }
      setFrame(e, t, i) {
        (this.frames[e] = t), (this.vertices[e] = i);
      }
      setBezier(e, t, i, s, n, d, a, r, o, l, h) {
        let u = this.curves,
          c = this.getFrameCount() + e * 18;
        i == 0 && (u[t] = 2 + c);
        let f = (s - d * 2 + r) * 0.03,
          m = o * 0.03 - a * 0.06,
          g = ((d - r) * 3 - s + l) * 0.006,
          b = (a - o + 0.33333333) * 0.018,
          x = f * 2 + g,
          p = m * 2 + b,
          w = (d - s) * 0.3 + f + g * 0.16666667,
          y = a * 0.3 + m + b * 0.16666667,
          v = s + w,
          A = y;
        for (let C = c + 18; c < C; c += 2)
          (u[c] = v),
            (u[c + 1] = A),
            (w += x),
            (y += p),
            (x += g),
            (p += b),
            (v += w),
            (A += y);
      }
      getCurvePercent(e, t) {
        let i = this.curves,
          s = i[t];
        switch (s) {
          case 0:
            let r = this.frames[t];
            return (e - r) / (this.frames[t + this.getFrameEntries()] - r);
          case 1:
            return 0;
        }
        if (((s -= 2), i[s] > e)) {
          let r = this.frames[t];
          return (i[s + 1] * (e - r)) / (i[s] - r);
        }
        let n = s + 18;
        for (s += 2; s < n; s += 2)
          if (i[s] >= e) {
            let r = i[s - 2],
              o = i[s - 1];
            return o + ((e - r) / (i[s] - r)) * (i[s + 1] - o);
          }
        let d = i[n - 2],
          a = i[n - 1];
        return (
          a +
          ((1 - a) * (e - d)) / (this.frames[t + this.getFrameEntries()] - d)
        );
      }
      apply(e, t, i, s, n, d, a) {
        let r = e.slots[this.slotIndex];
        if (!r.bone.active) return;
        let o = r.getAttachment();
        if (!o || !(o instanceof Re) || o.timelineAttachment != this.attachment)
          return;
        let l = r.deform;
        l.length == 0 && (d = 0);
        let h = this.vertices,
          u = h[0].length,
          c = this.frames;
        if (i < c[0]) {
          switch (d) {
            case 0:
              l.length = 0;
              return;
            case 1:
              if (n == 1) {
                l.length = 0;
                return;
              }
              l.length = u;
              let p = o;
              if (p.bones) {
                n = 1 - n;
                for (var f = 0; f < u; f++) l[f] *= n;
              } else {
                let w = p.vertices;
                for (var f = 0; f < u; f++) l[f] += (w[f] - l[f]) * n;
              }
          }
          return;
        }
        if (((l.length = u), i >= c[c.length - 1])) {
          let p = h[c.length - 1];
          if (n == 1)
            if (d == 3) {
              let w = o;
              if (w.bones) for (let y = 0; y < u; y++) l[y] += p[y];
              else {
                let y = w.vertices;
                for (let v = 0; v < u; v++) l[v] += p[v] - y[v];
              }
            } else P.arrayCopy(p, 0, l, 0, u);
          else
            switch (d) {
              case 0: {
                let y = o;
                if (y.bones) for (let v = 0; v < u; v++) l[v] = p[v] * n;
                else {
                  let v = y.vertices;
                  for (let A = 0; A < u; A++) {
                    let C = v[A];
                    l[A] = C + (p[A] - C) * n;
                  }
                }
                break;
              }
              case 1:
              case 2:
                for (let y = 0; y < u; y++) l[y] += (p[y] - l[y]) * n;
                break;
              case 3:
                let w = o;
                if (w.bones) for (let y = 0; y < u; y++) l[y] += p[y] * n;
                else {
                  let y = w.vertices;
                  for (let v = 0; v < u; v++) l[v] += (p[v] - y[v]) * n;
                }
            }
          return;
        }
        let m = me.search1(c, i),
          g = this.getCurvePercent(i, m),
          b = h[m],
          x = h[m + 1];
        if (n == 1)
          if (d == 3) {
            let p = o;
            if (p.bones)
              for (let w = 0; w < u; w++) {
                let y = b[w];
                l[w] += y + (x[w] - y) * g;
              }
            else {
              let w = p.vertices;
              for (let y = 0; y < u; y++) {
                let v = b[y];
                l[y] += v + (x[y] - v) * g - w[y];
              }
            }
          } else
            for (let p = 0; p < u; p++) {
              let w = b[p];
              l[p] = w + (x[p] - w) * g;
            }
        else
          switch (d) {
            case 0: {
              let w = o;
              if (w.bones)
                for (let y = 0; y < u; y++) {
                  let v = b[y];
                  l[y] = (v + (x[y] - v) * g) * n;
                }
              else {
                let y = w.vertices;
                for (let v = 0; v < u; v++) {
                  let A = b[v],
                    C = y[v];
                  l[v] = C + (A + (x[v] - A) * g - C) * n;
                }
              }
              break;
            }
            case 1:
            case 2:
              for (let w = 0; w < u; w++) {
                let y = b[w];
                l[w] += (y + (x[w] - y) * g - l[w]) * n;
              }
              break;
            case 3:
              let p = o;
              if (p.bones)
                for (let w = 0; w < u; w++) {
                  let y = b[w];
                  l[w] += (y + (x[w] - y) * g) * n;
                }
              else {
                let w = p.vertices;
                for (let y = 0; y < u; y++) {
                  let v = b[y];
                  l[y] += (v + (x[y] - v) * g - w[y]) * n;
                }
              }
          }
      }
    },
    hs = class extends me {
      events;
      constructor(e) {
        super(e, hs.propertyIds), (this.events = new Array(e));
      }
      getFrameCount() {
        return this.frames.length;
      }
      setFrame(e, t) {
        (this.frames[e] = t.time), (this.events[e] = t);
      }
      apply(e, t, i, s, n, d, a) {
        if (!s) return;
        let r = this.frames,
          o = this.frames.length;
        if (t > i) this.apply(e, t, Number.MAX_VALUE, s, n, d, a), (t = -1);
        else if (t >= r[o - 1]) return;
        if (i < r[0]) return;
        let l = 0;
        if (t < r[0]) l = 0;
        else {
          l = me.search1(r, t) + 1;
          let h = r[l];
          for (; l > 0 && r[l - 1] == h; ) l--;
        }
        for (; l < o && i >= r[l]; l++) s.push(this.events[l]);
      }
    },
    ht = hs;
  B(ht, "propertyIds", ["" + re.event]);
  var ds = class extends me {
      drawOrders;
      constructor(e) {
        super(e, ds.propertyIds), (this.drawOrders = new Array(e));
      }
      getFrameCount() {
        return this.frames.length;
      }
      setFrame(e, t, i) {
        (this.frames[e] = t), (this.drawOrders[e] = i);
      }
      apply(e, t, i, s, n, d, a) {
        if (a == 1) {
          d == 0 && P.arrayCopy(e.slots, 0, e.drawOrder, 0, e.slots.length);
          return;
        }
        if (i < this.frames[0]) {
          (d == 0 || d == 1) &&
            P.arrayCopy(e.slots, 0, e.drawOrder, 0, e.slots.length);
          return;
        }
        let r = me.search1(this.frames, i),
          o = this.drawOrders[r];
        if (!o) P.arrayCopy(e.slots, 0, e.drawOrder, 0, e.slots.length);
        else {
          let l = e.drawOrder,
            h = e.slots;
          for (let u = 0, c = o.length; u < c; u++) l[u] = h[o[u]];
        }
      }
    },
    je = ds;
  B(je, "propertyIds", ["" + re.drawOrder]);
  var ci = class extends Fe {
      constraintIndex = 0;
      constructor(e, t, i) {
        super(e, t, [re.ikConstraint + "|" + i]), (this.constraintIndex = i);
      }
      getFrameEntries() {
        return 6;
      }
      setFrame(e, t, i, s, n, d, a) {
        (e *= 6),
          (this.frames[e] = t),
          (this.frames[e + 1] = i),
          (this.frames[e + 2] = s),
          (this.frames[e + 3] = n),
          (this.frames[e + 4] = d ? 1 : 0),
          (this.frames[e + 5] = a ? 1 : 0);
      }
      apply(e, t, i, s, n, d, a) {
        let r = e.ikConstraints[this.constraintIndex];
        if (!r.active) return;
        let o = this.frames;
        if (i < o[0]) {
          switch (d) {
            case 0:
              (r.mix = r.data.mix),
                (r.softness = r.data.softness),
                (r.bendDirection = r.data.bendDirection),
                (r.compress = r.data.compress),
                (r.stretch = r.data.stretch);
              return;
            case 1:
              (r.mix += (r.data.mix - r.mix) * n),
                (r.softness += (r.data.softness - r.softness) * n),
                (r.bendDirection = r.data.bendDirection),
                (r.compress = r.data.compress),
                (r.stretch = r.data.stretch);
          }
          return;
        }
        let l = 0,
          h = 0,
          u = me.search(o, i, 6),
          c = this.curves[u / 6];
        switch (c) {
          case 0:
            let f = o[u];
            (l = o[u + 1]), (h = o[u + 2]);
            let m = (i - f) / (o[u + 6] - f);
            (l += (o[u + 6 + 1] - l) * m), (h += (o[u + 6 + 2] - h) * m);
            break;
          case 1:
            (l = o[u + 1]), (h = o[u + 2]);
            break;
          default:
            (l = this.getBezierValue(i, u, 1, c - 2)),
              (h = this.getBezierValue(i, u, 2, c + 18 - 2));
        }
        d == 0
          ? ((r.mix = r.data.mix + (l - r.data.mix) * n),
            (r.softness = r.data.softness + (h - r.data.softness) * n),
            a == 1
              ? ((r.bendDirection = r.data.bendDirection),
                (r.compress = r.data.compress),
                (r.stretch = r.data.stretch))
              : ((r.bendDirection = o[u + 3]),
                (r.compress = o[u + 4] != 0),
                (r.stretch = o[u + 5] != 0)))
          : ((r.mix += (l - r.mix) * n),
            (r.softness += (h - r.softness) * n),
            a == 0 &&
              ((r.bendDirection = o[u + 3]),
              (r.compress = o[u + 4] != 0),
              (r.stretch = o[u + 5] != 0)));
      }
    },
    ui = class extends Fe {
      constraintIndex = 0;
      constructor(e, t, i) {
        super(e, t, [re.transformConstraint + "|" + i]),
          (this.constraintIndex = i);
      }
      getFrameEntries() {
        return 7;
      }
      setFrame(e, t, i, s, n, d, a, r) {
        let o = this.frames;
        (e *= 7),
          (o[e] = t),
          (o[e + 1] = i),
          (o[e + 2] = s),
          (o[e + 3] = n),
          (o[e + 4] = d),
          (o[e + 5] = a),
          (o[e + 6] = r);
      }
      apply(e, t, i, s, n, d, a) {
        let r = e.transformConstraints[this.constraintIndex];
        if (!r.active) return;
        let o = this.frames;
        if (i < o[0]) {
          let x = r.data;
          switch (d) {
            case 0:
              (r.mixRotate = x.mixRotate),
                (r.mixX = x.mixX),
                (r.mixY = x.mixY),
                (r.mixScaleX = x.mixScaleX),
                (r.mixScaleY = x.mixScaleY),
                (r.mixShearY = x.mixShearY);
              return;
            case 1:
              (r.mixRotate += (x.mixRotate - r.mixRotate) * n),
                (r.mixX += (x.mixX - r.mixX) * n),
                (r.mixY += (x.mixY - r.mixY) * n),
                (r.mixScaleX += (x.mixScaleX - r.mixScaleX) * n),
                (r.mixScaleY += (x.mixScaleY - r.mixScaleY) * n),
                (r.mixShearY += (x.mixShearY - r.mixShearY) * n);
          }
          return;
        }
        let l,
          h,
          u,
          c,
          f,
          m,
          g = me.search(o, i, 7),
          b = this.curves[g / 7];
        switch (b) {
          case 0:
            let x = o[g];
            (l = o[g + 1]),
              (h = o[g + 2]),
              (u = o[g + 3]),
              (c = o[g + 4]),
              (f = o[g + 5]),
              (m = o[g + 6]);
            let p = (i - x) / (o[g + 7] - x);
            (l += (o[g + 7 + 1] - l) * p),
              (h += (o[g + 7 + 2] - h) * p),
              (u += (o[g + 7 + 3] - u) * p),
              (c += (o[g + 7 + 4] - c) * p),
              (f += (o[g + 7 + 5] - f) * p),
              (m += (o[g + 7 + 6] - m) * p);
            break;
          case 1:
            (l = o[g + 1]),
              (h = o[g + 2]),
              (u = o[g + 3]),
              (c = o[g + 4]),
              (f = o[g + 5]),
              (m = o[g + 6]);
            break;
          default:
            (l = this.getBezierValue(i, g, 1, b - 2)),
              (h = this.getBezierValue(i, g, 2, b + 18 - 2)),
              (u = this.getBezierValue(i, g, 3, b + 18 * 2 - 2)),
              (c = this.getBezierValue(i, g, 4, b + 18 * 3 - 2)),
              (f = this.getBezierValue(i, g, 5, b + 18 * 4 - 2)),
              (m = this.getBezierValue(i, g, 6, b + 18 * 5 - 2));
        }
        if (d == 0) {
          let x = r.data;
          (r.mixRotate = x.mixRotate + (l - x.mixRotate) * n),
            (r.mixX = x.mixX + (h - x.mixX) * n),
            (r.mixY = x.mixY + (u - x.mixY) * n),
            (r.mixScaleX = x.mixScaleX + (c - x.mixScaleX) * n),
            (r.mixScaleY = x.mixScaleY + (f - x.mixScaleY) * n),
            (r.mixShearY = x.mixShearY + (m - x.mixShearY) * n);
        } else
          (r.mixRotate += (l - r.mixRotate) * n),
            (r.mixX += (h - r.mixX) * n),
            (r.mixY += (u - r.mixY) * n),
            (r.mixScaleX += (c - r.mixScaleX) * n),
            (r.mixScaleY += (f - r.mixScaleY) * n),
            (r.mixShearY += (m - r.mixShearY) * n);
      }
    },
    fi = class extends ke {
      constraintIndex = 0;
      constructor(e, t, i) {
        super(e, t, re.pathConstraintPosition + "|" + i),
          (this.constraintIndex = i);
      }
      apply(e, t, i, s, n, d, a) {
        let r = e.pathConstraints[this.constraintIndex];
        r.active &&
          (r.position = this.getAbsoluteValue(
            i,
            n,
            d,
            r.position,
            r.data.position
          ));
      }
    },
    mi = class extends ke {
      constraintIndex = 0;
      constructor(e, t, i) {
        super(e, t, re.pathConstraintSpacing + "|" + i),
          (this.constraintIndex = i);
      }
      apply(e, t, i, s, n, d, a) {
        let r = e.pathConstraints[this.constraintIndex];
        r.active &&
          (r.spacing = this.getAbsoluteValue(
            i,
            n,
            d,
            r.spacing,
            r.data.spacing
          ));
      }
    },
    gi = class extends Fe {
      constraintIndex = 0;
      constructor(e, t, i) {
        super(e, t, [re.pathConstraintMix + "|" + i]),
          (this.constraintIndex = i);
      }
      getFrameEntries() {
        return 4;
      }
      setFrame(e, t, i, s, n) {
        let d = this.frames;
        (e <<= 2), (d[e] = t), (d[e + 1] = i), (d[e + 2] = s), (d[e + 3] = n);
      }
      apply(e, t, i, s, n, d, a) {
        let r = e.pathConstraints[this.constraintIndex];
        if (!r.active) return;
        let o = this.frames;
        if (i < o[0]) {
          switch (d) {
            case 0:
              (r.mixRotate = r.data.mixRotate),
                (r.mixX = r.data.mixX),
                (r.mixY = r.data.mixY);
              return;
            case 1:
              (r.mixRotate += (r.data.mixRotate - r.mixRotate) * n),
                (r.mixX += (r.data.mixX - r.mixX) * n),
                (r.mixY += (r.data.mixY - r.mixY) * n);
          }
          return;
        }
        let l,
          h,
          u,
          c = me.search(o, i, 4),
          f = this.curves[c >> 2];
        switch (f) {
          case 0:
            let m = o[c];
            (l = o[c + 1]), (h = o[c + 2]), (u = o[c + 3]);
            let g = (i - m) / (o[c + 4] - m);
            (l += (o[c + 4 + 1] - l) * g),
              (h += (o[c + 4 + 2] - h) * g),
              (u += (o[c + 4 + 3] - u) * g);
            break;
          case 1:
            (l = o[c + 1]), (h = o[c + 2]), (u = o[c + 3]);
            break;
          default:
            (l = this.getBezierValue(i, c, 1, f - 2)),
              (h = this.getBezierValue(i, c, 2, f + 18 - 2)),
              (u = this.getBezierValue(i, c, 3, f + 18 * 2 - 2));
        }
        if (d == 0) {
          let m = r.data;
          (r.mixRotate = m.mixRotate + (l - m.mixRotate) * n),
            (r.mixX = m.mixX + (h - m.mixX) * n),
            (r.mixY = m.mixY + (u - m.mixY) * n);
        } else
          (r.mixRotate += (l - r.mixRotate) * n),
            (r.mixX += (h - r.mixX) * n),
            (r.mixY += (u - r.mixY) * n);
      }
    },
    De = class extends ke {
      constraintIndex = 0;
      constructor(e, t, i, s) {
        super(e, t, s + "|" + i), (this.constraintIndex = i);
      }
      apply(e, t, i, s, n, d, a) {
        let r;
        if (this.constraintIndex == -1) {
          const o = i >= this.frames[0] ? this.getCurveValue(i) : 0;
          for (const l of e.physicsConstraints)
            l.active &&
              this.global(l.data) &&
              this.set(
                l,
                this.getAbsoluteValue2(i, n, d, this.get(l), this.setup(l), o)
              );
        } else
          (r = e.physicsConstraints[this.constraintIndex]),
            r.active &&
              this.set(
                r,
                this.getAbsoluteValue(i, n, d, this.get(r), this.setup(r))
              );
      }
    },
    pi = class extends De {
      constructor(e, t, i) {
        super(e, t, i, re.physicsConstraintInertia);
      }
      setup(e) {
        return e.data.inertia;
      }
      get(e) {
        return e.inertia;
      }
      set(e, t) {
        e.inertia = t;
      }
      global(e) {
        return e.inertiaGlobal;
      }
    },
    wi = class extends De {
      constructor(e, t, i) {
        super(e, t, i, re.physicsConstraintStrength);
      }
      setup(e) {
        return e.data.strength;
      }
      get(e) {
        return e.strength;
      }
      set(e, t) {
        e.strength = t;
      }
      global(e) {
        return e.strengthGlobal;
      }
    },
    xi = class extends De {
      constructor(e, t, i) {
        super(e, t, i, re.physicsConstraintDamping);
      }
      setup(e) {
        return e.data.damping;
      }
      get(e) {
        return e.damping;
      }
      set(e, t) {
        e.damping = t;
      }
      global(e) {
        return e.dampingGlobal;
      }
    },
    bi = class extends De {
      constructor(e, t, i) {
        super(e, t, i, re.physicsConstraintMass);
      }
      setup(e) {
        return 1 / e.data.massInverse;
      }
      get(e) {
        return 1 / e.massInverse;
      }
      set(e, t) {
        e.massInverse = 1 / t;
      }
      global(e) {
        return e.massGlobal;
      }
    },
    vi = class extends De {
      constructor(e, t, i) {
        super(e, t, i, re.physicsConstraintWind);
      }
      setup(e) {
        return e.data.wind;
      }
      get(e) {
        return e.wind;
      }
      set(e, t) {
        e.wind = t;
      }
      global(e) {
        return e.windGlobal;
      }
    },
    yi = class extends De {
      constructor(e, t, i) {
        super(e, t, i, re.physicsConstraintGravity);
      }
      setup(e) {
        return e.data.gravity;
      }
      get(e) {
        return e.gravity;
      }
      set(e, t) {
        e.gravity = t;
      }
      global(e) {
        return e.gravityGlobal;
      }
    },
    Ai = class extends De {
      constructor(e, t, i) {
        super(e, t, i, re.physicsConstraintMix);
      }
      setup(e) {
        return e.data.mix;
      }
      get(e) {
        return e.mix;
      }
      set(e, t) {
        e.mix = t;
      }
      global(e) {
        return e.mixGlobal;
      }
    },
    cs = class extends me {
      constraintIndex;
      constructor(e, t) {
        super(e, cs.propertyIds), (this.constraintIndex = t);
      }
      getFrameCount() {
        return this.frames.length;
      }
      setFrame(e, t) {
        this.frames[e] = t;
      }
      apply(e, t, i, s, n, d, a) {
        let r;
        if (
          this.constraintIndex != -1 &&
          ((r = e.physicsConstraints[this.constraintIndex]), !r.active)
        )
          return;
        const o = this.frames;
        if (t > i) this.apply(e, t, Number.MAX_VALUE, [], n, d, a), (t = -1);
        else if (t >= o[o.length - 1]) return;
        if (!(i < o[0]) && (t < o[0] || i >= o[me.search1(o, t) + 1]))
          if (r != null) r.reset();
          else for (const l of e.physicsConstraints) l.active && l.reset();
      }
    },
    Ct = cs;
  B(Ct, "propertyIds", [re.physicsConstraintReset.toString()]);
  var Ne = class extends me {
      slotIndex;
      attachment;
      constructor(e, t, i) {
        super(e, [re.sequence + "|" + t + "|" + i.sequence.id]),
          (this.slotIndex = t),
          (this.attachment = i);
      }
      getFrameEntries() {
        return Ne.ENTRIES;
      }
      getSlotIndex() {
        return this.slotIndex;
      }
      getAttachment() {
        return this.attachment;
      }
      setFrame(e, t, i, s, n) {
        let d = this.frames;
        (e *= Ne.ENTRIES),
          (d[e] = t),
          (d[e + Ne.MODE] = i | (s << 4)),
          (d[e + Ne.DELAY] = n);
      }
      apply(e, t, i, s, n, d, a) {
        let r = e.slots[this.slotIndex];
        if (!r.bone.active) return;
        let o = r.attachment,
          l = this.attachment;
        if (o != l && (!(o instanceof Re) || o.timelineAttachment != l)) return;
        if (a == 1) {
          d == 0 && (r.sequenceIndex = -1);
          return;
        }
        let h = this.frames;
        if (i < h[0]) {
          (d == 0 || d == 1) && (r.sequenceIndex = -1);
          return;
        }
        let u = me.search(h, i, Ne.ENTRIES),
          c = h[u],
          f = h[u + Ne.MODE],
          m = h[u + Ne.DELAY];
        if (!this.attachment.sequence) return;
        let g = f >> 4,
          b = this.attachment.sequence.regions.length,
          x = as[f & 15];
        if (x != 0)
          switch (((g += ((i - c) / m + 1e-5) | 0), x)) {
            case 1:
              g = Math.min(b - 1, g);
              break;
            case 2:
              g %= b;
              break;
            case 3: {
              let p = (b << 1) - 2;
              (g = p == 0 ? 0 : g % p), g >= b && (g = p - g);
              break;
            }
            case 4:
              g = Math.max(b - 1 - g, 0);
              break;
            case 5:
              g = b - 1 - (g % b);
              break;
            case 6: {
              let p = (b << 1) - 2;
              (g = p == 0 ? 0 : (g + b - 1) % p), g >= b && (g = p - g);
            }
          }
        r.sequenceIndex = g;
      }
    },
    et = Ne;
  B(et, "ENTRIES", 3), B(et, "MODE", 1), B(et, "DELAY", 2);
  var St = class {
      static emptyAnimation() {
        return St._emptyAnimation;
      }
      data;
      tracks = new Array();
      timeScale = 1;
      unkeyedState = 0;
      events = new Array();
      listeners = new Array();
      queue = new fs(this);
      propertyIDs = new Gt();
      animationsChanged = !1;
      trackEntryPool = new lt(() => new us());
      constructor(e) {
        this.data = e;
      }
      update(e) {
        e *= this.timeScale;
        let t = this.tracks;
        for (let i = 0, s = t.length; i < s; i++) {
          let n = t[i];
          if (!n) continue;
          (n.animationLast = n.nextAnimationLast),
            (n.trackLast = n.nextTrackLast);
          let d = e * n.timeScale;
          if (n.delay > 0) {
            if (((n.delay -= d), n.delay > 0)) continue;
            (d = -n.delay), (n.delay = 0);
          }
          let a = n.next;
          if (a) {
            let r = n.trackLast - a.delay;
            if (r >= 0) {
              for (
                a.delay = 0,
                  a.trackTime +=
                    n.timeScale == 0 ? 0 : (r / n.timeScale + e) * a.timeScale,
                  n.trackTime += d,
                  this.setCurrent(i, a, !0);
                a.mixingFrom;

              )
                (a.mixTime += e), (a = a.mixingFrom);
              continue;
            }
          } else if (n.trackLast >= n.trackEnd && !n.mixingFrom) {
            (t[i] = null), this.queue.end(n), this.clearNext(n);
            continue;
          }
          if (n.mixingFrom && this.updateMixingFrom(n, e)) {
            let r = n.mixingFrom;
            for (n.mixingFrom = null, r && (r.mixingTo = null); r; )
              this.queue.end(r), (r = r.mixingFrom);
          }
          n.trackTime += d;
        }
        this.queue.drain();
      }
      updateMixingFrom(e, t) {
        let i = e.mixingFrom;
        if (!i) return !0;
        let s = this.updateMixingFrom(i, t);
        if (
          ((i.animationLast = i.nextAnimationLast),
          (i.trackLast = i.nextTrackLast),
          e.nextTrackLast != -1)
        ) {
          const n = e.mixTime == 0 && i.mixTime == 0;
          if (e.mixTime >= e.mixDuration || n)
            return (
              (i.totalAlpha == 0 || e.mixDuration == 0 || n) &&
                ((e.mixingFrom = i.mixingFrom),
                i.mixingFrom != null && (i.mixingFrom.mixingTo = e),
                (e.interruptAlpha = i.interruptAlpha),
                this.queue.end(i)),
              s
            );
        }
        return (i.trackTime += t * i.timeScale), (e.mixTime += t), !1;
      }
      apply(e) {
        if (!e) throw new Error("skeleton cannot be null.");
        this.animationsChanged && this._animationsChanged();
        let t = this.events,
          i = this.tracks,
          s = !1;
        for (let u = 0, c = i.length; u < c; u++) {
          let f = i[u];
          if (!f || f.delay > 0) continue;
          s = !0;
          let m = u == 0 ? 1 : f.mixBlend,
            g = f.alpha;
          f.mixingFrom
            ? (g *= this.applyMixingFrom(f, e, m))
            : f.trackTime >= f.trackEnd && !f.next && (g = 0);
          let b = g >= f.alphaAttachmentThreshold,
            x = f.animationLast,
            p = f.getAnimationTime(),
            w = p,
            y = t;
          f.reverse && ((w = f.animation.duration - w), (y = null));
          let v = f.animation.timelines,
            A = v.length;
          if ((u == 0 && g == 1) || m == 3) {
            u == 0 && (b = !0);
            for (let C = 0; C < A; C++) {
              P.webkit602BugfixHelper(g, m);
              var n = v[C];
              n instanceof He
                ? this.applyAttachmentTimeline(n, e, w, m, b)
                : n.apply(e, x, w, y, g, m, 0);
            }
          } else {
            let C = f.timelineMode,
              T = f.shortestRotation,
              I = !T && f.timelinesRotation.length != A << 1;
            I && (f.timelinesRotation.length = A << 1);
            for (let M = 0; M < A; M++) {
              let F = v[M],
                L = C[M] == Tt ? m : 0;
              !T && F instanceof ot
                ? this.applyRotateTimeline(
                    F,
                    e,
                    w,
                    g,
                    L,
                    f.timelinesRotation,
                    M << 1,
                    I
                  )
                : F instanceof He
                ? this.applyAttachmentTimeline(F, e, w, m, b)
                : (P.webkit602BugfixHelper(g, m), F.apply(e, x, w, y, g, L, 0));
            }
          }
          this.queueEvents(f, p),
            (t.length = 0),
            (f.nextAnimationLast = p),
            (f.nextTrackLast = f.trackTime);
        }
        for (
          var d = this.unkeyedState + ki,
            a = e.slots,
            r = 0,
            o = e.slots.length;
          r < o;
          r++
        ) {
          var l = a[r];
          if (l.attachmentState == d) {
            var h = l.data.attachmentName;
            l.setAttachment(h ? e.getAttachment(l.data.index, h) : null);
          }
        }
        return (this.unkeyedState += 2), this.queue.drain(), s;
      }
      applyMixingFrom(e, t, i) {
        let s = e.mixingFrom;
        s.mixingFrom && this.applyMixingFrom(s, t, i);
        let n = 0;
        e.mixDuration == 0
          ? ((n = 1), i == 1 && (i = 0))
          : ((n = e.mixTime / e.mixDuration),
            n > 1 && (n = 1),
            i != 1 && (i = s.mixBlend));
        let d = n < s.mixAttachmentThreshold,
          a = n < s.mixDrawOrderThreshold,
          r = s.animation.timelines,
          o = r.length,
          l = s.alpha * e.interruptAlpha,
          h = l * (1 - n),
          u = s.animationLast,
          c = s.getAnimationTime(),
          f = c,
          m = null;
        if (
          (s.reverse
            ? (f = s.animation.duration - f)
            : n < s.eventThreshold && (m = this.events),
          i == 3)
        )
          for (let g = 0; g < o; g++) r[g].apply(t, u, f, m, h, i, 1);
        else {
          let g = s.timelineMode,
            b = s.timelineHoldMix,
            x = s.shortestRotation,
            p = !x && s.timelinesRotation.length != o << 1;
          p && (s.timelinesRotation.length = o << 1), (s.totalAlpha = 0);
          for (let w = 0; w < o; w++) {
            let y = r[w],
              v = 1,
              A,
              C = 0;
            switch (g[w]) {
              case Tt:
                if (!a && y instanceof je) continue;
                (A = i), (C = h);
                break;
              case Si:
                (A = 0), (C = h);
                break;
              case Ti:
                (A = i), (C = l);
                break;
              case kt:
                (A = 0), (C = l);
                break;
              default:
                A = 0;
                let T = b[w];
                C = l * Math.max(0, 1 - T.mixTime / T.mixDuration);
                break;
            }
            (s.totalAlpha += C),
              !x && y instanceof ot
                ? this.applyRotateTimeline(
                    y,
                    t,
                    f,
                    C,
                    A,
                    s.timelinesRotation,
                    w << 1,
                    p
                  )
                : y instanceof He
                ? this.applyAttachmentTimeline(
                    y,
                    t,
                    f,
                    A,
                    d && C >= s.alphaAttachmentThreshold
                  )
                : (P.webkit602BugfixHelper(C, i),
                  a && y instanceof je && A == 0 && (v = 0),
                  y.apply(t, u, f, m, C, A, v));
          }
        }
        return (
          e.mixDuration > 0 && this.queueEvents(s, c),
          (this.events.length = 0),
          (s.nextAnimationLast = c),
          (s.nextTrackLast = s.trackTime),
          n
        );
      }
      applyAttachmentTimeline(e, t, i, s, n) {
        var d = t.slots[e.slotIndex];
        d.bone.active &&
          (i < e.frames[0]
            ? (s == 0 || s == 1) &&
              this.setAttachment(t, d, d.data.attachmentName, n)
            : this.setAttachment(
                t,
                d,
                e.attachmentNames[me.search1(e.frames, i)],
                n
              ),
          d.attachmentState <= this.unkeyedState &&
            (d.attachmentState = this.unkeyedState + ki));
      }
      setAttachment(e, t, i, s) {
        t.setAttachment(i ? e.getAttachment(t.data.index, i) : null),
          s && (t.attachmentState = this.unkeyedState + gs);
      }
      applyRotateTimeline(e, t, i, s, n, d, a, r) {
        if ((r && (d[a] = 0), s == 1)) {
          e.apply(t, 0, i, null, 1, n, 0);
          return;
        }
        let o = t.bones[e.boneIndex];
        if (!o.active) return;
        let l = e.frames,
          h = 0,
          u = 0;
        if (i < l[0])
          switch (n) {
            case 0:
              o.rotation = o.data.rotation;
            default:
              return;
            case 1:
              (h = o.rotation), (u = o.data.rotation);
          }
        else
          (h = n == 0 ? o.data.rotation : o.rotation),
            (u = o.data.rotation + e.getCurveValue(i));
        let c = 0,
          f = u - h;
        if (((f -= Math.ceil(f / 360 - 0.5) * 360), f == 0)) c = d[a];
        else {
          let m = 0,
            g = 0;
          r ? ((m = 0), (g = f)) : ((m = d[a]), (g = d[a + 1]));
          let b = m - (m % 360);
          c = f + b;
          let x = f >= 0,
            p = m >= 0;
          Math.abs(g) <= 90 &&
            X.signum(g) != X.signum(f) &&
            (Math.abs(m - b) > 180
              ? ((c += 360 * X.signum(m)), (p = x))
              : b != 0
              ? (c -= 360 * X.signum(m))
              : (p = x)),
            p != x && (c += 360 * X.signum(m)),
            (d[a] = c);
        }
        (d[a + 1] = f), (o.rotation = h + c * s);
      }
      queueEvents(e, t) {
        let i = e.animationStart,
          s = e.animationEnd,
          n = s - i,
          d = e.trackLast % n,
          a = this.events,
          r = 0,
          o = a.length;
        for (; r < o; r++) {
          let h = a[r];
          if (h.time < d) break;
          h.time > s || this.queue.event(e, h);
        }
        let l = !1;
        if (e.loop)
          if (n == 0) l = !0;
          else {
            const h = Math.floor(e.trackTime / n);
            l = h > 0 && h > Math.floor(e.trackLast / n);
          }
        else l = t >= s && e.animationLast < s;
        for (l && this.queue.complete(e); r < o; r++) {
          let h = a[r];
          h.time < i || this.queue.event(e, h);
        }
      }
      clearTracks() {
        let e = this.queue.drainDisabled;
        this.queue.drainDisabled = !0;
        for (let t = 0, i = this.tracks.length; t < i; t++) this.clearTrack(t);
        (this.tracks.length = 0),
          (this.queue.drainDisabled = e),
          this.queue.drain();
      }
      clearTrack(e) {
        if (e >= this.tracks.length) return;
        let t = this.tracks[e];
        if (!t) return;
        this.queue.end(t), this.clearNext(t);
        let i = t;
        for (;;) {
          let s = i.mixingFrom;
          if (!s) break;
          this.queue.end(s),
            (i.mixingFrom = null),
            (i.mixingTo = null),
            (i = s);
        }
        (this.tracks[t.trackIndex] = null), this.queue.drain();
      }
      setCurrent(e, t, i) {
        let s = this.expandToIndex(e);
        (this.tracks[e] = t),
          (t.previous = null),
          s &&
            (i && this.queue.interrupt(s),
            (t.mixingFrom = s),
            (s.mixingTo = t),
            (t.mixTime = 0),
            s.mixingFrom &&
              s.mixDuration > 0 &&
              (t.interruptAlpha *= Math.min(1, s.mixTime / s.mixDuration)),
            (s.timelinesRotation.length = 0)),
          this.queue.start(t);
      }
      setAnimation(e, t, i = !1) {
        let s = this.data.skeletonData.findAnimation(t);
        if (!s) throw new Error("Animation not found: " + t);
        return this.setAnimationWith(e, s, i);
      }
      setAnimationWith(e, t, i = !1) {
        if (!t) throw new Error("animation cannot be null.");
        let s = !0,
          n = this.expandToIndex(e);
        n &&
          (n.nextTrackLast == -1
            ? ((this.tracks[e] = n.mixingFrom),
              this.queue.interrupt(n),
              this.queue.end(n),
              this.clearNext(n),
              (n = n.mixingFrom),
              (s = !1))
            : this.clearNext(n));
        let d = this.trackEntry(e, t, i, n);
        return this.setCurrent(e, d, s), this.queue.drain(), d;
      }
      addAnimation(e, t, i = !1, s = 0) {
        let n = this.data.skeletonData.findAnimation(t);
        if (!n) throw new Error("Animation not found: " + t);
        return this.addAnimationWith(e, n, i, s);
      }
      addAnimationWith(e, t, i = !1, s = 0) {
        if (!t) throw new Error("animation cannot be null.");
        let n = this.expandToIndex(e);
        if (n) for (; n.next; ) n = n.next;
        let d = this.trackEntry(e, t, i, n);
        return (
          n
            ? ((n.next = d),
              (d.previous = n),
              s <= 0 && (s += n.getTrackComplete() - d.mixDuration))
            : (this.setCurrent(e, d, !0), this.queue.drain()),
          (d.delay = s),
          d
        );
      }
      setEmptyAnimation(e, t = 0) {
        let i = this.setAnimationWith(e, St.emptyAnimation(), !1);
        return (i.mixDuration = t), (i.trackEnd = t), i;
      }
      addEmptyAnimation(e, t = 0, i = 0) {
        let s = this.addAnimationWith(e, St.emptyAnimation(), !1, i);
        return (
          i <= 0 && (s.delay += s.mixDuration - t),
          (s.mixDuration = t),
          (s.trackEnd = t),
          s
        );
      }
      setEmptyAnimations(e = 0) {
        let t = this.queue.drainDisabled;
        this.queue.drainDisabled = !0;
        for (let i = 0, s = this.tracks.length; i < s; i++) {
          let n = this.tracks[i];
          n && this.setEmptyAnimation(n.trackIndex, e);
        }
        (this.queue.drainDisabled = t), this.queue.drain();
      }
      expandToIndex(e) {
        return e < this.tracks.length
          ? this.tracks[e]
          : (P.ensureArrayCapacity(this.tracks, e + 1, null),
            (this.tracks.length = e + 1),
            null);
      }
      trackEntry(e, t, i, s) {
        let n = this.trackEntryPool.obtain();
        return (
          n.reset(),
          (n.trackIndex = e),
          (n.animation = t),
          (n.loop = i),
          (n.holdPrevious = !1),
          (n.reverse = !1),
          (n.shortestRotation = !1),
          (n.eventThreshold = 0),
          (n.alphaAttachmentThreshold = 0),
          (n.mixAttachmentThreshold = 0),
          (n.mixDrawOrderThreshold = 0),
          (n.animationStart = 0),
          (n.animationEnd = t.duration),
          (n.animationLast = -1),
          (n.nextAnimationLast = -1),
          (n.delay = 0),
          (n.trackTime = 0),
          (n.trackLast = -1),
          (n.nextTrackLast = -1),
          (n.trackEnd = Number.MAX_VALUE),
          (n.timeScale = 1),
          (n.alpha = 1),
          (n.mixTime = 0),
          (n.mixDuration = s ? this.data.getMix(s.animation, t) : 0),
          (n.interruptAlpha = 1),
          (n.totalAlpha = 0),
          (n.mixBlend = 2),
          n
        );
      }
      clearNext(e) {
        let t = e.next;
        for (; t; ) this.queue.dispose(t), (t = t.next);
        e.next = null;
      }
      _animationsChanged() {
        (this.animationsChanged = !1), this.propertyIDs.clear();
        let e = this.tracks;
        for (let t = 0, i = e.length; t < i; t++) {
          let s = e[t];
          if (s) {
            for (; s.mixingFrom; ) s = s.mixingFrom;
            do
              (!s.mixingTo || s.mixBlend != 3) && this.computeHold(s),
                (s = s.mixingTo);
            while (s);
          }
        }
      }
      computeHold(e) {
        let t = e.mixingTo,
          i = e.animation.timelines,
          s = e.animation.timelines.length,
          n = e.timelineMode;
        n.length = s;
        let d = e.timelineHoldMix;
        d.length = 0;
        let a = this.propertyIDs;
        if (t && t.holdPrevious) {
          for (let r = 0; r < s; r++)
            n[r] = a.addAll(i[r].getPropertyIds()) ? kt : Ti;
          return;
        }
        e: for (let r = 0; r < s; r++) {
          let o = i[r],
            l = o.getPropertyIds();
          if (!a.addAll(l)) n[r] = Tt;
          else if (
            !t ||
            o instanceof He ||
            o instanceof je ||
            o instanceof ht ||
            !t.animation.hasTimeline(l)
          )
            n[r] = Si;
          else {
            for (let h = t.mixingTo; h; h = h.mixingTo)
              if (!h.animation.hasTimeline(l)) {
                if (e.mixDuration > 0) {
                  (n[r] = ms), (d[r] = h);
                  continue e;
                }
                break;
              }
            n[r] = kt;
          }
        }
      }
      getCurrent(e) {
        return e >= this.tracks.length ? null : this.tracks[e];
      }
      addListener(e) {
        if (!e) throw new Error("listener cannot be null.");
        this.listeners.push(e);
      }
      removeListener(e) {
        let t = this.listeners.indexOf(e);
        t >= 0 && this.listeners.splice(t, 1);
      }
      clearListeners() {
        this.listeners.length = 0;
      }
      clearListenerNotifications() {
        this.queue.clear();
      }
    },
    Ci = St;
  B(Ci, "_emptyAnimation", new yt("<empty>", [], 0));
  var us = class {
      animation = null;
      previous = null;
      next = null;
      mixingFrom = null;
      mixingTo = null;
      listener = null;
      trackIndex = 0;
      loop = !1;
      holdPrevious = !1;
      reverse = !1;
      shortestRotation = !1;
      eventThreshold = 0;
      mixAttachmentThreshold = 0;
      alphaAttachmentThreshold = 0;
      mixDrawOrderThreshold = 0;
      animationStart = 0;
      animationEnd = 0;
      animationLast = 0;
      nextAnimationLast = 0;
      delay = 0;
      trackTime = 0;
      trackLast = 0;
      nextTrackLast = 0;
      trackEnd = 0;
      timeScale = 0;
      alpha = 0;
      mixTime = 0;
      _mixDuration = 0;
      interruptAlpha = 0;
      totalAlpha = 0;
      get mixDuration() {
        return this._mixDuration;
      }
      set mixDuration(e) {
        this._mixDuration = e;
      }
      setMixDurationWithDelay(e, t) {
        (this._mixDuration = e),
          this.previous != null &&
            t <= 0 &&
            (t += this.previous.getTrackComplete() - e),
          (this.delay = t);
      }
      mixBlend = 2;
      timelineMode = new Array();
      timelineHoldMix = new Array();
      timelinesRotation = new Array();
      reset() {
        (this.next = null),
          (this.previous = null),
          (this.mixingFrom = null),
          (this.mixingTo = null),
          (this.animation = null),
          (this.listener = null),
          (this.timelineMode.length = 0),
          (this.timelineHoldMix.length = 0),
          (this.timelinesRotation.length = 0);
      }
      getAnimationTime() {
        if (this.loop) {
          let e = this.animationEnd - this.animationStart;
          return e == 0
            ? this.animationStart
            : (this.trackTime % e) + this.animationStart;
        }
        return Math.min(
          this.trackTime + this.animationStart,
          this.animationEnd
        );
      }
      setAnimationLast(e) {
        (this.animationLast = e), (this.nextAnimationLast = e);
      }
      isComplete() {
        return this.trackTime >= this.animationEnd - this.animationStart;
      }
      resetRotationDirections() {
        this.timelinesRotation.length = 0;
      }
      getTrackComplete() {
        let e = this.animationEnd - this.animationStart;
        if (e != 0) {
          if (this.loop) return e * (1 + ((this.trackTime / e) | 0));
          if (this.trackTime < e) return e;
        }
        return this.trackTime;
      }
      wasApplied() {
        return this.nextTrackLast != -1;
      }
      isNextReady() {
        return this.next != null && this.nextTrackLast - this.next.delay >= 0;
      }
    },
    fs = class {
      objects = [];
      drainDisabled = !1;
      animState;
      constructor(e) {
        this.animState = e;
      }
      start(e) {
        this.objects.push(Ae.start),
          this.objects.push(e),
          (this.animState.animationsChanged = !0);
      }
      interrupt(e) {
        this.objects.push(Ae.interrupt), this.objects.push(e);
      }
      end(e) {
        this.objects.push(Ae.end),
          this.objects.push(e),
          (this.animState.animationsChanged = !0);
      }
      dispose(e) {
        this.objects.push(Ae.dispose), this.objects.push(e);
      }
      complete(e) {
        this.objects.push(Ae.complete), this.objects.push(e);
      }
      event(e, t) {
        this.objects.push(Ae.event), this.objects.push(e), this.objects.push(t);
      }
      drain() {
        if (this.drainDisabled) return;
        this.drainDisabled = !0;
        let e = this.objects,
          t = this.animState.listeners;
        for (let i = 0; i < e.length; i += 2) {
          let s = e[i],
            n = e[i + 1];
          switch (s) {
            case Ae.start:
              n.listener && n.listener.start && n.listener.start(n);
              for (let a = 0; a < t.length; a++) {
                let r = t[a];
                r.start && r.start(n);
              }
              break;
            case Ae.interrupt:
              n.listener && n.listener.interrupt && n.listener.interrupt(n);
              for (let a = 0; a < t.length; a++) {
                let r = t[a];
                r.interrupt && r.interrupt(n);
              }
              break;
            case Ae.end:
              n.listener && n.listener.end && n.listener.end(n);
              for (let a = 0; a < t.length; a++) {
                let r = t[a];
                r.end && r.end(n);
              }
            case Ae.dispose:
              n.listener && n.listener.dispose && n.listener.dispose(n);
              for (let a = 0; a < t.length; a++) {
                let r = t[a];
                r.dispose && r.dispose(n);
              }
              this.animState.trackEntryPool.free(n);
              break;
            case Ae.complete:
              n.listener && n.listener.complete && n.listener.complete(n);
              for (let a = 0; a < t.length; a++) {
                let r = t[a];
                r.complete && r.complete(n);
              }
              break;
            case Ae.event:
              let d = e[i++ + 2];
              n.listener && n.listener.event && n.listener.event(n, d);
              for (let a = 0; a < t.length; a++) {
                let r = t[a];
                r.event && r.event(n, d);
              }
              break;
          }
        }
        this.clear(), (this.drainDisabled = !1);
      }
      clear() {
        this.objects.length = 0;
      }
    },
    Ae = ((e) => (
      (e[(e.start = 0)] = "start"),
      (e[(e.interrupt = 1)] = "interrupt"),
      (e[(e.end = 2)] = "end"),
      (e[(e.dispose = 3)] = "dispose"),
      (e[(e.complete = 4)] = "complete"),
      (e[(e.event = 5)] = "event"),
      e
    ))(Ae || {}),
    dr = class {
      start(e) {}
      interrupt(e) {}
      end(e) {}
      dispose(e) {}
      complete(e) {}
      event(e, t) {}
    },
    Tt = 0,
    Si = 1,
    Ti = 2,
    kt = 3,
    ms = 4,
    ki = 1,
    gs = 2,
    ps = class {
      skeletonData;
      animationToMixTime = {};
      defaultMix = 0;
      constructor(e) {
        if (!e) throw new Error("skeletonData cannot be null.");
        this.skeletonData = e;
      }
      setMix(e, t, i) {
        let s = this.skeletonData.findAnimation(e);
        if (!s) throw new Error("Animation not found: " + e);
        let n = this.skeletonData.findAnimation(t);
        if (!n) throw new Error("Animation not found: " + t);
        this.setMixWith(s, n, i);
      }
      setMixWith(e, t, i) {
        if (!e) throw new Error("from cannot be null.");
        if (!t) throw new Error("to cannot be null.");
        let s = e.name + "." + t.name;
        this.animationToMixTime[s] = i;
      }
      getMix(e, t) {
        let i = e.name + "." + t.name,
          s = this.animationToMixTime[i];
        return s === void 0 ? this.defaultMix : s;
      }
    },
    Et = class extends Re {
      color = new V(1, 1, 1, 1);
      constructor(e) {
        super(e);
      }
      copy() {
        let e = new Et(this.name);
        return this.copyTo(e), e.color.setFromColor(this.color), e;
      }
    },
    tt = class extends Re {
      endSlot = null;
      color = new V(0.2275, 0.2275, 0.8078, 1);
      constructor(e) {
        super(e);
      }
      copy() {
        let e = new tt(this.name);
        return (
          this.copyTo(e),
          (e.endSlot = this.endSlot),
          e.color.setFromColor(this.color),
          e
        );
      }
    },
    Ei = class {
      _image;
      constructor(e) {
        this._image = e;
      }
      getImage() {
        return this._image;
      }
    },
    It = ((e) => (
      (e[(e.Nearest = 9728)] = "Nearest"),
      (e[(e.Linear = 9729)] = "Linear"),
      (e[(e.MipMap = 9987)] = "MipMap"),
      (e[(e.MipMapNearestNearest = 9984)] = "MipMapNearestNearest"),
      (e[(e.MipMapLinearNearest = 9985)] = "MipMapLinearNearest"),
      (e[(e.MipMapNearestLinear = 9986)] = "MipMapNearestLinear"),
      (e[(e.MipMapLinearLinear = 9987)] = "MipMapLinearLinear"),
      e
    ))(It || {}),
    ws = ((e) => (
      (e[(e.MirroredRepeat = 33648)] = "MirroredRepeat"),
      (e[(e.ClampToEdge = 33071)] = "ClampToEdge"),
      (e[(e.Repeat = 10497)] = "Repeat"),
      e
    ))(ws || {}),
    xs = class {
      texture;
      u = 0;
      v = 0;
      u2 = 0;
      v2 = 0;
      width = 0;
      height = 0;
      degrees = 0;
      offsetX = 0;
      offsetY = 0;
      originalWidth = 0;
      originalHeight = 0;
    },
    cr = class extends Ei {
      setFilters(e, t) {}
      setWraps(e, t) {}
      dispose() {}
    },
    bs = class {
      pages = new Array();
      regions = new Array();
      constructor(e) {
        let t = new ur(e),
          i = new Array(4),
          s = {};
        (s.size = (l) => {
          (l.width = parseInt(i[1])), (l.height = parseInt(i[2]));
        }),
          (s.format = () => {}),
          (s.filter = (l) => {
            (l.minFilter = P.enumValue(It, i[1])),
              (l.magFilter = P.enumValue(It, i[2]));
          }),
          (s.repeat = (l) => {
            i[1].indexOf("x") != -1 && (l.uWrap = 10497),
              i[1].indexOf("y") != -1 && (l.vWrap = 10497);
          }),
          (s.pma = (l) => {
            l.pma = i[1] == "true";
          });
        var n = {};
        (n.xy = (l) => {
          (l.x = parseInt(i[1])), (l.y = parseInt(i[2]));
        }),
          (n.size = (l) => {
            (l.width = parseInt(i[1])), (l.height = parseInt(i[2]));
          }),
          (n.bounds = (l) => {
            (l.x = parseInt(i[1])),
              (l.y = parseInt(i[2])),
              (l.width = parseInt(i[3])),
              (l.height = parseInt(i[4]));
          }),
          (n.offset = (l) => {
            (l.offsetX = parseInt(i[1])), (l.offsetY = parseInt(i[2]));
          }),
          (n.orig = (l) => {
            (l.originalWidth = parseInt(i[1])),
              (l.originalHeight = parseInt(i[2]));
          }),
          (n.offsets = (l) => {
            (l.offsetX = parseInt(i[1])),
              (l.offsetY = parseInt(i[2])),
              (l.originalWidth = parseInt(i[3])),
              (l.originalHeight = parseInt(i[4]));
          }),
          (n.rotate = (l) => {
            let h = i[1];
            h == "true"
              ? (l.degrees = 90)
              : h != "false" && (l.degrees = parseInt(h));
          }),
          (n.index = (l) => {
            l.index = parseInt(i[1]);
          });
        let d = t.readLine();
        for (; d && d.trim().length == 0; ) d = t.readLine();
        for (; !(!d || d.trim().length == 0 || t.readEntry(i, d) == 0); )
          d = t.readLine();
        let a = null,
          r = null,
          o = null;
        for (; d !== null; )
          if (d.trim().length == 0) (a = null), (d = t.readLine());
          else if (a) {
            let l = new Ii(a, d);
            for (;;) {
              let h = t.readEntry(i, (d = t.readLine()));
              if (h == 0) break;
              let u = n[i[0]];
              if (u) u(l);
              else {
                r || (r = []), o || (o = []), r.push(i[0]);
                let c = [];
                for (let f = 0; f < h; f++) c.push(parseInt(i[f + 1]));
                o.push(c);
              }
            }
            l.originalWidth == 0 &&
              l.originalHeight == 0 &&
              ((l.originalWidth = l.width), (l.originalHeight = l.height)),
              r &&
                r.length > 0 &&
                o &&
                o.length > 0 &&
                ((l.names = r), (l.values = o), (r = null), (o = null)),
              (l.u = l.x / a.width),
              (l.v = l.y / a.height),
              l.degrees == 90
                ? ((l.u2 = (l.x + l.height) / a.width),
                  (l.v2 = (l.y + l.width) / a.height))
                : ((l.u2 = (l.x + l.width) / a.width),
                  (l.v2 = (l.y + l.height) / a.height)),
              this.regions.push(l);
          } else {
            for (
              a = new vs(d.trim());
              t.readEntry(i, (d = t.readLine())) != 0;

            ) {
              let l = s[i[0]];
              l && l(a);
            }
            this.pages.push(a);
          }
      }
      findRegion(e) {
        for (let t = 0; t < this.regions.length; t++)
          if (this.regions[t].name == e) return this.regions[t];
        return null;
      }
      setTextures(e, t = "") {
        for (let i of this.pages) i.setTexture(e.get(t + i.name));
      }
      dispose() {
        for (let e = 0; e < this.pages.length; e++)
          this.pages[e].texture?.dispose();
      }
    },
    ur = class {
      lines;
      index = 0;
      constructor(e) {
        this.lines = e.split(/\r\n|\r|\n/);
      }
      readLine() {
        return this.index >= this.lines.length
          ? null
          : this.lines[this.index++];
      }
      readEntry(e, t) {
        if (!t || ((t = t.trim()), t.length == 0)) return 0;
        let i = t.indexOf(":");
        if (i == -1) return 0;
        e[0] = t.substr(0, i).trim();
        for (let s = 1, n = i + 1; ; s++) {
          let d = t.indexOf(",", n);
          if (d == -1) return (e[s] = t.substr(n).trim()), s;
          if (((e[s] = t.substr(n, d - n).trim()), (n = d + 1), s == 4))
            return 4;
        }
      }
    },
    vs = class {
      name;
      minFilter = 9728;
      magFilter = 9728;
      uWrap = 33071;
      vWrap = 33071;
      texture = null;
      width = 0;
      height = 0;
      pma = !1;
      regions = new Array();
      constructor(e) {
        this.name = e;
      }
      setTexture(e) {
        (this.texture = e),
          e.setFilters(this.minFilter, this.magFilter),
          e.setWraps(this.uWrap, this.vWrap);
        for (let t of this.regions) t.texture = e;
      }
    },
    Ii = class extends xs {
      page;
      name;
      x = 0;
      y = 0;
      offsetX = 0;
      offsetY = 0;
      originalWidth = 0;
      originalHeight = 0;
      index = 0;
      degrees = 0;
      names = null;
      values = null;
      constructor(e, t) {
        super(), (this.page = e), (this.name = t), e.regions.push(this);
      }
    },
    Ue = class extends Re {
      region = null;
      path;
      regionUVs = [];
      uvs = [];
      triangles = [];
      color = new V(1, 1, 1, 1);
      width = 0;
      height = 0;
      hullLength = 0;
      edges = [];
      parentMesh = null;
      sequence = null;
      tempColor = new V(0, 0, 0, 0);
      constructor(e, t) {
        super(e), (this.path = t);
      }
      updateRegion() {
        if (!this.region) throw new Error("Region not set.");
        let e = this.regionUVs;
        (!this.uvs || this.uvs.length != e.length) &&
          (this.uvs = P.newFloatArray(e.length));
        let t = this.uvs,
          i = this.uvs.length,
          s = this.region.u,
          n = this.region.v,
          d = 0,
          a = 0;
        if (this.region instanceof Ii) {
          let r = this.region,
            o = r.page,
            l = o.width,
            h = o.height;
          switch (r.degrees) {
            case 90:
              (s -= (r.originalHeight - r.offsetY - r.height) / l),
                (n -= (r.originalWidth - r.offsetX - r.width) / h),
                (d = r.originalHeight / l),
                (a = r.originalWidth / h);
              for (let u = 0; u < i; u += 2)
                (t[u] = s + e[u + 1] * d), (t[u + 1] = n + (1 - e[u]) * a);
              return;
            case 180:
              (s -= (r.originalWidth - r.offsetX - r.width) / l),
                (n -= r.offsetY / h),
                (d = r.originalWidth / l),
                (a = r.originalHeight / h);
              for (let u = 0; u < i; u += 2)
                (t[u] = s + (1 - e[u]) * d),
                  (t[u + 1] = n + (1 - e[u + 1]) * a);
              return;
            case 270:
              (s -= r.offsetY / l),
                (n -= r.offsetX / h),
                (d = r.originalHeight / l),
                (a = r.originalWidth / h);
              for (let u = 0; u < i; u += 2)
                (t[u] = s + (1 - e[u + 1]) * d), (t[u + 1] = n + e[u] * a);
              return;
          }
          (s -= r.offsetX / l),
            (n -= (r.originalHeight - r.offsetY - r.height) / h),
            (d = r.originalWidth / l),
            (a = r.originalHeight / h);
        } else
          this.region
            ? ((d = this.region.u2 - s), (a = this.region.v2 - n))
            : ((s = n = 0), (d = a = 1));
        for (let r = 0; r < i; r += 2)
          (t[r] = s + e[r] * d), (t[r + 1] = n + e[r + 1] * a);
      }
      getParentMesh() {
        return this.parentMesh;
      }
      setParentMesh(e) {
        (this.parentMesh = e),
          e &&
            ((this.bones = e.bones),
            (this.vertices = e.vertices),
            (this.worldVerticesLength = e.worldVerticesLength),
            (this.regionUVs = e.regionUVs),
            (this.triangles = e.triangles),
            (this.hullLength = e.hullLength),
            (this.worldVerticesLength = e.worldVerticesLength));
      }
      copy() {
        if (this.parentMesh) return this.newLinkedMesh();
        let e = new Ue(this.name, this.path);
        return (
          (e.region = this.region),
          e.color.setFromColor(this.color),
          this.copyTo(e),
          (e.regionUVs = new Array(this.regionUVs.length)),
          P.arrayCopy(this.regionUVs, 0, e.regionUVs, 0, this.regionUVs.length),
          (e.uvs = new Array(this.uvs.length)),
          P.arrayCopy(this.uvs, 0, e.uvs, 0, this.uvs.length),
          (e.triangles = new Array(this.triangles.length)),
          P.arrayCopy(this.triangles, 0, e.triangles, 0, this.triangles.length),
          (e.hullLength = this.hullLength),
          (e.sequence = this.sequence != null ? this.sequence.copy() : null),
          this.edges &&
            ((e.edges = new Array(this.edges.length)),
            P.arrayCopy(this.edges, 0, e.edges, 0, this.edges.length)),
          (e.width = this.width),
          (e.height = this.height),
          e
        );
      }
      computeWorldVertices(e, t, i, s, n, d) {
        this.sequence != null && this.sequence.apply(e, this),
          super.computeWorldVertices(e, t, i, s, n, d);
      }
      newLinkedMesh() {
        let e = new Ue(this.name, this.path);
        return (
          (e.region = this.region),
          e.color.setFromColor(this.color),
          (e.timelineAttachment = this.timelineAttachment),
          e.setParentMesh(this.parentMesh ? this.parentMesh : this),
          e.region != null && e.updateRegion(),
          e
        );
      }
    },
    Ze = class extends Re {
      lengths = [];
      closed = !1;
      constantSpeed = !1;
      color = new V(1, 1, 1, 1);
      constructor(e) {
        super(e);
      }
      copy() {
        let e = new Ze(this.name);
        return (
          this.copyTo(e),
          (e.lengths = new Array(this.lengths.length)),
          P.arrayCopy(this.lengths, 0, e.lengths, 0, this.lengths.length),
          (e.closed = closed),
          (e.constantSpeed = this.constantSpeed),
          e.color.setFromColor(this.color),
          e
        );
      }
    },
    Mi = class extends Re {
      x = 0;
      y = 0;
      rotation = 0;
      color = new V(0.38, 0.94, 0, 1);
      constructor(e) {
        super(e);
      }
      computeWorldPosition(e, t) {
        return (
          (t.x = this.x * e.a + this.y * e.b + e.worldX),
          (t.y = this.x * e.c + this.y * e.d + e.worldY),
          t
        );
      }
      computeWorldRotation(e) {
        const t = this.rotation * X.degRad,
          i = Math.cos(t),
          s = Math.sin(t),
          n = i * e.a + s * e.b,
          d = i * e.c + s * e.d;
        return X.atan2Deg(d, n);
      }
      copy() {
        let e = new Mi(this.name);
        return (
          (e.x = this.x),
          (e.y = this.y),
          (e.rotation = this.rotation),
          e.color.setFromColor(this.color),
          e
        );
      }
    },
    ys = class extends Ht {
      x = 0;
      y = 0;
      scaleX = 1;
      scaleY = 1;
      rotation = 0;
      width = 0;
      height = 0;
      color = new V(1, 1, 1, 1);
      path;
      region = null;
      sequence = null;
      offset = P.newFloatArray(8);
      uvs = P.newFloatArray(8);
      tempColor = new V(1, 1, 1, 1);
      constructor(e, t) {
        super(e), (this.path = t);
      }
      updateRegion() {
        if (!this.region) throw new Error("Region not set.");
        let e = this.region,
          t = this.uvs;
        if (e == null) {
          (t[0] = 0),
            (t[1] = 0),
            (t[2] = 0),
            (t[3] = 1),
            (t[4] = 1),
            (t[5] = 1),
            (t[6] = 1),
            (t[7] = 0);
          return;
        }
        let i = (this.width / this.region.originalWidth) * this.scaleX,
          s = (this.height / this.region.originalHeight) * this.scaleY,
          n = (-this.width / 2) * this.scaleX + this.region.offsetX * i,
          d = (-this.height / 2) * this.scaleY + this.region.offsetY * s,
          a = n + this.region.width * i,
          r = d + this.region.height * s,
          o = this.rotation * X.degRad,
          l = Math.cos(o),
          h = Math.sin(o),
          u = this.x,
          c = this.y,
          f = n * l + u,
          m = n * h,
          g = d * l + c,
          b = d * h,
          x = a * l + u,
          p = a * h,
          w = r * l + c,
          y = r * h,
          v = this.offset;
        (v[0] = f - b),
          (v[1] = g + m),
          (v[2] = f - y),
          (v[3] = w + m),
          (v[4] = x - y),
          (v[5] = w + p),
          (v[6] = x - b),
          (v[7] = g + p),
          e.degrees == 90
            ? ((t[0] = e.u2),
              (t[1] = e.v2),
              (t[2] = e.u),
              (t[3] = e.v2),
              (t[4] = e.u),
              (t[5] = e.v),
              (t[6] = e.u2),
              (t[7] = e.v))
            : ((t[0] = e.u),
              (t[1] = e.v2),
              (t[2] = e.u),
              (t[3] = e.v),
              (t[4] = e.u2),
              (t[5] = e.v),
              (t[6] = e.u2),
              (t[7] = e.v2));
      }
      computeWorldVertices(e, t, i, s) {
        this.sequence != null && this.sequence.apply(e, this);
        let n = e.bone,
          d = this.offset,
          a = n.worldX,
          r = n.worldY,
          o = n.a,
          l = n.b,
          h = n.c,
          u = n.d,
          c = 0,
          f = 0;
        (c = d[0]),
          (f = d[1]),
          (t[i] = c * o + f * l + a),
          (t[i + 1] = c * h + f * u + r),
          (i += s),
          (c = d[2]),
          (f = d[3]),
          (t[i] = c * o + f * l + a),
          (t[i + 1] = c * h + f * u + r),
          (i += s),
          (c = d[4]),
          (f = d[5]),
          (t[i] = c * o + f * l + a),
          (t[i + 1] = c * h + f * u + r),
          (i += s),
          (c = d[6]),
          (f = d[7]),
          (t[i] = c * o + f * l + a),
          (t[i + 1] = c * h + f * u + r);
      }
      copy() {
        let e = new ys(this.name, this.path);
        return (
          (e.region = this.region),
          (e.x = this.x),
          (e.y = this.y),
          (e.scaleX = this.scaleX),
          (e.scaleY = this.scaleY),
          (e.rotation = this.rotation),
          (e.width = this.width),
          (e.height = this.height),
          P.arrayCopy(this.uvs, 0, e.uvs, 0, 8),
          P.arrayCopy(this.offset, 0, e.offset, 0, 8),
          e.color.setFromColor(this.color),
          (e.sequence = this.sequence != null ? this.sequence.copy() : null),
          e
        );
      }
    },
    le = ys;
  B(le, "X1", 0),
    B(le, "Y1", 1),
    B(le, "C1R", 2),
    B(le, "C1G", 3),
    B(le, "C1B", 4),
    B(le, "C1A", 5),
    B(le, "U1", 6),
    B(le, "V1", 7),
    B(le, "X2", 8),
    B(le, "Y2", 9),
    B(le, "C2R", 10),
    B(le, "C2G", 11),
    B(le, "C2B", 12),
    B(le, "C2A", 13),
    B(le, "U2", 14),
    B(le, "V2", 15),
    B(le, "X3", 16),
    B(le, "Y3", 17),
    B(le, "C3R", 18),
    B(le, "C3G", 19),
    B(le, "C3B", 20),
    B(le, "C3A", 21),
    B(le, "U3", 22),
    B(le, "V3", 23),
    B(le, "X4", 24),
    B(le, "Y4", 25),
    B(le, "C4R", 26),
    B(le, "C4G", 27),
    B(le, "C4B", 28),
    B(le, "C4A", 29),
    B(le, "U4", 30),
    B(le, "V4", 31);
  var As = class {
      atlas;
      constructor(e) {
        this.atlas = e;
      }
      loadSequence(e, t, i) {
        let s = i.regions;
        for (let n = 0, d = s.length; n < d; n++) {
          let a = i.getPath(t, n),
            r = this.atlas.findRegion(a);
          if (r == null)
            throw new Error(
              "Region not found in atlas: " + a + " (sequence: " + e + ")"
            );
          s[n] = r;
        }
      }
      newRegionAttachment(e, t, i, s) {
        let n = new le(t, i);
        if (s != null) this.loadSequence(t, i, s);
        else {
          let d = this.atlas.findRegion(i);
          if (!d)
            throw new Error(
              "Region not found in atlas: " +
                i +
                " (region attachment: " +
                t +
                ")"
            );
          n.region = d;
        }
        return n;
      }
      newMeshAttachment(e, t, i, s) {
        let n = new Ue(t, i);
        if (s != null) this.loadSequence(t, i, s);
        else {
          let d = this.atlas.findRegion(i);
          if (!d)
            throw new Error(
              "Region not found in atlas: " +
                i +
                " (mesh attachment: " +
                t +
                ")"
            );
          n.region = d;
        }
        return n;
      }
      newBoundingBoxAttachment(e, t) {
        return new Et(t);
      }
      newPathAttachment(e, t) {
        return new Ze(t);
      }
      newPointAttachment(e, t) {
        return new Mi(t);
      }
      newClippingAttachment(e, t) {
        return new tt(t);
      }
    },
    Ri = class {
      index = 0;
      name;
      parent = null;
      length = 0;
      x = 0;
      y = 0;
      rotation = 0;
      scaleX = 1;
      scaleY = 1;
      shearX = 0;
      shearY = 0;
      inherit = dt.Normal;
      skinRequired = !1;
      color = new V();
      icon;
      visible = !1;
      constructor(e, t, i) {
        if (e < 0) throw new Error("index must be >= 0.");
        if (!t) throw new Error("name cannot be null.");
        (this.index = e), (this.name = t), (this.parent = i);
      }
    },
    dt = ((e) => (
      (e[(e.Normal = 0)] = "Normal"),
      (e[(e.OnlyTranslation = 1)] = "OnlyTranslation"),
      (e[(e.NoRotationOrReflection = 2)] = "NoRotationOrReflection"),
      (e[(e.NoScale = 3)] = "NoScale"),
      (e[(e.NoScaleOrReflection = 4)] = "NoScaleOrReflection"),
      e
    ))(dt || {}),
    Fi = class {
      data;
      skeleton;
      parent = null;
      children = new Array();
      x = 0;
      y = 0;
      rotation = 0;
      scaleX = 0;
      scaleY = 0;
      shearX = 0;
      shearY = 0;
      ax = 0;
      ay = 0;
      arotation = 0;
      ascaleX = 0;
      ascaleY = 0;
      ashearX = 0;
      ashearY = 0;
      a = 0;
      b = 0;
      c = 0;
      d = 0;
      worldY = 0;
      worldX = 0;
      inherit = 0;
      sorted = !1;
      active = !1;
      constructor(e, t, i) {
        if (!e) throw new Error("data cannot be null.");
        if (!t) throw new Error("skeleton cannot be null.");
        (this.data = e),
          (this.skeleton = t),
          (this.parent = i),
          this.setToSetupPose();
      }
      isActive() {
        return this.active;
      }
      update(e) {
        this.updateWorldTransformWith(
          this.ax,
          this.ay,
          this.arotation,
          this.ascaleX,
          this.ascaleY,
          this.ashearX,
          this.ashearY
        );
      }
      updateWorldTransform() {
        this.updateWorldTransformWith(
          this.x,
          this.y,
          this.rotation,
          this.scaleX,
          this.scaleY,
          this.shearX,
          this.shearY
        );
      }
      updateWorldTransformWith(e, t, i, s, n, d, a) {
        (this.ax = e),
          (this.ay = t),
          (this.arotation = i),
          (this.ascaleX = s),
          (this.ascaleY = n),
          (this.ashearX = d),
          (this.ashearY = a);
        let r = this.parent;
        if (!r) {
          let c = this.skeleton;
          const f = c.scaleX,
            m = c.scaleY,
            g = (i + d) * X.degRad,
            b = (i + 90 + a) * X.degRad;
          (this.a = Math.cos(g) * s * f),
            (this.b = Math.cos(b) * n * f),
            (this.c = Math.sin(g) * s * m),
            (this.d = Math.sin(b) * n * m),
            (this.worldX = e * f + c.x),
            (this.worldY = t * m + c.y);
          return;
        }
        let o = r.a,
          l = r.b,
          h = r.c,
          u = r.d;
        switch (
          ((this.worldX = o * e + l * t + r.worldX),
          (this.worldY = h * e + u * t + r.worldY),
          this.inherit)
        ) {
          case 0: {
            const c = (i + d) * X.degRad,
              f = (i + 90 + a) * X.degRad,
              m = Math.cos(c) * s,
              g = Math.cos(f) * n,
              b = Math.sin(c) * s,
              x = Math.sin(f) * n;
            (this.a = o * m + l * b),
              (this.b = o * g + l * x),
              (this.c = h * m + u * b),
              (this.d = h * g + u * x);
            return;
          }
          case 1: {
            const c = (i + d) * X.degRad,
              f = (i + 90 + a) * X.degRad;
            (this.a = Math.cos(c) * s),
              (this.b = Math.cos(f) * n),
              (this.c = Math.sin(c) * s),
              (this.d = Math.sin(f) * n);
            break;
          }
          case 2: {
            let c = 1 / this.skeleton.scaleX,
              f = 1 / this.skeleton.scaleY;
            (o *= c), (h *= f);
            let m = o * o + h * h,
              g = 0;
            m > 1e-4
              ? ((m = Math.abs(o * u * f - l * c * h) / m),
                (l = h * m),
                (u = o * m),
                (g = Math.atan2(h, o) * X.radDeg))
              : ((o = 0), (h = 0), (g = 90 - Math.atan2(u, l) * X.radDeg));
            const b = (i + d - g) * X.degRad,
              x = (i + a - g + 90) * X.degRad,
              p = Math.cos(b) * s,
              w = Math.cos(x) * n,
              y = Math.sin(b) * s,
              v = Math.sin(x) * n;
            (this.a = o * p - l * y),
              (this.b = o * w - l * v),
              (this.c = h * p + u * y),
              (this.d = h * w + u * v);
            break;
          }
          case 3:
          case 4: {
            i *= X.degRad;
            const c = Math.cos(i),
              f = Math.sin(i);
            let m = (o * c + l * f) / this.skeleton.scaleX,
              g = (h * c + u * f) / this.skeleton.scaleY,
              b = Math.sqrt(m * m + g * g);
            b > 1e-5 && (b = 1 / b),
              (m *= b),
              (g *= b),
              (b = Math.sqrt(m * m + g * g)),
              this.inherit == 3 &&
                o * u - l * h < 0 !=
                  (this.skeleton.scaleX < 0 != this.skeleton.scaleY < 0) &&
                (b = -b),
              (i = Math.PI / 2 + Math.atan2(g, m));
            const x = Math.cos(i) * b,
              p = Math.sin(i) * b;
            (d *= X.degRad), (a = (90 + a) * X.degRad);
            const w = Math.cos(d) * s,
              y = Math.cos(a) * n,
              v = Math.sin(d) * s,
              A = Math.sin(a) * n;
            (this.a = m * w + x * v),
              (this.b = m * y + x * A),
              (this.c = g * w + p * v),
              (this.d = g * y + p * A);
            break;
          }
        }
        (this.a *= this.skeleton.scaleX),
          (this.b *= this.skeleton.scaleX),
          (this.c *= this.skeleton.scaleY),
          (this.d *= this.skeleton.scaleY);
      }
      setToSetupPose() {
        let e = this.data;
        (this.x = e.x),
          (this.y = e.y),
          (this.rotation = e.rotation),
          (this.scaleX = e.scaleX),
          (this.scaleY = e.scaleY),
          (this.shearX = e.shearX),
          (this.shearY = e.shearY),
          (this.inherit = e.inherit);
      }
      updateAppliedTransform() {
        let e = this.parent;
        if (!e) {
          (this.ax = this.worldX - this.skeleton.x),
            (this.ay = this.worldY - this.skeleton.y),
            (this.arotation = Math.atan2(this.c, this.a) * X.radDeg),
            (this.ascaleX = Math.sqrt(this.a * this.a + this.c * this.c)),
            (this.ascaleY = Math.sqrt(this.b * this.b + this.d * this.d)),
            (this.ashearX = 0),
            (this.ashearY =
              Math.atan2(
                this.a * this.b + this.c * this.d,
                this.a * this.d - this.b * this.c
              ) * X.radDeg);
          return;
        }
        let t = e.a,
          i = e.b,
          s = e.c,
          n = e.d,
          d = 1 / (t * n - i * s),
          a = n * d,
          r = i * d,
          o = s * d,
          l = t * d,
          h = this.worldX - e.worldX,
          u = this.worldY - e.worldY;
        (this.ax = h * a - u * r), (this.ay = u * l - h * o);
        let c, f, m, g;
        if (this.inherit == 1)
          (c = this.a), (f = this.b), (m = this.c), (g = this.d);
        else {
          switch (this.inherit) {
            case 2: {
              let y = Math.abs(t * n - i * s) / (t * t + s * s);
              (i = (-s * this.skeleton.scaleX * y) / this.skeleton.scaleY),
                (n = (t * this.skeleton.scaleY * y) / this.skeleton.scaleX),
                (d = 1 / (t * n - i * s)),
                (a = n * d),
                (r = i * d);
              break;
            }
            case 3:
            case 4:
              let b = X.cosDeg(this.rotation),
                x = X.sinDeg(this.rotation);
              (t = (t * b + i * x) / this.skeleton.scaleX),
                (s = (s * b + n * x) / this.skeleton.scaleY);
              let p = Math.sqrt(t * t + s * s);
              p > 1e-5 && (p = 1 / p),
                (t *= p),
                (s *= p),
                (p = Math.sqrt(t * t + s * s)),
                this.inherit == 3 &&
                  d < 0 !=
                    (this.skeleton.scaleX < 0 != this.skeleton.scaleY < 0) &&
                  (p = -p);
              let w = X.PI / 2 + Math.atan2(s, t);
              (i = Math.cos(w) * p),
                (n = Math.sin(w) * p),
                (d = 1 / (t * n - i * s)),
                (a = n * d),
                (r = i * d),
                (o = s * d),
                (l = t * d);
          }
          (c = a * this.a - r * this.c),
            (f = a * this.b - r * this.d),
            (m = l * this.c - o * this.a),
            (g = l * this.d - o * this.b);
        }
        if (
          ((this.ashearX = 0),
          (this.ascaleX = Math.sqrt(c * c + m * m)),
          this.ascaleX > 1e-4)
        ) {
          let b = c * g - f * m;
          (this.ascaleY = b / this.ascaleX),
            (this.ashearY = -Math.atan2(c * f + m * g, b) * X.radDeg),
            (this.arotation = Math.atan2(m, c) * X.radDeg);
        } else
          (this.ascaleX = 0),
            (this.ascaleY = Math.sqrt(f * f + g * g)),
            (this.ashearY = 0),
            (this.arotation = 90 - Math.atan2(g, f) * X.radDeg);
      }
      getWorldRotationX() {
        return Math.atan2(this.c, this.a) * X.radDeg;
      }
      getWorldRotationY() {
        return Math.atan2(this.d, this.b) * X.radDeg;
      }
      getWorldScaleX() {
        return Math.sqrt(this.a * this.a + this.c * this.c);
      }
      getWorldScaleY() {
        return Math.sqrt(this.b * this.b + this.d * this.d);
      }
      worldToLocal(e) {
        let t = 1 / (this.a * this.d - this.b * this.c),
          i = e.x - this.worldX,
          s = e.y - this.worldY;
        return (
          (e.x = i * this.d * t - s * this.b * t),
          (e.y = s * this.a * t - i * this.c * t),
          e
        );
      }
      localToWorld(e) {
        let t = e.x,
          i = e.y;
        return (
          (e.x = t * this.a + i * this.b + this.worldX),
          (e.y = t * this.c + i * this.d + this.worldY),
          e
        );
      }
      worldToParent(e) {
        if (e == null) throw new Error("world cannot be null.");
        return this.parent == null ? e : this.parent.worldToLocal(e);
      }
      parentToWorld(e) {
        if (e == null) throw new Error("world cannot be null.");
        return this.parent == null ? e : this.parent.localToWorld(e);
      }
      worldToLocalRotation(e) {
        let t = X.sinDeg(e),
          i = X.cosDeg(e);
        return (
          Math.atan2(this.a * t - this.c * i, this.d * i - this.b * t) *
            X.radDeg +
          this.rotation -
          this.shearX
        );
      }
      localToWorldRotation(e) {
        e -= this.rotation - this.shearX;
        let t = X.sinDeg(e),
          i = X.cosDeg(e);
        return (
          Math.atan2(i * this.c + t * this.d, i * this.a + t * this.b) *
          X.radDeg
        );
      }
      rotateWorld(e) {
        e *= X.degRad;
        const t = Math.sin(e),
          i = Math.cos(e),
          s = this.a,
          n = this.b;
        (this.a = i * s - t * this.c),
          (this.b = i * n - t * this.d),
          (this.c = t * s + i * this.c),
          (this.d = t * n + i * this.d);
      }
    },
    ct = class {
      constructor(e, t, i) {
        (this.name = e), (this.order = t), (this.skinRequired = i);
      }
    },
    Cs = class {
      pathPrefix = "";
      textureLoader;
      downloader;
      assets = {};
      errors = {};
      toLoad = 0;
      loaded = 0;
      constructor(e, t = "", i = new Yi()) {
        (this.textureLoader = e), (this.pathPrefix = t), (this.downloader = i);
      }
      start(e) {
        return this.toLoad++, this.pathPrefix + e;
      }
      success(e, t, i) {
        this.toLoad--, this.loaded++, (this.assets[t] = i), e && e(t, i);
      }
      error(e, t, i) {
        this.toLoad--, this.loaded++, (this.errors[t] = i), e && e(t, i);
      }
      loadAll() {
        return new Promise((t, i) => {
          let s = () => {
            if (this.isLoadingComplete()) {
              this.hasErrors() ? i(this.errors) : t(this);
              return;
            }
            requestAnimationFrame(s);
          };
          requestAnimationFrame(s);
        });
      }
      setRawDataURI(e, t) {
        this.downloader.rawDataUris[this.pathPrefix + e] = t;
      }
      loadBinary(e, t = () => {}, i = () => {}) {
        (e = this.start(e)),
          this.downloader.downloadBinary(
            e,
            (s) => {
              this.success(t, e, s);
            },
            (s, n) => {
              this.error(i, e, `Couldn't load binary ${e}: status ${s}, ${n}`);
            }
          );
      }
      loadText(e, t = () => {}, i = () => {}) {
        (e = this.start(e)),
          this.downloader.downloadText(
            e,
            (s) => {
              this.success(t, e, s);
            },
            (s, n) => {
              this.error(i, e, `Couldn't load text ${e}: status ${s}, ${n}`);
            }
          );
      }
      loadJson(e, t = () => {}, i = () => {}) {
        (e = this.start(e)),
          this.downloader.downloadJson(
            e,
            (s) => {
              this.success(t, e, s);
            },
            (s, n) => {
              this.error(i, e, `Couldn't load JSON ${e}: status ${s}, ${n}`);
            }
          );
      }
      loadTexture(e, t = () => {}, i = () => {}) {
        if (
          ((e = this.start(e)),
          !!!(typeof window < "u" && typeof navigator < "u" && window.document))
        )
          fetch(e, { mode: "cors" })
            .then((d) =>
              d.ok
                ? d.blob()
                : (this.error(i, e, `Couldn't load image: ${e}`), null)
            )
            .then((d) =>
              d
                ? createImageBitmap(d, {
                    premultiplyAlpha: "none",
                    colorSpaceConversion: "none",
                  })
                : null
            )
            .then((d) => {
              d && this.success(t, e, this.textureLoader(d));
            });
        else {
          let d = new Image();
          (d.crossOrigin = "anonymous"),
            (d.onload = () => {
              this.success(t, e, this.textureLoader(d));
            }),
            (d.onerror = () => {
              this.error(i, e, `Couldn't load image: ${e}`);
            }),
            this.downloader.rawDataUris[e] &&
              (e = this.downloader.rawDataUris[e]),
            (d.src = e);
        }
      }
      loadTextureAtlas(e, t = () => {}, i = () => {}, s) {
        let n = e.lastIndexOf("/"),
          d = n >= 0 ? e.substring(0, n + 1) : "";
        (e = this.start(e)),
          this.downloader.downloadText(
            e,
            (a) => {
              try {
                let r = new bs(a),
                  o = r.pages.length,
                  l = !1;
                for (let h of r.pages)
                  this.loadTexture(
                    s ? s[h.name] : d + h.name,
                    (u, c) => {
                      l || (h.setTexture(c), --o == 0 && this.success(t, e, r));
                    },
                    (u, c) => {
                      l ||
                        this.error(
                          i,
                          e,
                          `Couldn't load texture atlas ${e} page image: ${u}`
                        ),
                        (l = !0);
                    }
                  );
              } catch (r) {
                this.error(
                  i,
                  e,
                  `Couldn't parse texture atlas ${e}: ${r.message}`
                );
              }
            },
            (a, r) => {
              this.error(
                i,
                e,
                `Couldn't load texture atlas ${e}: status ${a}, ${r}`
              );
            }
          );
      }
      get(e) {
        return this.assets[this.pathPrefix + e];
      }
      require(e) {
        e = this.pathPrefix + e;
        let t = this.assets[e];
        if (t) return t;
        let i = this.errors[e];
        throw Error(
          "Asset not found: " +
            e +
            (i
              ? `
` + i
              : "")
        );
      }
      remove(e) {
        e = this.pathPrefix + e;
        let t = this.assets[e];
        return t.dispose && t.dispose(), delete this.assets[e], t;
      }
      removeAll() {
        for (let e in this.assets) {
          let t = this.assets[e];
          t.dispose && t.dispose();
        }
        this.assets = {};
      }
      isLoadingComplete() {
        return this.toLoad == 0;
      }
      getToLoad() {
        return this.toLoad;
      }
      getLoaded() {
        return this.loaded;
      }
      dispose() {
        this.removeAll();
      }
      hasErrors() {
        return Object.keys(this.errors).length > 0;
      }
      getErrors() {
        return this.errors;
      }
    },
    Yi = class {
      callbacks = {};
      rawDataUris = {};
      dataUriToString(e) {
        if (!e.startsWith("data:")) throw new Error("Not a data URI.");
        let t = e.indexOf("base64,");
        return t != -1
          ? ((t += 7), atob(e.substr(t)))
          : e.substr(e.indexOf(",") + 1);
      }
      base64ToUint8Array(e) {
        for (
          var t = window.atob(e), i = t.length, s = new Uint8Array(i), n = 0;
          n < i;
          n++
        )
          s[n] = t.charCodeAt(n);
        return s;
      }
      dataUriToUint8Array(e) {
        if (!e.startsWith("data:")) throw new Error("Not a data URI.");
        let t = e.indexOf("base64,");
        if (t == -1) throw new Error("Not a binary data URI.");
        return (t += 7), this.base64ToUint8Array(e.substr(t));
      }
      downloadText(e, t, i) {
        if (this.start(e, t, i)) return;
        if (this.rawDataUris[e]) {
          try {
            let d = this.rawDataUris[e];
            this.finish(e, 200, this.dataUriToString(d));
          } catch (d) {
            this.finish(e, 400, JSON.stringify(d));
          }
          return;
        }
        let s = new XMLHttpRequest();
        s.overrideMimeType("text/html"), s.open("GET", e, !0);
        let n = () => {
          this.finish(e, s.status, s.responseText);
        };
        (s.onload = n), (s.onerror = n), s.send();
      }
      downloadJson(e, t, i) {
        this.downloadText(
          e,
          (s) => {
            t(JSON.parse(s));
          },
          i
        );
      }
      downloadBinary(e, t, i) {
        if (this.start(e, t, i)) return;
        if (this.rawDataUris[e]) {
          try {
            let d = this.rawDataUris[e];
            this.finish(e, 200, this.dataUriToUint8Array(d));
          } catch (d) {
            this.finish(e, 400, JSON.stringify(d));
          }
          return;
        }
        let s = new XMLHttpRequest();
        s.open("GET", e, !0), (s.responseType = "arraybuffer");
        let n = () => {
          this.finish(e, s.status, s.response);
        };
        (s.onload = () => {
          s.status == 200 || s.status == 0
            ? this.finish(e, 200, new Uint8Array(s.response))
            : n();
        }),
          (s.onerror = n),
          s.send();
      }
      start(e, t, i) {
        let s = this.callbacks[e];
        try {
          if (s) return !0;
          this.callbacks[e] = s = [];
        } finally {
          s.push(t, i);
        }
      }
      finish(e, t, i) {
        let s = this.callbacks[e];
        delete this.callbacks[e];
        let n = t == 200 || t == 0 ? [i] : [t, i];
        for (let d = n.length - 1, a = s.length; d < a; d += 2)
          s[d].apply(null, n);
      }
    },
    Li = class {
      data;
      intValue = 0;
      floatValue = 0;
      stringValue = null;
      time = 0;
      volume = 0;
      balance = 0;
      constructor(e, t) {
        if (!t) throw new Error("data cannot be null.");
        (this.time = e), (this.data = t);
      }
    },
    Xi = class {
      name;
      intValue = 0;
      floatValue = 0;
      stringValue = null;
      audioPath = null;
      volume = 0;
      balance = 0;
      constructor(e) {
        this.name = e;
      }
    },
    Ss = class {
      data;
      bones;
      target;
      bendDirection = 0;
      compress = !1;
      stretch = !1;
      mix = 1;
      softness = 0;
      active = !1;
      constructor(e, t) {
        if (!e) throw new Error("data cannot be null.");
        if (!t) throw new Error("skeleton cannot be null.");
        (this.data = e), (this.bones = new Array());
        for (let s = 0; s < e.bones.length; s++) {
          let n = t.findBone(e.bones[s].name);
          if (!n) throw new Error(`Couldn't find bone ${e.bones[s].name}`);
          this.bones.push(n);
        }
        let i = t.findBone(e.target.name);
        if (!i) throw new Error(`Couldn't find bone ${e.target.name}`);
        (this.target = i),
          (this.mix = e.mix),
          (this.softness = e.softness),
          (this.bendDirection = e.bendDirection),
          (this.compress = e.compress),
          (this.stretch = e.stretch);
      }
      isActive() {
        return this.active;
      }
      setToSetupPose() {
        const e = this.data;
        (this.mix = e.mix),
          (this.softness = e.softness),
          (this.bendDirection = e.bendDirection),
          (this.compress = e.compress),
          (this.stretch = e.stretch);
      }
      update(e) {
        if (this.mix == 0) return;
        let t = this.target,
          i = this.bones;
        switch (i.length) {
          case 1:
            this.apply1(
              i[0],
              t.worldX,
              t.worldY,
              this.compress,
              this.stretch,
              this.data.uniform,
              this.mix
            );
            break;
          case 2:
            this.apply2(
              i[0],
              i[1],
              t.worldX,
              t.worldY,
              this.bendDirection,
              this.stretch,
              this.data.uniform,
              this.softness,
              this.mix
            );
            break;
        }
      }
      apply1(e, t, i, s, n, d, a) {
        let r = e.parent;
        if (!r) throw new Error("IK bone must have parent.");
        let o = r.a,
          l = r.b,
          h = r.c,
          u = r.d,
          c = -e.ashearX - e.arotation,
          f = 0,
          m = 0;
        switch (e.inherit) {
          case 1:
            (f = (t - e.worldX) * X.signum(e.skeleton.scaleX)),
              (m = (i - e.worldY) * X.signum(e.skeleton.scaleY));
            break;
          case 2:
            let x = Math.abs(o * u - l * h) / Math.max(1e-4, o * o + h * h),
              p = o / e.skeleton.scaleX,
              w = h / e.skeleton.scaleY;
            (l = -w * x * e.skeleton.scaleX),
              (u = p * x * e.skeleton.scaleY),
              (c += Math.atan2(w, p) * X.radDeg);
          default:
            let y = t - r.worldX,
              v = i - r.worldY,
              A = o * u - l * h;
            Math.abs(A) <= 1e-4
              ? ((f = 0), (m = 0))
              : ((f = (y * u - v * l) / A - e.ax),
                (m = (v * o - y * h) / A - e.ay));
        }
        (c += Math.atan2(m, f) * X.radDeg),
          e.ascaleX < 0 && (c += 180),
          c > 180 ? (c -= 360) : c < -180 && (c += 360);
        let g = e.ascaleX,
          b = e.ascaleY;
        if (s || n) {
          switch (e.inherit) {
            case 3:
            case 4:
              (f = t - e.worldX), (m = i - e.worldY);
          }
          const x = e.data.length * g;
          if (x > 1e-4) {
            const p = f * f + m * m;
            if ((s && p < x * x) || (n && p > x * x)) {
              const w = (Math.sqrt(p) / x - 1) * a + 1;
              (g *= w), d && (b *= w);
            }
          }
        }
        e.updateWorldTransformWith(
          e.ax,
          e.ay,
          e.arotation + c * a,
          g,
          b,
          e.ashearX,
          e.ashearY
        );
      }
      apply2(e, t, i, s, n, d, a, r, o) {
        if (e.inherit != 0 || t.inherit != 0) return;
        let l = e.ax,
          h = e.ay,
          u = e.ascaleX,
          c = e.ascaleY,
          f = u,
          m = c,
          g = t.ascaleX,
          b = 0,
          x = 0,
          p = 0;
        u < 0 ? ((u = -u), (b = 180), (p = -1)) : ((b = 0), (p = 1)),
          c < 0 && ((c = -c), (p = -p)),
          g < 0 ? ((g = -g), (x = 180)) : (x = 0);
        let w = t.ax,
          y = 0,
          v = 0,
          A = 0,
          C = e.a,
          T = e.b,
          I = e.c,
          M = e.d,
          F = Math.abs(u - c) <= 1e-4;
        !F || d
          ? ((y = 0), (v = C * w + e.worldX), (A = I * w + e.worldY))
          : ((y = t.ay),
            (v = C * w + T * y + e.worldX),
            (A = I * w + M * y + e.worldY));
        let L = e.parent;
        if (!L) throw new Error("IK parent must itself have a parent.");
        (C = L.a), (T = L.b), (I = L.c), (M = L.d);
        let k = C * M - T * I,
          R = v - L.worldX,
          Y = A - L.worldY;
        k = Math.abs(k) <= 1e-4 ? 0 : 1 / k;
        let ie = (R * M - Y * T) * k - l,
          se = (Y * C - R * I) * k - h,
          te = Math.sqrt(ie * ie + se * se),
          de = t.data.length * g,
          ce,
          ue;
        if (te < 1e-4) {
          this.apply1(e, i, s, !1, d, !1, o),
            t.updateWorldTransformWith(
              w,
              y,
              0,
              t.ascaleX,
              t.ascaleY,
              t.ashearX,
              t.ashearY
            );
          return;
        }
        (R = i - L.worldX), (Y = s - L.worldY);
        let J = (R * M - Y * T) * k - l,
          ne = (Y * C - R * I) * k - h,
          ae = J * J + ne * ne;
        if (r != 0) {
          r *= u * (g + 1) * 0.5;
          let ge = Math.sqrt(ae),
            be = ge - te - de * u + r;
          if (be > 0) {
            let ve = Math.min(1, be / (r * 2)) - 1;
            (ve = (be - r * (1 - ve * ve)) / ge),
              (J -= ve * J),
              (ne -= ve * ne),
              (ae = J * J + ne * ne);
          }
        }
        e: if (F) {
          de *= u;
          let ge = (ae - te * te - de * de) / (2 * te * de);
          ge < -1
            ? ((ge = -1), (ue = Math.PI * n))
            : ge > 1
            ? ((ge = 1),
              (ue = 0),
              d &&
                ((C = (Math.sqrt(ae) / (te + de) - 1) * o + 1),
                (f *= C),
                a && (m *= C)))
            : (ue = Math.acos(ge) * n),
            (C = te + de * ge),
            (T = de * Math.sin(ue)),
            (ce = Math.atan2(ne * C - J * T, J * C + ne * T));
        } else {
          (C = u * de), (T = c * de);
          let ge = C * C,
            be = T * T,
            ve = Math.atan2(ne, J);
          I = be * te * te + ge * ae - ge * be;
          let qe = -2 * be * te,
            pt = be - ge;
          if (((M = qe * qe - 4 * pt * I), M >= 0)) {
            let at = Math.sqrt(M);
            qe < 0 && (at = -at), (at = -(qe + at) * 0.5);
            let wt = at / pt,
              Qs = I / at,
              qt = Math.abs(wt) < Math.abs(Qs) ? wt : Qs;
            if (((wt = ae - qt * qt), wt >= 0)) {
              (Y = Math.sqrt(wt) * n),
                (ce = ve - Math.atan2(Y, qt)),
                (ue = Math.atan2(Y / c, (qt - te) / u));
              break e;
            }
          }
          let js = X.PI,
            Wt = te - C,
            $i = Wt * Wt,
            Zs = 0,
            Js = 0,
            zt = te + C,
            es = zt * zt,
            Ks = 0;
          (I = (-C * te) / (ge - be)),
            I >= -1 &&
              I <= 1 &&
              ((I = Math.acos(I)),
              (R = C * Math.cos(I) + te),
              (Y = T * Math.sin(I)),
              (M = R * R + Y * Y),
              M < $i && ((js = I), ($i = M), (Wt = R), (Zs = Y)),
              M > es && ((Js = I), (es = M), (zt = R), (Ks = Y))),
            ae <= ($i + es) * 0.5
              ? ((ce = ve - Math.atan2(Zs * n, Wt)), (ue = js * n))
              : ((ce = ve - Math.atan2(Ks * n, zt)), (ue = Js * n));
        }
        let Ee = Math.atan2(y, w) * p,
          Te = e.arotation;
        (ce = (ce - Ee) * X.radDeg + b - Te),
          ce > 180 ? (ce -= 360) : ce < -180 && (ce += 360),
          e.updateWorldTransformWith(l, h, Te + ce * o, f, m, 0, 0),
          (Te = t.arotation),
          (ue = ((ue + Ee) * X.radDeg - t.ashearX) * p + x - Te),
          ue > 180 ? (ue -= 360) : ue < -180 && (ue += 360),
          t.updateWorldTransformWith(
            w,
            y,
            Te + ue * o,
            t.ascaleX,
            t.ascaleY,
            t.ashearX,
            t.ashearY
          );
      }
    },
    Pi = class extends ct {
      bones = new Array();
      _target = null;
      set target(e) {
        this._target = e;
      }
      get target() {
        if (this._target) return this._target;
        throw new Error("BoneData not set.");
      }
      bendDirection = 0;
      compress = !1;
      stretch = !1;
      uniform = !1;
      mix = 0;
      softness = 0;
      constructor(e) {
        super(e, 0, !1);
      }
    },
    Bi = class extends ct {
      bones = new Array();
      _target = null;
      set target(e) {
        this._target = e;
      }
      get target() {
        if (this._target) return this._target;
        throw new Error("SlotData not set.");
      }
      positionMode = Mt.Fixed;
      spacingMode = Rt.Fixed;
      rotateMode = Ft.Chain;
      offsetRotation = 0;
      position = 0;
      spacing = 0;
      mixRotate = 0;
      mixX = 0;
      mixY = 0;
      constructor(e) {
        super(e, 0, !1);
      }
    },
    Mt = ((e) => (
      (e[(e.Fixed = 0)] = "Fixed"), (e[(e.Percent = 1)] = "Percent"), e
    ))(Mt || {}),
    Rt = ((e) => (
      (e[(e.Length = 0)] = "Length"),
      (e[(e.Fixed = 1)] = "Fixed"),
      (e[(e.Percent = 2)] = "Percent"),
      (e[(e.Proportional = 3)] = "Proportional"),
      e
    ))(Rt || {}),
    Ft = ((e) => (
      (e[(e.Tangent = 0)] = "Tangent"),
      (e[(e.Chain = 1)] = "Chain"),
      (e[(e.ChainScale = 2)] = "ChainScale"),
      e
    ))(Ft || {}),
    _e = class {
      data;
      bones;
      target;
      position = 0;
      spacing = 0;
      mixRotate = 0;
      mixX = 0;
      mixY = 0;
      spaces = new Array();
      positions = new Array();
      world = new Array();
      curves = new Array();
      lengths = new Array();
      segments = new Array();
      active = !1;
      constructor(e, t) {
        if (!e) throw new Error("data cannot be null.");
        if (!t) throw new Error("skeleton cannot be null.");
        (this.data = e), (this.bones = new Array());
        for (let s = 0, n = e.bones.length; s < n; s++) {
          let d = t.findBone(e.bones[s].name);
          if (!d) throw new Error(`Couldn't find bone ${e.bones[s].name}.`);
          this.bones.push(d);
        }
        let i = t.findSlot(e.target.name);
        if (!i) throw new Error(`Couldn't find target bone ${e.target.name}`);
        (this.target = i),
          (this.position = e.position),
          (this.spacing = e.spacing),
          (this.mixRotate = e.mixRotate),
          (this.mixX = e.mixX),
          (this.mixY = e.mixY);
      }
      isActive() {
        return this.active;
      }
      setToSetupPose() {
        const e = this.data;
        (this.position = e.position),
          (this.spacing = e.spacing),
          (this.mixRotate = e.mixRotate),
          (this.mixX = e.mixX),
          (this.mixY = e.mixY);
      }
      update(e) {
        let t = this.target.getAttachment();
        if (!(t instanceof Ze)) return;
        let i = this.mixRotate,
          s = this.mixX,
          n = this.mixY;
        if (i == 0 && s == 0 && n == 0) return;
        let d = this.data,
          a = d.rotateMode == 0,
          r = d.rotateMode == 2,
          o = this.bones,
          l = o.length,
          h = a ? l : l + 1,
          u = P.setArraySize(this.spaces, h),
          c = r ? (this.lengths = P.setArraySize(this.lengths, l)) : [],
          f = this.spacing;
        switch (d.spacingMode) {
          case 2:
            if (r)
              for (let v = 0, A = h - 1; v < A; v++) {
                let C = o[v],
                  T = C.data.length,
                  I = T * C.a,
                  M = T * C.c;
                c[v] = Math.sqrt(I * I + M * M);
              }
            P.arrayFill(u, 1, h, f);
            break;
          case 3:
            let w = 0;
            for (let v = 0, A = h - 1; v < A; ) {
              let C = o[v],
                T = C.data.length;
              if (T < _e.epsilon) r && (c[v] = 0), (u[++v] = f);
              else {
                let I = T * C.a,
                  M = T * C.c,
                  F = Math.sqrt(I * I + M * M);
                r && (c[v] = F), (u[++v] = F), (w += F);
              }
            }
            if (w > 0) {
              w = (h / w) * f;
              for (let v = 1; v < h; v++) u[v] *= w;
            }
            break;
          default:
            let y = d.spacingMode == 0;
            for (let v = 0, A = h - 1; v < A; ) {
              let C = o[v],
                T = C.data.length;
              if (T < _e.epsilon) r && (c[v] = 0), (u[++v] = f);
              else {
                let I = T * C.a,
                  M = T * C.c,
                  F = Math.sqrt(I * I + M * M);
                r && (c[v] = F), (u[++v] = ((y ? T + f : f) * F) / T);
              }
            }
        }
        let m = this.computeWorldPositions(t, h, a),
          g = m[0],
          b = m[1],
          x = d.offsetRotation,
          p = !1;
        if (x == 0) p = d.rotateMode == 1;
        else {
          p = !1;
          let w = this.target.bone;
          x *= w.a * w.d - w.b * w.c > 0 ? X.degRad : -X.degRad;
        }
        for (let w = 0, y = 3; w < l; w++, y += 3) {
          let v = o[w];
          (v.worldX += (g - v.worldX) * s), (v.worldY += (b - v.worldY) * n);
          let A = m[y],
            C = m[y + 1],
            T = A - g,
            I = C - b;
          if (r) {
            let M = c[w];
            if (M != 0) {
              let F = (Math.sqrt(T * T + I * I) / M - 1) * i + 1;
              (v.a *= F), (v.c *= F);
            }
          }
          if (((g = A), (b = C), i > 0)) {
            let M = v.a,
              F = v.b,
              L = v.c,
              k = v.d,
              R = 0,
              Y = 0,
              ie = 0;
            if (
              (a
                ? (R = m[y - 1])
                : u[w + 1] == 0
                ? (R = m[y + 2])
                : (R = Math.atan2(I, T)),
              (R -= Math.atan2(L, M)),
              p)
            ) {
              (Y = Math.cos(R)), (ie = Math.sin(R));
              let se = v.data.length;
              (g += (se * (Y * M - ie * L) - T) * i),
                (b += (se * (ie * M + Y * L) - I) * i);
            } else R += x;
            R > X.PI ? (R -= X.PI2) : R < -X.PI && (R += X.PI2),
              (R *= i),
              (Y = Math.cos(R)),
              (ie = Math.sin(R)),
              (v.a = Y * M - ie * L),
              (v.b = Y * F - ie * k),
              (v.c = ie * M + Y * L),
              (v.d = ie * F + Y * k);
          }
          v.updateAppliedTransform();
        }
      }
      computeWorldPositions(e, t, i) {
        let s = this.target,
          n = this.position,
          d = this.spaces,
          a = P.setArraySize(this.positions, t * 3 + 2),
          r = this.world,
          o = e.closed,
          l = e.worldVerticesLength,
          h = l / 6,
          u = _e.NONE;
        if (!e.constantSpeed) {
          let se = e.lengths;
          h -= o ? 1 : 2;
          let te = se[h];
          this.data.positionMode == 1 && (n *= te);
          let de;
          switch (this.data.spacingMode) {
            case 2:
              de = te;
              break;
            case 3:
              de = te / t;
              break;
            default:
              de = 1;
          }
          r = P.setArraySize(this.world, 8);
          for (let ce = 0, ue = 0, J = 0; ce < t; ce++, ue += 3) {
            let ne = d[ce] * de;
            n += ne;
            let ae = n;
            if (o) (ae %= te), ae < 0 && (ae += te), (J = 0);
            else if (ae < 0) {
              u != _e.BEFORE &&
                ((u = _e.BEFORE), e.computeWorldVertices(s, 2, 4, r, 0, 2)),
                this.addBeforePosition(ae, r, 0, a, ue);
              continue;
            } else if (ae > te) {
              u != _e.AFTER &&
                ((u = _e.AFTER), e.computeWorldVertices(s, l - 6, 4, r, 0, 2)),
                this.addAfterPosition(ae - te, r, 0, a, ue);
              continue;
            }
            for (; ; J++) {
              let Ee = se[J];
              if (!(ae > Ee)) {
                if (J == 0) ae /= Ee;
                else {
                  let Te = se[J - 1];
                  ae = (ae - Te) / (Ee - Te);
                }
                break;
              }
            }
            J != u &&
              ((u = J),
              o && J == h
                ? (e.computeWorldVertices(s, l - 4, 4, r, 0, 2),
                  e.computeWorldVertices(s, 0, 4, r, 4, 2))
                : e.computeWorldVertices(s, J * 6 + 2, 8, r, 0, 2)),
              this.addCurvePosition(
                ae,
                r[0],
                r[1],
                r[2],
                r[3],
                r[4],
                r[5],
                r[6],
                r[7],
                a,
                ue,
                i || (ce > 0 && ne == 0)
              );
          }
          return a;
        }
        o
          ? ((l += 2),
            (r = P.setArraySize(this.world, l)),
            e.computeWorldVertices(s, 2, l - 4, r, 0, 2),
            e.computeWorldVertices(s, 0, 2, r, l - 4, 2),
            (r[l - 2] = r[0]),
            (r[l - 1] = r[1]))
          : (h--,
            (l -= 4),
            (r = P.setArraySize(this.world, l)),
            e.computeWorldVertices(s, 2, l, r, 0, 2));
        let c = P.setArraySize(this.curves, h),
          f = 0,
          m = r[0],
          g = r[1],
          b = 0,
          x = 0,
          p = 0,
          w = 0,
          y = 0,
          v = 0,
          A = 0,
          C = 0,
          T = 0,
          I = 0,
          M = 0,
          F = 0,
          L = 0,
          k = 0;
        for (let se = 0, te = 2; se < h; se++, te += 6)
          (b = r[te]),
            (x = r[te + 1]),
            (p = r[te + 2]),
            (w = r[te + 3]),
            (y = r[te + 4]),
            (v = r[te + 5]),
            (A = (m - b * 2 + p) * 0.1875),
            (C = (g - x * 2 + w) * 0.1875),
            (T = ((b - p) * 3 - m + y) * 0.09375),
            (I = ((x - w) * 3 - g + v) * 0.09375),
            (M = A * 2 + T),
            (F = C * 2 + I),
            (L = (b - m) * 0.75 + A + T * 0.16666667),
            (k = (x - g) * 0.75 + C + I * 0.16666667),
            (f += Math.sqrt(L * L + k * k)),
            (L += M),
            (k += F),
            (M += T),
            (F += I),
            (f += Math.sqrt(L * L + k * k)),
            (L += M),
            (k += F),
            (f += Math.sqrt(L * L + k * k)),
            (L += M + T),
            (k += F + I),
            (f += Math.sqrt(L * L + k * k)),
            (c[se] = f),
            (m = y),
            (g = v);
        this.data.positionMode == 1 && (n *= f);
        let R;
        switch (this.data.spacingMode) {
          case 2:
            R = f;
            break;
          case 3:
            R = f / t;
            break;
          default:
            R = 1;
        }
        let Y = this.segments,
          ie = 0;
        for (let se = 0, te = 0, de = 0, ce = 0; se < t; se++, te += 3) {
          let ue = d[se] * R;
          n += ue;
          let J = n;
          if (o) (J %= f), J < 0 && (J += f), (de = 0);
          else if (J < 0) {
            this.addBeforePosition(J, r, 0, a, te);
            continue;
          } else if (J > f) {
            this.addAfterPosition(J - f, r, l - 4, a, te);
            continue;
          }
          for (; ; de++) {
            let ne = c[de];
            if (!(J > ne)) {
              if (de == 0) J /= ne;
              else {
                let ae = c[de - 1];
                J = (J - ae) / (ne - ae);
              }
              break;
            }
          }
          if (de != u) {
            u = de;
            let ne = de * 6;
            for (
              m = r[ne],
                g = r[ne + 1],
                b = r[ne + 2],
                x = r[ne + 3],
                p = r[ne + 4],
                w = r[ne + 5],
                y = r[ne + 6],
                v = r[ne + 7],
                A = (m - b * 2 + p) * 0.03,
                C = (g - x * 2 + w) * 0.03,
                T = ((b - p) * 3 - m + y) * 0.006,
                I = ((x - w) * 3 - g + v) * 0.006,
                M = A * 2 + T,
                F = C * 2 + I,
                L = (b - m) * 0.3 + A + T * 0.16666667,
                k = (x - g) * 0.3 + C + I * 0.16666667,
                ie = Math.sqrt(L * L + k * k),
                Y[0] = ie,
                ne = 1;
              ne < 8;
              ne++
            )
              (L += M),
                (k += F),
                (M += T),
                (F += I),
                (ie += Math.sqrt(L * L + k * k)),
                (Y[ne] = ie);
            (L += M),
              (k += F),
              (ie += Math.sqrt(L * L + k * k)),
              (Y[8] = ie),
              (L += M + T),
              (k += F + I),
              (ie += Math.sqrt(L * L + k * k)),
              (Y[9] = ie),
              (ce = 0);
          }
          for (J *= ie; ; ce++) {
            let ne = Y[ce];
            if (!(J > ne)) {
              if (ce == 0) J /= ne;
              else {
                let ae = Y[ce - 1];
                J = ce + (J - ae) / (ne - ae);
              }
              break;
            }
          }
          this.addCurvePosition(
            J * 0.1,
            m,
            g,
            b,
            x,
            p,
            w,
            y,
            v,
            a,
            te,
            i || (se > 0 && ue == 0)
          );
        }
        return a;
      }
      addBeforePosition(e, t, i, s, n) {
        let d = t[i],
          a = t[i + 1],
          r = t[i + 2] - d,
          o = t[i + 3] - a,
          l = Math.atan2(o, r);
        (s[n] = d + e * Math.cos(l)),
          (s[n + 1] = a + e * Math.sin(l)),
          (s[n + 2] = l);
      }
      addAfterPosition(e, t, i, s, n) {
        let d = t[i + 2],
          a = t[i + 3],
          r = d - t[i],
          o = a - t[i + 1],
          l = Math.atan2(o, r);
        (s[n] = d + e * Math.cos(l)),
          (s[n + 1] = a + e * Math.sin(l)),
          (s[n + 2] = l);
      }
      addCurvePosition(e, t, i, s, n, d, a, r, o, l, h, u) {
        if (e == 0 || isNaN(e)) {
          (l[h] = t), (l[h + 1] = i), (l[h + 2] = Math.atan2(n - i, s - t));
          return;
        }
        let c = e * e,
          f = c * e,
          m = 1 - e,
          g = m * m,
          b = g * m,
          x = m * e,
          p = x * 3,
          w = m * p,
          y = p * e,
          v = t * b + s * w + d * y + r * f,
          A = i * b + n * w + a * y + o * f;
        (l[h] = v),
          (l[h + 1] = A),
          u &&
            (e < 0.001
              ? (l[h + 2] = Math.atan2(n - i, s - t))
              : (l[h + 2] = Math.atan2(
                  A - (i * g + n * x * 2 + a * c),
                  v - (t * g + s * x * 2 + d * c)
                )));
      }
    },
    it = _e;
  B(it, "NONE", -1),
    B(it, "BEFORE", -2),
    B(it, "AFTER", -3),
    B(it, "epsilon", 1e-5);
  var fr = class {
      data;
      _bone = null;
      set bone(e) {
        this._bone = e;
      }
      get bone() {
        if (this._bone) return this._bone;
        throw new Error("Bone not set.");
      }
      inertia = 0;
      strength = 0;
      damping = 0;
      massInverse = 0;
      wind = 0;
      gravity = 0;
      mix = 0;
      _reset = !0;
      ux = 0;
      uy = 0;
      cx = 0;
      cy = 0;
      tx = 0;
      ty = 0;
      xOffset = 0;
      xVelocity = 0;
      yOffset = 0;
      yVelocity = 0;
      rotateOffset = 0;
      rotateVelocity = 0;
      scaleOffset = 0;
      scaleVelocity = 0;
      active = !1;
      skeleton;
      remaining = 0;
      lastTime = 0;
      constructor(e, t) {
        (this.data = e),
          (this.skeleton = t),
          (this.bone = t.bones[e.bone.index]),
          (this.inertia = e.inertia),
          (this.strength = e.strength),
          (this.damping = e.damping),
          (this.massInverse = e.massInverse),
          (this.wind = e.wind),
          (this.gravity = e.gravity),
          (this.mix = e.mix);
      }
      reset() {
        (this.remaining = 0),
          (this.lastTime = this.skeleton.time),
          (this._reset = !0),
          (this.xOffset = 0),
          (this.xVelocity = 0),
          (this.yOffset = 0),
          (this.yVelocity = 0),
          (this.rotateOffset = 0),
          (this.rotateVelocity = 0),
          (this.scaleOffset = 0),
          (this.scaleVelocity = 0);
      }
      setToSetupPose() {
        const e = this.data;
        (this.inertia = e.inertia),
          (this.strength = e.strength),
          (this.damping = e.damping),
          (this.massInverse = e.massInverse),
          (this.wind = e.wind),
          (this.gravity = e.gravity),
          (this.mix = e.mix);
      }
      isActive() {
        return this.active;
      }
      update(e) {
        const t = this.mix;
        if (t == 0) return;
        const i = this.data.x > 0,
          s = this.data.y > 0,
          n = this.data.rotate > 0 || this.data.shearX > 0,
          d = this.data.scaleX > 0,
          a = this.bone,
          r = a.data.length;
        switch (e) {
          case 0:
            return;
          case 1:
            this.reset();
          case 2:
            const o = this.skeleton,
              l = Math.max(this.skeleton.time - this.lastTime, 0);
            (this.remaining += l), (this.lastTime = o.time);
            const h = a.worldX,
              u = a.worldY;
            if (this._reset) (this._reset = !1), (this.ux = h), (this.uy = u);
            else {
              let c = this.remaining,
                f = this.inertia,
                m = this.data.step,
                g = this.skeleton.data.referenceScale,
                b = -1,
                x = this.data.limit * l,
                p = x * Math.abs(o.scaleY);
              if (((x *= Math.abs(o.scaleX)), i || s)) {
                if (i) {
                  const w = (this.ux - h) * f;
                  (this.xOffset += w > x ? x : w < -x ? -x : w), (this.ux = h);
                }
                if (s) {
                  const w = (this.uy - u) * f;
                  (this.yOffset += w > p ? p : w < -p ? -p : w), (this.uy = u);
                }
                if (c >= m) {
                  b = Math.pow(this.damping, 60 * m);
                  const w = this.massInverse * m,
                    y = this.strength,
                    v = this.wind * g * o.scaleX,
                    A = this.gravity * g * o.scaleY;
                  do
                    i &&
                      ((this.xVelocity += (v - this.xOffset * y) * w),
                      (this.xOffset += this.xVelocity * m),
                      (this.xVelocity *= b)),
                      s &&
                        ((this.yVelocity -= (A + this.yOffset * y) * w),
                        (this.yOffset += this.yVelocity * m),
                        (this.yVelocity *= b)),
                      (c -= m);
                  while (c >= m);
                }
                i && (a.worldX += this.xOffset * t * this.data.x),
                  s && (a.worldY += this.yOffset * t * this.data.y);
              }
              if (n || d) {
                let w = Math.atan2(a.c, a.a),
                  y = 0,
                  v = 0,
                  A = 0,
                  C = this.cx - a.worldX,
                  T = this.cy - a.worldY;
                if (
                  (C > x ? (C = x) : C < -x && (C = -x),
                  T > p ? (T = p) : T < -p && (T = -p),
                  n)
                ) {
                  A = (this.data.rotate + this.data.shearX) * t;
                  let I =
                    Math.atan2(T + this.ty, C + this.tx) -
                    w -
                    this.rotateOffset * A;
                  (this.rotateOffset +=
                    (I - Math.ceil(I * X.invPI2 - 0.5) * X.PI2) * f),
                    (I = this.rotateOffset * A + w),
                    (y = Math.cos(I)),
                    (v = Math.sin(I)),
                    d &&
                      ((I = r * a.getWorldScaleX()),
                      I > 0 && (this.scaleOffset += ((C * y + T * v) * f) / I));
                } else {
                  (y = Math.cos(w)), (v = Math.sin(w));
                  const I = r * a.getWorldScaleX();
                  I > 0 && (this.scaleOffset += ((C * y + T * v) * f) / I);
                }
                if (((c = this.remaining), c >= m)) {
                  b == -1 && (b = Math.pow(this.damping, 60 * m));
                  const I = this.massInverse * m,
                    M = this.strength,
                    F = this.wind,
                    L = ut.yDown ? -this.gravity : this.gravity,
                    k = r / g;
                  for (;;)
                    if (
                      ((c -= m),
                      d &&
                        ((this.scaleVelocity +=
                          (F * y - L * v - this.scaleOffset * M) * I),
                        (this.scaleOffset += this.scaleVelocity * m),
                        (this.scaleVelocity *= b)),
                      n)
                    ) {
                      if (
                        ((this.rotateVelocity -=
                          ((F * v + L * y) * k + this.rotateOffset * M) * I),
                        (this.rotateOffset += this.rotateVelocity * m),
                        (this.rotateVelocity *= b),
                        c < m)
                      )
                        break;
                      const R = this.rotateOffset * A + w;
                      (y = Math.cos(R)), (v = Math.sin(R));
                    } else if (c < m) break;
                }
              }
              this.remaining = c;
            }
            (this.cx = a.worldX), (this.cy = a.worldY);
            break;
          case 3:
            i && (a.worldX += this.xOffset * t * this.data.x),
              s && (a.worldY += this.yOffset * t * this.data.y);
        }
        if (n) {
          let o = this.rotateOffset * t,
            l = 0,
            h = 0,
            u = 0;
          if (this.data.shearX > 0) {
            let c = 0;
            this.data.rotate > 0 &&
              ((c = o * this.data.rotate),
              (l = Math.sin(c)),
              (h = Math.cos(c)),
              (u = a.b),
              (a.b = h * u - l * a.d),
              (a.d = l * u + h * a.d)),
              (c += o * this.data.shearX),
              (l = Math.sin(c)),
              (h = Math.cos(c)),
              (u = a.a),
              (a.a = h * u - l * a.c),
              (a.c = l * u + h * a.c);
          } else
            (o *= this.data.rotate),
              (l = Math.sin(o)),
              (h = Math.cos(o)),
              (u = a.a),
              (a.a = h * u - l * a.c),
              (a.c = l * u + h * a.c),
              (u = a.b),
              (a.b = h * u - l * a.d),
              (a.d = l * u + h * a.d);
        }
        if (d) {
          const o = 1 + this.scaleOffset * t * this.data.scaleX;
          (a.a *= o), (a.c *= o);
        }
        e != 3 && ((this.tx = r * a.a), (this.ty = r * a.c)),
          a.updateAppliedTransform();
      }
      translate(e, t) {
        (this.ux -= e), (this.uy -= t), (this.cx -= e), (this.cy -= t);
      }
      rotate(e, t, i) {
        const s = i * X.degRad,
          n = Math.cos(s),
          d = Math.sin(s),
          a = this.cx - e,
          r = this.cy - t;
        this.translate(a * n - r * d - a, a * d + r * n - r);
      }
    },
    Ts = class {
      data;
      bone;
      color;
      darkColor = null;
      attachment = null;
      attachmentState = 0;
      sequenceIndex = -1;
      deform = new Array();
      constructor(e, t) {
        if (!e) throw new Error("data cannot be null.");
        if (!t) throw new Error("bone cannot be null.");
        (this.data = e),
          (this.bone = t),
          (this.color = new V()),
          (this.darkColor = e.darkColor ? new V() : null),
          this.setToSetupPose();
      }
      getSkeleton() {
        return this.bone.skeleton;
      }
      getAttachment() {
        return this.attachment;
      }
      setAttachment(e) {
        this.attachment != e &&
          ((!(e instanceof Re) ||
            !(this.attachment instanceof Re) ||
            e.timelineAttachment != this.attachment.timelineAttachment) &&
            (this.deform.length = 0),
          (this.attachment = e),
          (this.sequenceIndex = -1));
      }
      setToSetupPose() {
        this.color.setFromColor(this.data.color),
          this.darkColor && this.darkColor.setFromColor(this.data.darkColor),
          this.data.attachmentName
            ? ((this.attachment = null),
              this.setAttachment(
                this.bone.skeleton.getAttachment(
                  this.data.index,
                  this.data.attachmentName
                )
              ))
            : (this.attachment = null);
      }
    },
    ks = class {
      data;
      bones;
      target;
      mixRotate = 0;
      mixX = 0;
      mixY = 0;
      mixScaleX = 0;
      mixScaleY = 0;
      mixShearY = 0;
      temp = new Me();
      active = !1;
      constructor(e, t) {
        if (!e) throw new Error("data cannot be null.");
        if (!t) throw new Error("skeleton cannot be null.");
        (this.data = e), (this.bones = new Array());
        for (let s = 0; s < e.bones.length; s++) {
          let n = t.findBone(e.bones[s].name);
          if (!n) throw new Error(`Couldn't find bone ${e.bones[s].name}.`);
          this.bones.push(n);
        }
        let i = t.findBone(e.target.name);
        if (!i) throw new Error(`Couldn't find target bone ${e.target.name}.`);
        (this.target = i),
          (this.mixRotate = e.mixRotate),
          (this.mixX = e.mixX),
          (this.mixY = e.mixY),
          (this.mixScaleX = e.mixScaleX),
          (this.mixScaleY = e.mixScaleY),
          (this.mixShearY = e.mixShearY);
      }
      isActive() {
        return this.active;
      }
      setToSetupPose() {
        const e = this.data;
        (this.mixRotate = e.mixRotate),
          (this.mixX = e.mixX),
          (this.mixY = e.mixY),
          (this.mixScaleX = e.mixScaleX),
          (this.mixScaleY = e.mixScaleY),
          (this.mixShearY = e.mixShearY);
      }
      update(e) {
        (this.mixRotate == 0 &&
          this.mixX == 0 &&
          this.mixY == 0 &&
          this.mixScaleX == 0 &&
          this.mixScaleY == 0 &&
          this.mixShearY == 0) ||
          (this.data.local
            ? this.data.relative
              ? this.applyRelativeLocal()
              : this.applyAbsoluteLocal()
            : this.data.relative
            ? this.applyRelativeWorld()
            : this.applyAbsoluteWorld());
      }
      applyAbsoluteWorld() {
        let e = this.mixRotate,
          t = this.mixX,
          i = this.mixY,
          s = this.mixScaleX,
          n = this.mixScaleY,
          d = this.mixShearY,
          a = t != 0 || i != 0,
          r = this.target,
          o = r.a,
          l = r.b,
          h = r.c,
          u = r.d,
          c = o * u - l * h > 0 ? X.degRad : -X.degRad,
          f = this.data.offsetRotation * c,
          m = this.data.offsetShearY * c,
          g = this.bones;
        for (let b = 0, x = g.length; b < x; b++) {
          let p = g[b];
          if (e != 0) {
            let w = p.a,
              y = p.b,
              v = p.c,
              A = p.d,
              C = Math.atan2(h, o) - Math.atan2(v, w) + f;
            C > X.PI ? (C -= X.PI2) : C < -X.PI && (C += X.PI2), (C *= e);
            let T = Math.cos(C),
              I = Math.sin(C);
            (p.a = T * w - I * v),
              (p.b = T * y - I * A),
              (p.c = I * w + T * v),
              (p.d = I * y + T * A);
          }
          if (a) {
            let w = this.temp;
            r.localToWorld(w.set(this.data.offsetX, this.data.offsetY)),
              (p.worldX += (w.x - p.worldX) * t),
              (p.worldY += (w.y - p.worldY) * i);
          }
          if (s != 0) {
            let w = Math.sqrt(p.a * p.a + p.c * p.c);
            w != 0 &&
              (w =
                (w +
                  (Math.sqrt(o * o + h * h) - w + this.data.offsetScaleX) * s) /
                w),
              (p.a *= w),
              (p.c *= w);
          }
          if (n != 0) {
            let w = Math.sqrt(p.b * p.b + p.d * p.d);
            w != 0 &&
              (w =
                (w +
                  (Math.sqrt(l * l + u * u) - w + this.data.offsetScaleY) * n) /
                w),
              (p.b *= w),
              (p.d *= w);
          }
          if (d > 0) {
            let w = p.b,
              y = p.d,
              v = Math.atan2(y, w),
              A =
                Math.atan2(u, l) -
                Math.atan2(h, o) -
                (v - Math.atan2(p.c, p.a));
            A > X.PI ? (A -= X.PI2) : A < -X.PI && (A += X.PI2),
              (A = v + (A + m) * d);
            let C = Math.sqrt(w * w + y * y);
            (p.b = Math.cos(A) * C), (p.d = Math.sin(A) * C);
          }
          p.updateAppliedTransform();
        }
      }
      applyRelativeWorld() {
        let e = this.mixRotate,
          t = this.mixX,
          i = this.mixY,
          s = this.mixScaleX,
          n = this.mixScaleY,
          d = this.mixShearY,
          a = t != 0 || i != 0,
          r = this.target,
          o = r.a,
          l = r.b,
          h = r.c,
          u = r.d,
          c = o * u - l * h > 0 ? X.degRad : -X.degRad,
          f = this.data.offsetRotation * c,
          m = this.data.offsetShearY * c,
          g = this.bones;
        for (let b = 0, x = g.length; b < x; b++) {
          let p = g[b];
          if (e != 0) {
            let w = p.a,
              y = p.b,
              v = p.c,
              A = p.d,
              C = Math.atan2(h, o) + f;
            C > X.PI ? (C -= X.PI2) : C < -X.PI && (C += X.PI2), (C *= e);
            let T = Math.cos(C),
              I = Math.sin(C);
            (p.a = T * w - I * v),
              (p.b = T * y - I * A),
              (p.c = I * w + T * v),
              (p.d = I * y + T * A);
          }
          if (a) {
            let w = this.temp;
            r.localToWorld(w.set(this.data.offsetX, this.data.offsetY)),
              (p.worldX += w.x * t),
              (p.worldY += w.y * i);
          }
          if (s != 0) {
            let w =
              (Math.sqrt(o * o + h * h) - 1 + this.data.offsetScaleX) * s + 1;
            (p.a *= w), (p.c *= w);
          }
          if (n != 0) {
            let w =
              (Math.sqrt(l * l + u * u) - 1 + this.data.offsetScaleY) * n + 1;
            (p.b *= w), (p.d *= w);
          }
          if (d > 0) {
            let w = Math.atan2(u, l) - Math.atan2(h, o);
            w > X.PI ? (w -= X.PI2) : w < -X.PI && (w += X.PI2);
            let y = p.b,
              v = p.d;
            w = Math.atan2(v, y) + (w - X.PI / 2 + m) * d;
            let A = Math.sqrt(y * y + v * v);
            (p.b = Math.cos(w) * A), (p.d = Math.sin(w) * A);
          }
          p.updateAppliedTransform();
        }
      }
      applyAbsoluteLocal() {
        let e = this.mixRotate,
          t = this.mixX,
          i = this.mixY,
          s = this.mixScaleX,
          n = this.mixScaleY,
          d = this.mixShearY,
          a = this.target,
          r = this.bones;
        for (let o = 0, l = r.length; o < l; o++) {
          let h = r[o],
            u = h.arotation;
          e != 0 && (u += (a.arotation - u + this.data.offsetRotation) * e);
          let c = h.ax,
            f = h.ay;
          (c += (a.ax - c + this.data.offsetX) * t),
            (f += (a.ay - f + this.data.offsetY) * i);
          let m = h.ascaleX,
            g = h.ascaleY;
          s != 0 &&
            m != 0 &&
            (m = (m + (a.ascaleX - m + this.data.offsetScaleX) * s) / m),
            n != 0 &&
              g != 0 &&
              (g = (g + (a.ascaleY - g + this.data.offsetScaleY) * n) / g);
          let b = h.ashearY;
          d != 0 && (b += (a.ashearY - b + this.data.offsetShearY) * d),
            h.updateWorldTransformWith(c, f, u, m, g, h.ashearX, b);
        }
      }
      applyRelativeLocal() {
        let e = this.mixRotate,
          t = this.mixX,
          i = this.mixY,
          s = this.mixScaleX,
          n = this.mixScaleY,
          d = this.mixShearY,
          a = this.target,
          r = this.bones;
        for (let o = 0, l = r.length; o < l; o++) {
          let h = r[o],
            u = h.arotation + (a.arotation + this.data.offsetRotation) * e,
            c = h.ax + (a.ax + this.data.offsetX) * t,
            f = h.ay + (a.ay + this.data.offsetY) * i,
            m = h.ascaleX * ((a.ascaleX - 1 + this.data.offsetScaleX) * s + 1),
            g = h.ascaleY * ((a.ascaleY - 1 + this.data.offsetScaleY) * n + 1),
            b = h.ashearY + (a.ashearY + this.data.offsetShearY) * d;
          h.updateWorldTransformWith(c, f, u, m, g, h.ashearX, b);
        }
      }
    },
    Vi = class {
      data;
      bones;
      slots;
      drawOrder;
      ikConstraints;
      transformConstraints;
      pathConstraints;
      physicsConstraints;
      _updateCache = new Array();
      skin = null;
      color;
      scaleX = 1;
      _scaleY = 1;
      get scaleY() {
        return Vi.yDown ? -this._scaleY : this._scaleY;
      }
      set scaleY(e) {
        this._scaleY = e;
      }
      x = 0;
      y = 0;
      time = 0;
      constructor(e) {
        if (!e) throw new Error("data cannot be null.");
        (this.data = e), (this.bones = new Array());
        for (let t = 0; t < e.bones.length; t++) {
          let i = e.bones[t],
            s;
          if (!i.parent) s = new Fi(i, this, null);
          else {
            let n = this.bones[i.parent.index];
            (s = new Fi(i, this, n)), n.children.push(s);
          }
          this.bones.push(s);
        }
        (this.slots = new Array()), (this.drawOrder = new Array());
        for (let t = 0; t < e.slots.length; t++) {
          let i = e.slots[t],
            s = this.bones[i.boneData.index],
            n = new Ts(i, s);
          this.slots.push(n), this.drawOrder.push(n);
        }
        this.ikConstraints = new Array();
        for (let t = 0; t < e.ikConstraints.length; t++) {
          let i = e.ikConstraints[t];
          this.ikConstraints.push(new Ss(i, this));
        }
        this.transformConstraints = new Array();
        for (let t = 0; t < e.transformConstraints.length; t++) {
          let i = e.transformConstraints[t];
          this.transformConstraints.push(new ks(i, this));
        }
        this.pathConstraints = new Array();
        for (let t = 0; t < e.pathConstraints.length; t++) {
          let i = e.pathConstraints[t];
          this.pathConstraints.push(new it(i, this));
        }
        this.physicsConstraints = new Array();
        for (let t = 0; t < e.physicsConstraints.length; t++) {
          let i = e.physicsConstraints[t];
          this.physicsConstraints.push(new fr(i, this));
        }
        (this.color = new V(1, 1, 1, 1)), this.updateCache();
      }
      updateCache() {
        let e = this._updateCache;
        e.length = 0;
        let t = this.bones;
        for (let u = 0, c = t.length; u < c; u++) {
          let f = t[u];
          (f.sorted = f.data.skinRequired), (f.active = !f.sorted);
        }
        if (this.skin) {
          let u = this.skin.bones;
          for (let c = 0, f = this.skin.bones.length; c < f; c++) {
            let m = this.bones[u[c].index];
            do (m.sorted = !1), (m.active = !0), (m = m.parent);
            while (m);
          }
        }
        let i = this.ikConstraints,
          s = this.transformConstraints,
          n = this.pathConstraints,
          d = this.physicsConstraints,
          a = i.length,
          r = s.length,
          o = n.length,
          l = this.physicsConstraints.length,
          h = a + r + o + l;
        e: for (let u = 0; u < h; u++) {
          for (let c = 0; c < a; c++) {
            let f = i[c];
            if (f.data.order == u) {
              this.sortIkConstraint(f);
              continue e;
            }
          }
          for (let c = 0; c < r; c++) {
            let f = s[c];
            if (f.data.order == u) {
              this.sortTransformConstraint(f);
              continue e;
            }
          }
          for (let c = 0; c < o; c++) {
            let f = n[c];
            if (f.data.order == u) {
              this.sortPathConstraint(f);
              continue e;
            }
          }
          for (let c = 0; c < l; c++) {
            const f = d[c];
            if (f.data.order == u) {
              this.sortPhysicsConstraint(f);
              continue e;
            }
          }
        }
        for (let u = 0, c = t.length; u < c; u++) this.sortBone(t[u]);
      }
      sortIkConstraint(e) {
        if (
          ((e.active =
            e.target.isActive() &&
            (!e.data.skinRequired ||
              (this.skin && P.contains(this.skin.constraints, e.data, !0)))),
          !e.active)
        )
          return;
        let t = e.target;
        this.sortBone(t);
        let i = e.bones,
          s = i[0];
        if ((this.sortBone(s), i.length == 1))
          this._updateCache.push(e), this.sortReset(s.children);
        else {
          let n = i[i.length - 1];
          this.sortBone(n),
            this._updateCache.push(e),
            this.sortReset(s.children),
            (n.sorted = !0);
        }
      }
      sortPathConstraint(e) {
        if (
          ((e.active =
            e.target.bone.isActive() &&
            (!e.data.skinRequired ||
              (this.skin && P.contains(this.skin.constraints, e.data, !0)))),
          !e.active)
        )
          return;
        let t = e.target,
          i = t.data.index,
          s = t.bone;
        this.skin && this.sortPathConstraintAttachment(this.skin, i, s),
          this.data.defaultSkin &&
            this.data.defaultSkin != this.skin &&
            this.sortPathConstraintAttachment(this.data.defaultSkin, i, s);
        for (let r = 0, o = this.data.skins.length; r < o; r++)
          this.sortPathConstraintAttachment(this.data.skins[r], i, s);
        let n = t.getAttachment();
        n instanceof Ze && this.sortPathConstraintAttachmentWith(n, s);
        let d = e.bones,
          a = d.length;
        for (let r = 0; r < a; r++) this.sortBone(d[r]);
        this._updateCache.push(e);
        for (let r = 0; r < a; r++) this.sortReset(d[r].children);
        for (let r = 0; r < a; r++) d[r].sorted = !0;
      }
      sortTransformConstraint(e) {
        if (
          ((e.active =
            e.target.isActive() &&
            (!e.data.skinRequired ||
              (this.skin && P.contains(this.skin.constraints, e.data, !0)))),
          !e.active)
        )
          return;
        this.sortBone(e.target);
        let t = e.bones,
          i = t.length;
        if (e.data.local)
          for (let s = 0; s < i; s++) {
            let n = t[s];
            this.sortBone(n.parent), this.sortBone(n);
          }
        else for (let s = 0; s < i; s++) this.sortBone(t[s]);
        this._updateCache.push(e);
        for (let s = 0; s < i; s++) this.sortReset(t[s].children);
        for (let s = 0; s < i; s++) t[s].sorted = !0;
      }
      sortPathConstraintAttachment(e, t, i) {
        let s = e.attachments[t];
        if (s) for (let n in s) this.sortPathConstraintAttachmentWith(s[n], i);
      }
      sortPathConstraintAttachmentWith(e, t) {
        if (!(e instanceof Ze)) return;
        let i = e.bones;
        if (!i) this.sortBone(t);
        else {
          let s = this.bones;
          for (let n = 0, d = i.length; n < d; ) {
            let a = i[n++];
            for (a += n; n < a; ) this.sortBone(s[i[n++]]);
          }
        }
      }
      sortPhysicsConstraint(e) {
        const t = e.bone;
        (e.active =
          t.active &&
          (!e.data.skinRequired ||
            (this.skin != null &&
              P.contains(this.skin.constraints, e.data, !0)))),
          e.active &&
            (this.sortBone(t),
            this._updateCache.push(e),
            this.sortReset(t.children),
            (t.sorted = !0));
      }
      sortBone(e) {
        if (!e || e.sorted) return;
        let t = e.parent;
        t && this.sortBone(t), (e.sorted = !0), this._updateCache.push(e);
      }
      sortReset(e) {
        for (let t = 0, i = e.length; t < i; t++) {
          let s = e[t];
          s.active && (s.sorted && this.sortReset(s.children), (s.sorted = !1));
        }
      }
      updateWorldTransform(e) {
        if (e == null) throw new Error("physics is undefined");
        let t = this.bones;
        for (let s = 0, n = t.length; s < n; s++) {
          let d = t[s];
          (d.ax = d.x),
            (d.ay = d.y),
            (d.arotation = d.rotation),
            (d.ascaleX = d.scaleX),
            (d.ascaleY = d.scaleY),
            (d.ashearX = d.shearX),
            (d.ashearY = d.shearY);
        }
        let i = this._updateCache;
        for (let s = 0, n = i.length; s < n; s++) i[s].update(e);
      }
      updateWorldTransformWith(e, t) {
        if (!t) throw new Error("parent cannot be null.");
        let i = this.bones;
        for (let g = 1, b = i.length; g < b; g++) {
          let x = i[g];
          (x.ax = x.x),
            (x.ay = x.y),
            (x.arotation = x.rotation),
            (x.ascaleX = x.scaleX),
            (x.ascaleY = x.scaleY),
            (x.ashearX = x.shearX),
            (x.ashearY = x.shearY);
        }
        let s = this.getRootBone();
        if (!s) throw new Error("Root bone must not be null.");
        let n = t.a,
          d = t.b,
          a = t.c,
          r = t.d;
        (s.worldX = n * this.x + d * this.y + t.worldX),
          (s.worldY = a * this.x + r * this.y + t.worldY);
        const o = (s.rotation + s.shearX) * X.degRad,
          l = (s.rotation + 90 + s.shearY) * X.degRad,
          h = Math.cos(o) * s.scaleX,
          u = Math.cos(l) * s.scaleY,
          c = Math.sin(o) * s.scaleX,
          f = Math.sin(l) * s.scaleY;
        (s.a = (n * h + d * c) * this.scaleX),
          (s.b = (n * u + d * f) * this.scaleX),
          (s.c = (a * h + r * c) * this.scaleY),
          (s.d = (a * u + r * f) * this.scaleY);
        let m = this._updateCache;
        for (let g = 0, b = m.length; g < b; g++) {
          let x = m[g];
          x != s && x.update(e);
        }
      }
      setToSetupPose() {
        this.setBonesToSetupPose(), this.setSlotsToSetupPose();
      }
      setBonesToSetupPose() {
        for (const e of this.bones) e.setToSetupPose();
        for (const e of this.ikConstraints) e.setToSetupPose();
        for (const e of this.transformConstraints) e.setToSetupPose();
        for (const e of this.pathConstraints) e.setToSetupPose();
        for (const e of this.physicsConstraints) e.setToSetupPose();
      }
      setSlotsToSetupPose() {
        let e = this.slots;
        P.arrayCopy(e, 0, this.drawOrder, 0, e.length);
        for (let t = 0, i = e.length; t < i; t++) e[t].setToSetupPose();
      }
      getRootBone() {
        return this.bones.length == 0 ? null : this.bones[0];
      }
      findBone(e) {
        if (!e) throw new Error("boneName cannot be null.");
        let t = this.bones;
        for (let i = 0, s = t.length; i < s; i++) {
          let n = t[i];
          if (n.data.name == e) return n;
        }
        return null;
      }
      findSlot(e) {
        if (!e) throw new Error("slotName cannot be null.");
        let t = this.slots;
        for (let i = 0, s = t.length; i < s; i++) {
          let n = t[i];
          if (n.data.name == e) return n;
        }
        return null;
      }
      setSkinByName(e) {
        let t = this.data.findSkin(e);
        if (!t) throw new Error("Skin not found: " + e);
        this.setSkin(t);
      }
      setSkin(e) {
        if (e != this.skin) {
          if (e)
            if (this.skin) e.attachAll(this, this.skin);
            else {
              let t = this.slots;
              for (let i = 0, s = t.length; i < s; i++) {
                let n = t[i],
                  d = n.data.attachmentName;
                if (d) {
                  let a = e.getAttachment(i, d);
                  a && n.setAttachment(a);
                }
              }
            }
          (this.skin = e), this.updateCache();
        }
      }
      getAttachmentByName(e, t) {
        let i = this.data.findSlot(e);
        if (!i) throw new Error(`Can't find slot with name ${e}`);
        return this.getAttachment(i.index, t);
      }
      getAttachment(e, t) {
        if (!t) throw new Error("attachmentName cannot be null.");
        if (this.skin) {
          let i = this.skin.getAttachment(e, t);
          if (i) return i;
        }
        return this.data.defaultSkin
          ? this.data.defaultSkin.getAttachment(e, t)
          : null;
      }
      setAttachment(e, t) {
        if (!e) throw new Error("slotName cannot be null.");
        let i = this.slots;
        for (let s = 0, n = i.length; s < n; s++) {
          let d = i[s];
          if (d.data.name == e) {
            let a = null;
            if (t && ((a = this.getAttachment(s, t)), !a))
              throw new Error(
                "Attachment not found: " + t + ", for slot: " + e
              );
            d.setAttachment(a);
            return;
          }
        }
        throw new Error("Slot not found: " + e);
      }
      findIkConstraint(e) {
        if (!e) throw new Error("constraintName cannot be null.");
        return this.ikConstraints.find((t) => t.data.name == e) ?? null;
      }
      findTransformConstraint(e) {
        if (!e) throw new Error("constraintName cannot be null.");
        return this.transformConstraints.find((t) => t.data.name == e) ?? null;
      }
      findPathConstraint(e) {
        if (!e) throw new Error("constraintName cannot be null.");
        return this.pathConstraints.find((t) => t.data.name == e) ?? null;
      }
      findPhysicsConstraint(e) {
        if (e == null) throw new Error("constraintName cannot be null.");
        return this.physicsConstraints.find((t) => t.data.name == e) ?? null;
      }
      getBoundsRect(e) {
        let t = new Me(),
          i = new Me();
        return (
          this.getBounds(t, i, void 0, e),
          { x: t.x, y: t.y, width: i.x, height: i.y }
        );
      }
      getBounds(e, t, i = new Array(2), s = null) {
        if (!e) throw new Error("offset cannot be null.");
        if (!t) throw new Error("size cannot be null.");
        let n = this.drawOrder,
          d = Number.POSITIVE_INFINITY,
          a = Number.POSITIVE_INFINITY,
          r = Number.NEGATIVE_INFINITY,
          o = Number.NEGATIVE_INFINITY;
        for (let l = 0, h = n.length; l < h; l++) {
          let u = n[l];
          if (!u.bone.active) continue;
          let c = 0,
            f = null,
            m = null,
            g = u.getAttachment();
          if (g instanceof le)
            (c = 8),
              (f = P.setArraySize(i, c, 0)),
              g.computeWorldVertices(u, f, 0, 2),
              (m = Vi.quadTriangles);
          else if (g instanceof Ue) {
            let b = g;
            (c = b.worldVerticesLength),
              (f = P.setArraySize(i, c, 0)),
              b.computeWorldVertices(u, 0, c, f, 0, 2),
              (m = b.triangles);
          } else if (g instanceof tt && s != null) {
            s.clipStart(u, g);
            continue;
          }
          if (f && m) {
            s != null &&
              s.isClipping() &&
              (s.clipTriangles(f, m, m.length),
              (f = s.clippedVertices),
              (c = s.clippedVertices.length));
            for (let b = 0, x = f.length; b < x; b += 2) {
              let p = f[b],
                w = f[b + 1];
              (d = Math.min(d, p)),
                (a = Math.min(a, w)),
                (r = Math.max(r, p)),
                (o = Math.max(o, w));
            }
          }
          s?.clipEndWithSlot(u);
        }
        s?.clipEnd(), e.set(d, a), t.set(r - d, o - a);
      }
      update(e) {
        this.time += e;
      }
      physicsTranslate(e, t) {
        const i = this.physicsConstraints;
        for (let s = 0, n = i.length; s < n; s++) i[s].translate(e, t);
      }
      physicsRotate(e, t, i) {
        const s = this.physicsConstraints;
        for (let n = 0, d = s.length; n < d; n++) s[n].rotate(e, t, i);
      }
    },
    ut = Vi;
  B(ut, "quadTriangles", [0, 1, 2, 2, 3, 0]), B(ut, "yDown", !1);
  var Es = ((e) => (
      (e[(e.none = 0)] = "none"),
      (e[(e.reset = 1)] = "reset"),
      (e[(e.update = 2)] = "update"),
      (e[(e.pose = 3)] = "pose"),
      e
    ))(Es || {}),
    Is = class extends ct {
      _bone = null;
      set bone(e) {
        this._bone = e;
      }
      get bone() {
        if (this._bone) return this._bone;
        throw new Error("BoneData not set.");
      }
      x = 0;
      y = 0;
      rotate = 0;
      scaleX = 0;
      shearX = 0;
      limit = 0;
      step = 0;
      inertia = 0;
      strength = 0;
      damping = 0;
      massInverse = 0;
      wind = 0;
      gravity = 0;
      mix = 0;
      inertiaGlobal = !1;
      strengthGlobal = !1;
      dampingGlobal = !1;
      massGlobal = !1;
      windGlobal = !1;
      gravityGlobal = !1;
      mixGlobal = !1;
      constructor(e) {
        super(e, 0, !1);
      }
    },
    Oi = class {
      name = null;
      bones = new Array();
      slots = new Array();
      skins = new Array();
      defaultSkin = null;
      events = new Array();
      animations = new Array();
      ikConstraints = new Array();
      transformConstraints = new Array();
      pathConstraints = new Array();
      physicsConstraints = new Array();
      x = 0;
      y = 0;
      width = 0;
      height = 0;
      referenceScale = 100;
      version = null;
      hash = null;
      fps = 0;
      imagesPath = null;
      audioPath = null;
      findBone(e) {
        if (!e) throw new Error("boneName cannot be null.");
        let t = this.bones;
        for (let i = 0, s = t.length; i < s; i++) {
          let n = t[i];
          if (n.name == e) return n;
        }
        return null;
      }
      findSlot(e) {
        if (!e) throw new Error("slotName cannot be null.");
        let t = this.slots;
        for (let i = 0, s = t.length; i < s; i++) {
          let n = t[i];
          if (n.name == e) return n;
        }
        return null;
      }
      findSkin(e) {
        if (!e) throw new Error("skinName cannot be null.");
        let t = this.skins;
        for (let i = 0, s = t.length; i < s; i++) {
          let n = t[i];
          if (n.name == e) return n;
        }
        return null;
      }
      findEvent(e) {
        if (!e) throw new Error("eventDataName cannot be null.");
        let t = this.events;
        for (let i = 0, s = t.length; i < s; i++) {
          let n = t[i];
          if (n.name == e) return n;
        }
        return null;
      }
      findAnimation(e) {
        if (!e) throw new Error("animationName cannot be null.");
        let t = this.animations;
        for (let i = 0, s = t.length; i < s; i++) {
          let n = t[i];
          if (n.name == e) return n;
        }
        return null;
      }
      findIkConstraint(e) {
        if (!e) throw new Error("constraintName cannot be null.");
        const t = this.ikConstraints;
        for (let i = 0, s = t.length; i < s; i++) {
          const n = t[i];
          if (n.name == e) return n;
        }
        return null;
      }
      findTransformConstraint(e) {
        if (!e) throw new Error("constraintName cannot be null.");
        const t = this.transformConstraints;
        for (let i = 0, s = t.length; i < s; i++) {
          const n = t[i];
          if (n.name == e) return n;
        }
        return null;
      }
      findPathConstraint(e) {
        if (!e) throw new Error("constraintName cannot be null.");
        const t = this.pathConstraints;
        for (let i = 0, s = t.length; i < s; i++) {
          const n = t[i];
          if (n.name == e) return n;
        }
        return null;
      }
      findPhysicsConstraint(e) {
        if (!e) throw new Error("constraintName cannot be null.");
        const t = this.physicsConstraints;
        for (let i = 0, s = t.length; i < s; i++) {
          const n = t[i];
          if (n.name == e) return n;
        }
        return null;
      }
    },
    Di = class {
      constructor(e = 0, t, i) {
        (this.slotIndex = e), (this.name = t), (this.attachment = i);
      }
    },
    Yt = class {
      name;
      attachments = new Array();
      bones = Array();
      constraints = new Array();
      color = new V(0.99607843, 0.61960787, 0.30980393, 1);
      constructor(e) {
        if (!e) throw new Error("name cannot be null.");
        this.name = e;
      }
      setAttachment(e, t, i) {
        if (!i) throw new Error("attachment cannot be null.");
        let s = this.attachments;
        e >= s.length && (s.length = e + 1), s[e] || (s[e] = {}), (s[e][t] = i);
      }
      addSkin(e) {
        for (let s = 0; s < e.bones.length; s++) {
          let n = e.bones[s],
            d = !1;
          for (let a = 0; a < this.bones.length; a++)
            if (this.bones[a] == n) {
              d = !0;
              break;
            }
          d || this.bones.push(n);
        }
        for (let s = 0; s < e.constraints.length; s++) {
          let n = e.constraints[s],
            d = !1;
          for (let a = 0; a < this.constraints.length; a++)
            if (this.constraints[a] == n) {
              d = !0;
              break;
            }
          d || this.constraints.push(n);
        }
        let t = e.getAttachments();
        for (let s = 0; s < t.length; s++) {
          var i = t[s];
          this.setAttachment(i.slotIndex, i.name, i.attachment);
        }
      }
      copySkin(e) {
        for (let s = 0; s < e.bones.length; s++) {
          let n = e.bones[s],
            d = !1;
          for (let a = 0; a < this.bones.length; a++)
            if (this.bones[a] == n) {
              d = !0;
              break;
            }
          d || this.bones.push(n);
        }
        for (let s = 0; s < e.constraints.length; s++) {
          let n = e.constraints[s],
            d = !1;
          for (let a = 0; a < this.constraints.length; a++)
            if (this.constraints[a] == n) {
              d = !0;
              break;
            }
          d || this.constraints.push(n);
        }
        let t = e.getAttachments();
        for (let s = 0; s < t.length; s++) {
          var i = t[s];
          i.attachment &&
            (i.attachment instanceof Ue
              ? ((i.attachment = i.attachment.newLinkedMesh()),
                this.setAttachment(i.slotIndex, i.name, i.attachment))
              : ((i.attachment = i.attachment.copy()),
                this.setAttachment(i.slotIndex, i.name, i.attachment)));
        }
      }
      getAttachment(e, t) {
        let i = this.attachments[e];
        return i ? i[t] : null;
      }
      removeAttachment(e, t) {
        let i = this.attachments[e];
        i && delete i[t];
      }
      getAttachments() {
        let e = new Array();
        for (var t = 0; t < this.attachments.length; t++) {
          let i = this.attachments[t];
          if (i)
            for (let s in i) {
              let n = i[s];
              n && e.push(new Di(t, s, n));
            }
        }
        return e;
      }
      getAttachmentsForSlot(e, t) {
        let i = this.attachments[e];
        if (i)
          for (let s in i) {
            let n = i[s];
            n && t.push(new Di(e, s, n));
          }
      }
      clear() {
        (this.attachments.length = 0),
          (this.bones.length = 0),
          (this.constraints.length = 0);
      }
      attachAll(e, t) {
        let i = 0;
        for (let s = 0; s < e.slots.length; s++) {
          let n = e.slots[s],
            d = n.getAttachment();
          if (d && i < t.attachments.length) {
            let a = t.attachments[i];
            for (let r in a) {
              let o = a[r];
              if (d == o) {
                let l = this.getAttachment(i, r);
                l && n.setAttachment(l);
                break;
              }
            }
          }
          i++;
        }
      }
    },
    Ni = class {
      index = 0;
      name;
      boneData;
      color = new V(1, 1, 1, 1);
      darkColor = null;
      attachmentName = null;
      blendMode = Lt.Normal;
      visible = !0;
      constructor(e, t, i) {
        if (e < 0) throw new Error("index must be >= 0.");
        if (!t) throw new Error("name cannot be null.");
        if (!i) throw new Error("boneData cannot be null.");
        (this.index = e), (this.name = t), (this.boneData = i);
      }
    },
    Lt = ((e) => (
      (e[(e.Normal = 0)] = "Normal"),
      (e[(e.Additive = 1)] = "Additive"),
      (e[(e.Multiply = 2)] = "Multiply"),
      (e[(e.Screen = 3)] = "Screen"),
      e
    ))(Lt || {}),
    Ui = class extends ct {
      bones = new Array();
      _target = null;
      set target(e) {
        this._target = e;
      }
      get target() {
        if (this._target) return this._target;
        throw new Error("BoneData not set.");
      }
      mixRotate = 0;
      mixX = 0;
      mixY = 0;
      mixScaleX = 0;
      mixScaleY = 0;
      mixShearY = 0;
      offsetRotation = 0;
      offsetX = 0;
      offsetY = 0;
      offsetScaleX = 0;
      offsetScaleY = 0;
      offsetShearY = 0;
      relative = !1;
      local = !1;
      constructor(e) {
        super(e, 0, !1);
      }
    },
    Ms = class {
      scale = 1;
      attachmentLoader;
      linkedMeshes = new Array();
      constructor(e) {
        this.attachmentLoader = e;
      }
      readSkeletonData(e) {
        let t = this.scale,
          i = new Oi();
        i.name = "";
        let s = new Rs(e),
          n = s.readInt32(),
          d = s.readInt32();
        (i.hash = d == 0 && n == 0 ? null : d.toString(16) + n.toString(16)),
          (i.version = s.readString()),
          (i.x = s.readFloat()),
          (i.y = s.readFloat()),
          (i.width = s.readFloat()),
          (i.height = s.readFloat()),
          (i.referenceScale = s.readFloat() * t);
        let a = s.readBoolean();
        a &&
          ((i.fps = s.readFloat()),
          (i.imagesPath = s.readString()),
          (i.audioPath = s.readString()));
        let r = 0;
        r = s.readInt(!0);
        for (let l = 0; l < r; l++) {
          let h = s.readString();
          if (!h) throw new Error("String in string table must not be null.");
          s.strings.push(h);
        }
        r = s.readInt(!0);
        for (let l = 0; l < r; l++) {
          let h = s.readString();
          if (!h) throw new Error("Bone name must not be null.");
          let u = l == 0 ? null : i.bones[s.readInt(!0)],
            c = new Ri(l, h, u);
          (c.rotation = s.readFloat()),
            (c.x = s.readFloat() * t),
            (c.y = s.readFloat() * t),
            (c.scaleX = s.readFloat()),
            (c.scaleY = s.readFloat()),
            (c.shearX = s.readFloat()),
            (c.shearY = s.readFloat()),
            (c.length = s.readFloat() * t),
            (c.inherit = s.readByte()),
            (c.skinRequired = s.readBoolean()),
            a &&
              (V.rgba8888ToColor(c.color, s.readInt32()),
              (c.icon = s.readString() ?? void 0),
              (c.visible = s.readBoolean())),
            i.bones.push(c);
        }
        r = s.readInt(!0);
        for (let l = 0; l < r; l++) {
          let h = s.readString();
          if (!h) throw new Error("Slot name must not be null.");
          let u = i.bones[s.readInt(!0)],
            c = new Ni(l, h, u);
          V.rgba8888ToColor(c.color, s.readInt32());
          let f = s.readInt32();
          f != -1 && V.rgb888ToColor((c.darkColor = new V()), f),
            (c.attachmentName = s.readStringRef()),
            (c.blendMode = s.readInt(!0)),
            a && (c.visible = s.readBoolean()),
            i.slots.push(c);
        }
        r = s.readInt(!0);
        for (let l = 0, h; l < r; l++) {
          let u = s.readString();
          if (!u) throw new Error("IK constraint data name must not be null.");
          let c = new Pi(u);
          (c.order = s.readInt(!0)), (h = s.readInt(!0));
          for (let m = 0; m < h; m++) c.bones.push(i.bones[s.readInt(!0)]);
          c.target = i.bones[s.readInt(!0)];
          let f = s.readByte();
          (c.skinRequired = (f & 1) != 0),
            (c.bendDirection = f & 2 ? 1 : -1),
            (c.compress = (f & 4) != 0),
            (c.stretch = (f & 8) != 0),
            (c.uniform = (f & 16) != 0),
            f & 32 && (c.mix = f & 64 ? s.readFloat() : 1),
            f & 128 && (c.softness = s.readFloat() * t),
            i.ikConstraints.push(c);
        }
        r = s.readInt(!0);
        for (let l = 0, h; l < r; l++) {
          let u = s.readString();
          if (!u)
            throw new Error("Transform constraint data name must not be null.");
          let c = new Ui(u);
          (c.order = s.readInt(!0)), (h = s.readInt(!0));
          for (let m = 0; m < h; m++) c.bones.push(i.bones[s.readInt(!0)]);
          c.target = i.bones[s.readInt(!0)];
          let f = s.readByte();
          (c.skinRequired = (f & 1) != 0),
            (c.local = (f & 2) != 0),
            (c.relative = (f & 4) != 0),
            f & 8 && (c.offsetRotation = s.readFloat()),
            f & 16 && (c.offsetX = s.readFloat() * t),
            f & 32 && (c.offsetY = s.readFloat() * t),
            f & 64 && (c.offsetScaleX = s.readFloat()),
            f & 128 && (c.offsetScaleY = s.readFloat()),
            (f = s.readByte()),
            f & 1 && (c.offsetShearY = s.readFloat()),
            f & 2 && (c.mixRotate = s.readFloat()),
            f & 4 && (c.mixX = s.readFloat()),
            f & 8 && (c.mixY = s.readFloat()),
            f & 16 && (c.mixScaleX = s.readFloat()),
            f & 32 && (c.mixScaleY = s.readFloat()),
            f & 64 && (c.mixShearY = s.readFloat()),
            i.transformConstraints.push(c);
        }
        r = s.readInt(!0);
        for (let l = 0, h; l < r; l++) {
          let u = s.readString();
          if (!u)
            throw new Error("Path constraint data name must not be null.");
          let c = new Bi(u);
          (c.order = s.readInt(!0)),
            (c.skinRequired = s.readBoolean()),
            (h = s.readInt(!0));
          for (let m = 0; m < h; m++) c.bones.push(i.bones[s.readInt(!0)]);
          c.target = i.slots[s.readInt(!0)];
          const f = s.readByte();
          (c.positionMode = f & 1),
            (c.spacingMode = (f >> 1) & 3),
            (c.rotateMode = (f >> 3) & 3),
            f & 128 && (c.offsetRotation = s.readFloat()),
            (c.position = s.readFloat()),
            c.positionMode == 0 && (c.position *= t),
            (c.spacing = s.readFloat()),
            (c.spacingMode == 0 || c.spacingMode == 1) && (c.spacing *= t),
            (c.mixRotate = s.readFloat()),
            (c.mixX = s.readFloat()),
            (c.mixY = s.readFloat()),
            i.pathConstraints.push(c);
        }
        r = s.readInt(!0);
        for (let l = 0, h; l < r; l++) {
          const u = s.readString();
          if (!u)
            throw new Error("Physics constraint data name must not be null.");
          const c = new Is(u);
          (c.order = s.readInt(!0)), (c.bone = i.bones[s.readInt(!0)]);
          let f = s.readByte();
          (c.skinRequired = (f & 1) != 0),
            f & 2 && (c.x = s.readFloat()),
            f & 4 && (c.y = s.readFloat()),
            f & 8 && (c.rotate = s.readFloat()),
            f & 16 && (c.scaleX = s.readFloat()),
            f & 32 && (c.shearX = s.readFloat()),
            (c.limit = (f & 64 ? s.readFloat() : 5e3) * t),
            (c.step = 1 / s.readUnsignedByte()),
            (c.inertia = s.readFloat()),
            (c.strength = s.readFloat()),
            (c.damping = s.readFloat()),
            (c.massInverse = f & 128 ? s.readFloat() : 1),
            (c.wind = s.readFloat()),
            (c.gravity = s.readFloat()),
            (f = s.readByte()),
            f & 1 && (c.inertiaGlobal = !0),
            f & 2 && (c.strengthGlobal = !0),
            f & 4 && (c.dampingGlobal = !0),
            f & 8 && (c.massGlobal = !0),
            f & 16 && (c.windGlobal = !0),
            f & 32 && (c.gravityGlobal = !0),
            f & 64 && (c.mixGlobal = !0),
            (c.mix = f & 128 ? s.readFloat() : 1),
            i.physicsConstraints.push(c);
        }
        let o = this.readSkin(s, i, !0, a);
        o && ((i.defaultSkin = o), i.skins.push(o));
        {
          let l = i.skins.length;
          for (P.setArraySize(i.skins, (r = l + s.readInt(!0))); l < r; l++) {
            let h = this.readSkin(s, i, !1, a);
            if (!h)
              throw new Error("readSkin() should not have returned null.");
            i.skins[l] = h;
          }
        }
        r = this.linkedMeshes.length;
        for (let l = 0; l < r; l++) {
          let h = this.linkedMeshes[l];
          const u = i.skins[h.skinIndex];
          if (!h.parent) throw new Error("Linked mesh parent must not be null");
          let c = u.getAttachment(h.slotIndex, h.parent);
          if (!c) throw new Error(`Parent mesh not found: ${h.parent}`);
          (h.mesh.timelineAttachment = h.inheritTimeline ? c : h.mesh),
            h.mesh.setParentMesh(c),
            h.mesh.region != null && h.mesh.updateRegion();
        }
        (this.linkedMeshes.length = 0), (r = s.readInt(!0));
        for (let l = 0; l < r; l++) {
          let h = s.readString();
          if (!h) throw new Error("Event data name must not be null");
          let u = new Xi(h);
          (u.intValue = s.readInt(!1)),
            (u.floatValue = s.readFloat()),
            (u.stringValue = s.readString()),
            (u.audioPath = s.readString()),
            u.audioPath &&
              ((u.volume = s.readFloat()), (u.balance = s.readFloat())),
            i.events.push(u);
        }
        r = s.readInt(!0);
        for (let l = 0; l < r; l++) {
          let h = s.readString();
          if (!h) throw new Error("Animatio name must not be null.");
          i.animations.push(this.readAnimation(s, h, i));
        }
        return i;
      }
      readSkin(e, t, i, s) {
        let n = null,
          d = 0;
        if (i) {
          if (((d = e.readInt(!0)), d == 0)) return null;
          n = new Yt("default");
        } else {
          let a = e.readString();
          if (!a) throw new Error("Skin name must not be null.");
          (n = new Yt(a)),
            s && V.rgba8888ToColor(n.color, e.readInt32()),
            (n.bones.length = e.readInt(!0));
          for (let r = 0, o = n.bones.length; r < o; r++)
            n.bones[r] = t.bones[e.readInt(!0)];
          for (let r = 0, o = e.readInt(!0); r < o; r++)
            n.constraints.push(t.ikConstraints[e.readInt(!0)]);
          for (let r = 0, o = e.readInt(!0); r < o; r++)
            n.constraints.push(t.transformConstraints[e.readInt(!0)]);
          for (let r = 0, o = e.readInt(!0); r < o; r++)
            n.constraints.push(t.pathConstraints[e.readInt(!0)]);
          for (let r = 0, o = e.readInt(!0); r < o; r++)
            n.constraints.push(t.physicsConstraints[e.readInt(!0)]);
          d = e.readInt(!0);
        }
        for (let a = 0; a < d; a++) {
          let r = e.readInt(!0);
          for (let o = 0, l = e.readInt(!0); o < l; o++) {
            let h = e.readStringRef();
            if (!h) throw new Error("Attachment name must not be null");
            let u = this.readAttachment(e, t, n, r, h, s);
            u && n.setAttachment(r, h, u);
          }
        }
        return n;
      }
      readAttachment(e, t, i, s, n, d) {
        let a = this.scale,
          r = e.readByte();
        const o = r & 8 ? e.readStringRef() : n;
        if (!o) throw new Error("Attachment name must not be null");
        switch (r & 7) {
          case We.Region: {
            let l = r & 16 ? e.readStringRef() : null;
            const h = r & 32 ? e.readInt32() : 4294967295,
              u = r & 64 ? this.readSequence(e) : null;
            let c = r & 128 ? e.readFloat() : 0,
              f = e.readFloat(),
              m = e.readFloat(),
              g = e.readFloat(),
              b = e.readFloat(),
              x = e.readFloat(),
              p = e.readFloat();
            l || (l = o);
            let w = this.attachmentLoader.newRegionAttachment(i, o, l, u);
            return w
              ? ((w.path = l),
                (w.x = f * a),
                (w.y = m * a),
                (w.scaleX = g),
                (w.scaleY = b),
                (w.rotation = c),
                (w.width = x * a),
                (w.height = p * a),
                V.rgba8888ToColor(w.color, h),
                (w.sequence = u),
                u == null && w.updateRegion(),
                w)
              : null;
          }
          case We.BoundingBox: {
            let l = this.readVertices(e, (r & 16) != 0),
              h = d ? e.readInt32() : 0,
              u = this.attachmentLoader.newBoundingBoxAttachment(i, o);
            return u
              ? ((u.worldVerticesLength = l.length),
                (u.vertices = l.vertices),
                (u.bones = l.bones),
                d && V.rgba8888ToColor(u.color, h),
                u)
              : null;
          }
          case We.Mesh: {
            let l = r & 16 ? e.readStringRef() : o;
            const h = r & 32 ? e.readInt32() : 4294967295,
              u = r & 64 ? this.readSequence(e) : null,
              c = e.readInt(!0),
              f = this.readVertices(e, (r & 128) != 0),
              m = this.readFloatArray(e, f.length, 1),
              g = this.readShortArray(e, (f.length - c - 2) * 3);
            let b = [],
              x = 0,
              p = 0;
            d &&
              ((b = this.readShortArray(e, e.readInt(!0))),
              (x = e.readFloat()),
              (p = e.readFloat())),
              l || (l = o);
            let w = this.attachmentLoader.newMeshAttachment(i, o, l, u);
            return w
              ? ((w.path = l),
                V.rgba8888ToColor(w.color, h),
                (w.bones = f.bones),
                (w.vertices = f.vertices),
                (w.worldVerticesLength = f.length),
                (w.triangles = g),
                (w.regionUVs = m),
                u == null && w.updateRegion(),
                (w.hullLength = c << 1),
                (w.sequence = u),
                d && ((w.edges = b), (w.width = x * a), (w.height = p * a)),
                w)
              : null;
          }
          case We.LinkedMesh: {
            const l = r & 16 ? e.readStringRef() : o;
            if (l == null)
              throw new Error("Path of linked mesh must not be null");
            const h = r & 32 ? e.readInt32() : 4294967295,
              u = r & 64 ? this.readSequence(e) : null,
              c = (r & 128) != 0,
              f = e.readInt(!0),
              m = e.readStringRef();
            let g = 0,
              b = 0;
            d && ((g = e.readFloat()), (b = e.readFloat()));
            let x = this.attachmentLoader.newMeshAttachment(i, o, l, u);
            return x
              ? ((x.path = l),
                V.rgba8888ToColor(x.color, h),
                (x.sequence = u),
                d && ((x.width = g * a), (x.height = b * a)),
                this.linkedMeshes.push(new mr(x, f, s, m, c)),
                x)
              : null;
          }
          case We.Path: {
            const l = (r & 16) != 0,
              h = (r & 32) != 0,
              u = this.readVertices(e, (r & 64) != 0),
              c = P.newArray(u.length / 6, 0);
            for (let g = 0, b = c.length; g < b; g++) c[g] = e.readFloat() * a;
            const f = d ? e.readInt32() : 0,
              m = this.attachmentLoader.newPathAttachment(i, o);
            return m
              ? ((m.closed = l),
                (m.constantSpeed = h),
                (m.worldVerticesLength = u.length),
                (m.vertices = u.vertices),
                (m.bones = u.bones),
                (m.lengths = c),
                d && V.rgba8888ToColor(m.color, f),
                m)
              : null;
          }
          case We.Point: {
            const l = e.readFloat(),
              h = e.readFloat(),
              u = e.readFloat(),
              c = d ? e.readInt32() : 0,
              f = this.attachmentLoader.newPointAttachment(i, o);
            return f
              ? ((f.x = h * a),
                (f.y = u * a),
                (f.rotation = l),
                d && V.rgba8888ToColor(f.color, c),
                f)
              : null;
          }
          case We.Clipping: {
            const l = e.readInt(!0),
              h = this.readVertices(e, (r & 16) != 0);
            let u = d ? e.readInt32() : 0,
              c = this.attachmentLoader.newClippingAttachment(i, o);
            return c
              ? ((c.endSlot = t.slots[l]),
                (c.worldVerticesLength = h.length),
                (c.vertices = h.vertices),
                (c.bones = h.bones),
                d && V.rgba8888ToColor(c.color, u),
                c)
              : null;
          }
        }
        return null;
      }
      readSequence(e) {
        let t = new jt(e.readInt(!0));
        return (
          (t.start = e.readInt(!0)),
          (t.digits = e.readInt(!0)),
          (t.setupIndex = e.readInt(!0)),
          t
        );
      }
      readVertices(e, t) {
        const i = this.scale,
          s = e.readInt(!0),
          n = new gr();
        if (((n.length = s << 1), !t))
          return (n.vertices = this.readFloatArray(e, n.length, i)), n;
        let d = new Array(),
          a = new Array();
        for (let r = 0; r < s; r++) {
          let o = e.readInt(!0);
          a.push(o);
          for (let l = 0; l < o; l++)
            a.push(e.readInt(!0)),
              d.push(e.readFloat() * i),
              d.push(e.readFloat() * i),
              d.push(e.readFloat());
        }
        return (n.vertices = P.toFloatArray(d)), (n.bones = a), n;
      }
      readFloatArray(e, t, i) {
        let s = new Array(t);
        if (i == 1) for (let n = 0; n < t; n++) s[n] = e.readFloat();
        else for (let n = 0; n < t; n++) s[n] = e.readFloat() * i;
        return s;
      }
      readShortArray(e, t) {
        let i = new Array(t);
        for (let s = 0; s < t; s++) i[s] = e.readInt(!0);
        return i;
      }
      readAnimation(e, t, i) {
        e.readInt(!0);
        let s = new Array(),
          n = this.scale;
        for (let o = 0, l = e.readInt(!0); o < l; o++) {
          let h = e.readInt(!0);
          for (let u = 0, c = e.readInt(!0); u < c; u++) {
            let f = e.readByte(),
              m = e.readInt(!0),
              g = m - 1;
            switch (f) {
              case Er: {
                let b = new He(m, h);
                for (let x = 0; x < m; x++)
                  b.setFrame(x, e.readFloat(), e.readStringRef());
                s.push(b);
                break;
              }
              case Ir: {
                let b = e.readInt(!0),
                  x = new ni(m, b, h),
                  p = e.readFloat(),
                  w = e.readUnsignedByte() / 255,
                  y = e.readUnsignedByte() / 255,
                  v = e.readUnsignedByte() / 255,
                  A = e.readUnsignedByte() / 255;
                for (
                  let C = 0, T = 0;
                  x.setFrame(C, p, w, y, v, A), C != g;
                  C++
                ) {
                  let I = e.readFloat(),
                    M = e.readUnsignedByte() / 255,
                    F = e.readUnsignedByte() / 255,
                    L = e.readUnsignedByte() / 255,
                    k = e.readUnsignedByte() / 255;
                  switch (e.readByte()) {
                    case Xe:
                      x.setStepped(C);
                      break;
                    case Pe:
                      oe(e, x, T++, C, 0, p, I, w, M, 1),
                        oe(e, x, T++, C, 1, p, I, y, F, 1),
                        oe(e, x, T++, C, 2, p, I, v, L, 1),
                        oe(e, x, T++, C, 3, p, I, A, k, 1);
                  }
                  (p = I), (w = M), (y = F), (v = L), (A = k);
                }
                s.push(x);
                break;
              }
              case Mr: {
                let b = e.readInt(!0),
                  x = new ai(m, b, h),
                  p = e.readFloat(),
                  w = e.readUnsignedByte() / 255,
                  y = e.readUnsignedByte() / 255,
                  v = e.readUnsignedByte() / 255;
                for (let A = 0, C = 0; x.setFrame(A, p, w, y, v), A != g; A++) {
                  let T = e.readFloat(),
                    I = e.readUnsignedByte() / 255,
                    M = e.readUnsignedByte() / 255,
                    F = e.readUnsignedByte() / 255;
                  switch (e.readByte()) {
                    case Xe:
                      x.setStepped(A);
                      break;
                    case Pe:
                      oe(e, x, C++, A, 0, p, T, w, I, 1),
                        oe(e, x, C++, A, 1, p, T, y, M, 1),
                        oe(e, x, C++, A, 2, p, T, v, F, 1);
                  }
                  (p = T), (w = I), (y = M), (v = F);
                }
                s.push(x);
                break;
              }
              case Rr: {
                let b = e.readInt(!0),
                  x = new oi(m, b, h),
                  p = e.readFloat(),
                  w = e.readUnsignedByte() / 255,
                  y = e.readUnsignedByte() / 255,
                  v = e.readUnsignedByte() / 255,
                  A = e.readUnsignedByte() / 255,
                  C = e.readUnsignedByte() / 255,
                  T = e.readUnsignedByte() / 255,
                  I = e.readUnsignedByte() / 255;
                for (
                  let M = 0, F = 0;
                  x.setFrame(M, p, w, y, v, A, C, T, I), M != g;
                  M++
                ) {
                  let L = e.readFloat(),
                    k = e.readUnsignedByte() / 255,
                    R = e.readUnsignedByte() / 255,
                    Y = e.readUnsignedByte() / 255,
                    ie = e.readUnsignedByte() / 255,
                    se = e.readUnsignedByte() / 255,
                    te = e.readUnsignedByte() / 255,
                    de = e.readUnsignedByte() / 255;
                  switch (e.readByte()) {
                    case Xe:
                      x.setStepped(M);
                      break;
                    case Pe:
                      oe(e, x, F++, M, 0, p, L, w, k, 1),
                        oe(e, x, F++, M, 1, p, L, y, R, 1),
                        oe(e, x, F++, M, 2, p, L, v, Y, 1),
                        oe(e, x, F++, M, 3, p, L, A, ie, 1),
                        oe(e, x, F++, M, 4, p, L, C, se, 1),
                        oe(e, x, F++, M, 5, p, L, T, te, 1),
                        oe(e, x, F++, M, 6, p, L, I, de, 1);
                  }
                  (p = L),
                    (w = k),
                    (y = R),
                    (v = Y),
                    (A = ie),
                    (C = se),
                    (T = te),
                    (I = de);
                }
                s.push(x);
                break;
              }
              case Fr: {
                let b = e.readInt(!0),
                  x = new hi(m, b, h),
                  p = e.readFloat(),
                  w = e.readUnsignedByte() / 255,
                  y = e.readUnsignedByte() / 255,
                  v = e.readUnsignedByte() / 255,
                  A = e.readUnsignedByte() / 255,
                  C = e.readUnsignedByte() / 255,
                  T = e.readUnsignedByte() / 255;
                for (
                  let I = 0, M = 0;
                  x.setFrame(I, p, w, y, v, A, C, T), I != g;
                  I++
                ) {
                  let F = e.readFloat(),
                    L = e.readUnsignedByte() / 255,
                    k = e.readUnsignedByte() / 255,
                    R = e.readUnsignedByte() / 255,
                    Y = e.readUnsignedByte() / 255,
                    ie = e.readUnsignedByte() / 255,
                    se = e.readUnsignedByte() / 255;
                  switch (e.readByte()) {
                    case Xe:
                      x.setStepped(I);
                      break;
                    case Pe:
                      oe(e, x, M++, I, 0, p, F, w, L, 1),
                        oe(e, x, M++, I, 1, p, F, y, k, 1),
                        oe(e, x, M++, I, 2, p, F, v, R, 1),
                        oe(e, x, M++, I, 3, p, F, A, Y, 1),
                        oe(e, x, M++, I, 4, p, F, C, ie, 1),
                        oe(e, x, M++, I, 5, p, F, T, se, 1);
                  }
                  (p = F),
                    (w = L),
                    (y = k),
                    (v = R),
                    (A = Y),
                    (C = ie),
                    (T = se);
                }
                s.push(x);
                break;
              }
              case Yr: {
                let b = new li(m, e.readInt(!0), h),
                  x = e.readFloat(),
                  p = e.readUnsignedByte() / 255;
                for (let w = 0, y = 0; b.setFrame(w, x, p), w != g; w++) {
                  let v = e.readFloat(),
                    A = e.readUnsignedByte() / 255;
                  switch (e.readByte()) {
                    case Xe:
                      b.setStepped(w);
                      break;
                    case Pe:
                      oe(e, b, y++, w, 0, x, v, p, A, 1);
                  }
                  (x = v), (p = A);
                }
                s.push(b);
              }
            }
          }
        }
        for (let o = 0, l = e.readInt(!0); o < l; o++) {
          let h = e.readInt(!0);
          for (let u = 0, c = e.readInt(!0); u < c; u++) {
            let f = e.readByte(),
              m = e.readInt(!0);
            if (f == kr) {
              let b = new ri(m, h);
              for (let x = 0; x < m; x++)
                b.setFrame(x, e.readFloat(), e.readByte());
              s.push(b);
              continue;
            }
            let g = e.readInt(!0);
            switch (f) {
              case pr:
                s.push(xe(e, new ot(m, g, h), 1));
                break;
              case wr:
                s.push(_i(e, new Zt(m, g, h), n));
                break;
              case xr:
                s.push(xe(e, new Jt(m, g, h), n));
                break;
              case br:
                s.push(xe(e, new Kt(m, g, h), n));
                break;
              case vr:
                s.push(_i(e, new Qt(m, g, h), 1));
                break;
              case yr:
                s.push(xe(e, new $t(m, g, h), 1));
                break;
              case Ar:
                s.push(xe(e, new ei(m, g, h), 1));
                break;
              case Cr:
                s.push(_i(e, new ti(m, g, h), 1));
                break;
              case Sr:
                s.push(xe(e, new ii(m, g, h), 1));
                break;
              case Tr:
                s.push(xe(e, new si(m, g, h), 1));
            }
          }
        }
        for (let o = 0, l = e.readInt(!0); o < l; o++) {
          let h = e.readInt(!0),
            u = e.readInt(!0),
            c = u - 1,
            f = new ci(u, e.readInt(!0), h),
            m = e.readByte(),
            g = e.readFloat(),
            b = m & 1 ? (m & 2 ? e.readFloat() : 1) : 0,
            x = m & 4 ? e.readFloat() * n : 0;
          for (
            let p = 0, w = 0;
            f.setFrame(
              p,
              g,
              b,
              x,
              m & 8 ? 1 : -1,
              (m & 16) != 0,
              (m & 32) != 0
            ),
              p != c;
            p++
          ) {
            m = e.readByte();
            const y = e.readFloat(),
              v = m & 1 ? (m & 2 ? e.readFloat() : 1) : 0,
              A = m & 4 ? e.readFloat() * n : 0;
            m & 64
              ? f.setStepped(p)
              : m & 128 &&
                (oe(e, f, w++, p, 0, g, y, b, v, 1),
                oe(e, f, w++, p, 1, g, y, x, A, n)),
              (g = y),
              (b = v),
              (x = A);
          }
          s.push(f);
        }
        for (let o = 0, l = e.readInt(!0); o < l; o++) {
          let h = e.readInt(!0),
            u = e.readInt(!0),
            c = u - 1,
            f = new ui(u, e.readInt(!0), h),
            m = e.readFloat(),
            g = e.readFloat(),
            b = e.readFloat(),
            x = e.readFloat(),
            p = e.readFloat(),
            w = e.readFloat(),
            y = e.readFloat();
          for (
            let v = 0, A = 0;
            f.setFrame(v, m, g, b, x, p, w, y), v != c;
            v++
          ) {
            let C = e.readFloat(),
              T = e.readFloat(),
              I = e.readFloat(),
              M = e.readFloat(),
              F = e.readFloat(),
              L = e.readFloat(),
              k = e.readFloat();
            switch (e.readByte()) {
              case Xe:
                f.setStepped(v);
                break;
              case Pe:
                oe(e, f, A++, v, 0, m, C, g, T, 1),
                  oe(e, f, A++, v, 1, m, C, b, I, 1),
                  oe(e, f, A++, v, 2, m, C, x, M, 1),
                  oe(e, f, A++, v, 3, m, C, p, F, 1),
                  oe(e, f, A++, v, 4, m, C, w, L, 1),
                  oe(e, f, A++, v, 5, m, C, y, k, 1);
            }
            (m = C), (g = T), (b = I), (x = M), (p = F), (w = L), (y = k);
          }
          s.push(f);
        }
        for (let o = 0, l = e.readInt(!0); o < l; o++) {
          let h = e.readInt(!0),
            u = i.pathConstraints[h];
          for (let c = 0, f = e.readInt(!0); c < f; c++) {
            const m = e.readByte(),
              g = e.readInt(!0),
              b = e.readInt(!0);
            switch (m) {
              case Pr:
                s.push(xe(e, new fi(g, b, h), u.positionMode == 0 ? n : 1));
                break;
              case Br:
                s.push(
                  xe(
                    e,
                    new mi(g, b, h),
                    u.spacingMode == 0 || u.spacingMode == 1 ? n : 1
                  )
                );
                break;
              case Vr:
                let x = new gi(g, b, h),
                  p = e.readFloat(),
                  w = e.readFloat(),
                  y = e.readFloat(),
                  v = e.readFloat();
                for (
                  let A = 0, C = 0, T = x.getFrameCount() - 1;
                  x.setFrame(A, p, w, y, v), A != T;
                  A++
                ) {
                  let I = e.readFloat(),
                    M = e.readFloat(),
                    F = e.readFloat(),
                    L = e.readFloat();
                  switch (e.readByte()) {
                    case Xe:
                      x.setStepped(A);
                      break;
                    case Pe:
                      oe(e, x, C++, A, 0, p, I, w, M, 1),
                        oe(e, x, C++, A, 1, p, I, y, F, 1),
                        oe(e, x, C++, A, 2, p, I, v, L, 1);
                  }
                  (p = I), (w = M), (y = F), (v = L);
                }
                s.push(x);
            }
          }
        }
        for (let o = 0, l = e.readInt(!0); o < l; o++) {
          const h = e.readInt(!0) - 1;
          for (let u = 0, c = e.readInt(!0); u < c; u++) {
            const f = e.readByte(),
              m = e.readInt(!0);
            if (f == qr) {
              const b = new Ct(m, h);
              for (let x = 0; x < m; x++) b.setFrame(x, e.readFloat());
              s.push(b);
              continue;
            }
            const g = e.readInt(!0);
            switch (f) {
              case Or:
                s.push(xe(e, new pi(m, g, h), 1));
                break;
              case Dr:
                s.push(xe(e, new wi(m, g, h), 1));
                break;
              case Nr:
                s.push(xe(e, new xi(m, g, h), 1));
                break;
              case Ur:
                s.push(xe(e, new bi(m, g, h), 1));
                break;
              case _r:
                s.push(xe(e, new vi(m, g, h), 1));
                break;
              case Wr:
                s.push(xe(e, new yi(m, g, h), 1));
                break;
              case zr:
                s.push(xe(e, new Ai(m, g, h), 1));
            }
          }
        }
        for (let o = 0, l = e.readInt(!0); o < l; o++) {
          let h = i.skins[e.readInt(!0)];
          for (let u = 0, c = e.readInt(!0); u < c; u++) {
            let f = e.readInt(!0);
            for (let m = 0, g = e.readInt(!0); m < g; m++) {
              let b = e.readStringRef();
              if (!b) throw new Error("attachmentName must not be null.");
              let x = h.getAttachment(f, b),
                p = e.readByte(),
                w = e.readInt(!0),
                y = w - 1;
              switch (p) {
                case Lr: {
                  let v = x,
                    A = v.bones,
                    C = v.vertices,
                    T = A ? (C.length / 3) * 2 : C.length,
                    I = e.readInt(!0),
                    M = new di(w, I, f, v),
                    F = e.readFloat();
                  for (let L = 0, k = 0; ; L++) {
                    let R,
                      Y = e.readInt(!0);
                    if (Y == 0) R = A ? P.newFloatArray(T) : C;
                    else {
                      R = P.newFloatArray(T);
                      let se = e.readInt(!0);
                      if (((Y += se), n == 1))
                        for (let te = se; te < Y; te++) R[te] = e.readFloat();
                      else
                        for (let te = se; te < Y; te++)
                          R[te] = e.readFloat() * n;
                      if (!A)
                        for (let te = 0, de = R.length; te < de; te++)
                          R[te] += C[te];
                    }
                    if ((M.setFrame(L, F, R), L == y)) break;
                    let ie = e.readFloat();
                    switch (e.readByte()) {
                      case Xe:
                        M.setStepped(L);
                        break;
                      case Pe:
                        oe(e, M, k++, L, 0, F, ie, 0, 1, 1);
                    }
                    F = ie;
                  }
                  s.push(M);
                  break;
                }
                case Xr: {
                  let v = new et(w, f, x);
                  for (let A = 0; A < w; A++) {
                    let C = e.readFloat(),
                      T = e.readInt32();
                    v.setFrame(A, C, as[T & 15], T >> 4, e.readFloat());
                  }
                  s.push(v);
                  break;
                }
              }
            }
          }
        }
        let d = e.readInt(!0);
        if (d > 0) {
          let o = new je(d),
            l = i.slots.length;
          for (let h = 0; h < d; h++) {
            let u = e.readFloat(),
              c = e.readInt(!0),
              f = P.newArray(l, 0);
            for (let x = l - 1; x >= 0; x--) f[x] = -1;
            let m = P.newArray(l - c, 0),
              g = 0,
              b = 0;
            for (let x = 0; x < c; x++) {
              let p = e.readInt(!0);
              for (; g != p; ) m[b++] = g++;
              f[g + e.readInt(!0)] = g++;
            }
            for (; g < l; ) m[b++] = g++;
            for (let x = l - 1; x >= 0; x--) f[x] == -1 && (f[x] = m[--b]);
            o.setFrame(h, u, f);
          }
          s.push(o);
        }
        let a = e.readInt(!0);
        if (a > 0) {
          let o = new ht(a);
          for (let l = 0; l < a; l++) {
            let h = e.readFloat(),
              u = i.events[e.readInt(!0)],
              c = new Li(h, u);
            (c.intValue = e.readInt(!1)),
              (c.floatValue = e.readFloat()),
              (c.stringValue = e.readString()),
              c.stringValue == null && (c.stringValue = u.stringValue),
              c.data.audioPath &&
                ((c.volume = e.readFloat()), (c.balance = e.readFloat())),
              o.setFrame(l, c);
          }
          s.push(o);
        }
        let r = 0;
        for (let o = 0, l = s.length; o < l; o++)
          r = Math.max(r, s[o].getDuration());
        return new yt(t, s, r);
      }
    },
    Rs = class {
      constructor(
        e,
        t = new Array(),
        i = 0,
        s = new DataView(e instanceof ArrayBuffer ? e : e.buffer)
      ) {
        (this.strings = t), (this.index = i), (this.buffer = s);
      }
      readByte() {
        return this.buffer.getInt8(this.index++);
      }
      readUnsignedByte() {
        return this.buffer.getUint8(this.index++);
      }
      readShort() {
        let e = this.buffer.getInt16(this.index);
        return (this.index += 2), e;
      }
      readInt32() {
        let e = this.buffer.getInt32(this.index);
        return (this.index += 4), e;
      }
      readInt(e) {
        let t = this.readByte(),
          i = t & 127;
        return (
          t & 128 &&
            ((t = this.readByte()),
            (i |= (t & 127) << 7),
            t & 128 &&
              ((t = this.readByte()),
              (i |= (t & 127) << 14),
              t & 128 &&
                ((t = this.readByte()),
                (i |= (t & 127) << 21),
                t & 128 && ((t = this.readByte()), (i |= (t & 127) << 28))))),
          e ? i : (i >>> 1) ^ -(i & 1)
        );
      }
      readStringRef() {
        let e = this.readInt(!0);
        return e == 0 ? null : this.strings[e - 1];
      }
      readString() {
        let e = this.readInt(!0);
        switch (e) {
          case 0:
            return null;
          case 1:
            return "";
        }
        e--;
        let t = "",
          i = 0;
        for (let s = 0; s < e; ) {
          let n = this.readUnsignedByte();
          switch (n >> 4) {
            case 12:
            case 13:
              (t += String.fromCharCode(
                ((n & 31) << 6) | (this.readByte() & 63)
              )),
                (s += 2);
              break;
            case 14:
              (t += String.fromCharCode(
                ((n & 15) << 12) |
                  ((this.readByte() & 63) << 6) |
                  (this.readByte() & 63)
              )),
                (s += 3);
              break;
            default:
              (t += String.fromCharCode(n)), s++;
          }
        }
        return t;
      }
      readFloat() {
        let e = this.buffer.getFloat32(this.index);
        return (this.index += 4), e;
      }
      readBoolean() {
        return this.readByte() != 0;
      }
    },
    mr = class {
      parent;
      skinIndex;
      slotIndex;
      mesh;
      inheritTimeline;
      constructor(e, t, i, s, n) {
        (this.mesh = e),
          (this.skinIndex = t),
          (this.slotIndex = i),
          (this.parent = s),
          (this.inheritTimeline = n);
      }
    },
    gr = class {
      constructor(e = null, t = null, i = 0) {
        (this.bones = e), (this.vertices = t), (this.length = i);
      }
    },
    We = ((e) => (
      (e[(e.Region = 0)] = "Region"),
      (e[(e.BoundingBox = 1)] = "BoundingBox"),
      (e[(e.Mesh = 2)] = "Mesh"),
      (e[(e.LinkedMesh = 3)] = "LinkedMesh"),
      (e[(e.Path = 4)] = "Path"),
      (e[(e.Point = 5)] = "Point"),
      (e[(e.Clipping = 6)] = "Clipping"),
      e
    ))(We || {});
  function xe(e, t, i) {
    let s = e.readFloat(),
      n = e.readFloat() * i;
    for (
      let d = 0, a = 0, r = t.getFrameCount() - 1;
      t.setFrame(d, s, n), d != r;
      d++
    ) {
      let o = e.readFloat(),
        l = e.readFloat() * i;
      switch (e.readByte()) {
        case Xe:
          t.setStepped(d);
          break;
        case Pe:
          oe(e, t, a++, d, 0, s, o, n, l, i);
      }
      (s = o), (n = l);
    }
    return t;
  }
  function _i(e, t, i) {
    let s = e.readFloat(),
      n = e.readFloat() * i,
      d = e.readFloat() * i;
    for (
      let a = 0, r = 0, o = t.getFrameCount() - 1;
      t.setFrame(a, s, n, d), a != o;
      a++
    ) {
      let l = e.readFloat(),
        h = e.readFloat() * i,
        u = e.readFloat() * i;
      switch (e.readByte()) {
        case Xe:
          t.setStepped(a);
          break;
        case Pe:
          oe(e, t, r++, a, 0, s, l, n, h, i),
            oe(e, t, r++, a, 1, s, l, d, u, i);
      }
      (s = l), (n = h), (d = u);
    }
    return t;
  }
  function oe(e, t, i, s, n, d, a, r, o, l) {
    t.setBezier(
      i,
      s,
      n,
      d,
      r,
      e.readFloat(),
      e.readFloat() * l,
      e.readFloat(),
      e.readFloat() * l,
      a,
      o
    );
  }
  var pr = 0,
    wr = 1,
    xr = 2,
    br = 3,
    vr = 4,
    yr = 5,
    Ar = 6,
    Cr = 7,
    Sr = 8,
    Tr = 9,
    kr = 10,
    Er = 0,
    Ir = 1,
    Mr = 2,
    Rr = 3,
    Fr = 4,
    Yr = 5,
    Lr = 0,
    Xr = 1,
    Pr = 0,
    Br = 1,
    Vr = 2,
    Or = 0,
    Dr = 1,
    Nr = 2,
    Ur = 4,
    _r = 5,
    Wr = 6,
    zr = 7,
    qr = 8,
    Xe = 1,
    Pe = 2,
    Fs = class {
      minX = 0;
      minY = 0;
      maxX = 0;
      maxY = 0;
      boundingBoxes = new Array();
      polygons = new Array();
      polygonPool = new lt(() => P.newFloatArray(16));
      update(e, t) {
        if (!e) throw new Error("skeleton cannot be null.");
        let i = this.boundingBoxes,
          s = this.polygons,
          n = this.polygonPool,
          d = e.slots,
          a = d.length;
        (i.length = 0), n.freeAll(s), (s.length = 0);
        for (let r = 0; r < a; r++) {
          let o = d[r];
          if (!o.bone.active) continue;
          let l = o.getAttachment();
          if (l instanceof Et) {
            let h = l;
            i.push(h);
            let u = n.obtain();
            u.length != h.worldVerticesLength &&
              (u = P.newFloatArray(h.worldVerticesLength)),
              s.push(u),
              h.computeWorldVertices(o, 0, h.worldVerticesLength, u, 0, 2);
          }
        }
        t
          ? this.aabbCompute()
          : ((this.minX = Number.POSITIVE_INFINITY),
            (this.minY = Number.POSITIVE_INFINITY),
            (this.maxX = Number.NEGATIVE_INFINITY),
            (this.maxY = Number.NEGATIVE_INFINITY));
      }
      aabbCompute() {
        let e = Number.POSITIVE_INFINITY,
          t = Number.POSITIVE_INFINITY,
          i = Number.NEGATIVE_INFINITY,
          s = Number.NEGATIVE_INFINITY,
          n = this.polygons;
        for (let d = 0, a = n.length; d < a; d++) {
          let r = n[d],
            o = r;
          for (let l = 0, h = r.length; l < h; l += 2) {
            let u = o[l],
              c = o[l + 1];
            (e = Math.min(e, u)),
              (t = Math.min(t, c)),
              (i = Math.max(i, u)),
              (s = Math.max(s, c));
          }
        }
        (this.minX = e), (this.minY = t), (this.maxX = i), (this.maxY = s);
      }
      aabbContainsPoint(e, t) {
        return (
          e >= this.minX && e <= this.maxX && t >= this.minY && t <= this.maxY
        );
      }
      aabbIntersectsSegment(e, t, i, s) {
        let n = this.minX,
          d = this.minY,
          a = this.maxX,
          r = this.maxY;
        if (
          (e <= n && i <= n) ||
          (t <= d && s <= d) ||
          (e >= a && i >= a) ||
          (t >= r && s >= r)
        )
          return !1;
        let o = (s - t) / (i - e),
          l = o * (n - e) + t;
        if ((l > d && l < r) || ((l = o * (a - e) + t), l > d && l < r))
          return !0;
        let h = (d - t) / o + e;
        return (h > n && h < a) || ((h = (r - t) / o + e), h > n && h < a);
      }
      aabbIntersectsSkeleton(e) {
        return (
          this.minX < e.maxX &&
          this.maxX > e.minX &&
          this.minY < e.maxY &&
          this.maxY > e.minY
        );
      }
      containsPoint(e, t) {
        let i = this.polygons;
        for (let s = 0, n = i.length; s < n; s++)
          if (this.containsPointPolygon(i[s], e, t))
            return this.boundingBoxes[s];
        return null;
      }
      containsPointPolygon(e, t, i) {
        let s = e,
          n = e.length,
          d = n - 2,
          a = !1;
        for (let r = 0; r < n; r += 2) {
          let o = s[r + 1],
            l = s[d + 1];
          if ((o < i && l >= i) || (l < i && o >= i)) {
            let h = s[r];
            h + ((i - o) / (l - o)) * (s[d] - h) < t && (a = !a);
          }
          d = r;
        }
        return a;
      }
      intersectsSegment(e, t, i, s) {
        let n = this.polygons;
        for (let d = 0, a = n.length; d < a; d++)
          if (this.intersectsSegmentPolygon(n[d], e, t, i, s))
            return this.boundingBoxes[d];
        return null;
      }
      intersectsSegmentPolygon(e, t, i, s, n) {
        let d = e,
          a = e.length,
          r = t - s,
          o = i - n,
          l = t * n - i * s,
          h = d[a - 2],
          u = d[a - 1];
        for (let c = 0; c < a; c += 2) {
          let f = d[c],
            m = d[c + 1],
            g = h * m - u * f,
            b = h - f,
            x = u - m,
            p = r * x - o * b,
            w = (l * b - r * g) / p;
          if (
            ((w >= h && w <= f) || (w >= f && w <= h)) &&
            ((w >= t && w <= s) || (w >= s && w <= t))
          ) {
            let y = (l * x - o * g) / p;
            if (
              ((y >= u && y <= m) || (y >= m && y <= u)) &&
              ((y >= i && y <= n) || (y >= n && y <= i))
            )
              return !0;
          }
          (h = f), (u = m);
        }
        return !1;
      }
      getPolygon(e) {
        if (!e) throw new Error("boundingBox cannot be null.");
        let t = this.boundingBoxes.indexOf(e);
        return t == -1 ? null : this.polygons[t];
      }
      getWidth() {
        return this.maxX - this.minX;
      }
      getHeight() {
        return this.maxY - this.minY;
      }
    },
    Ce = class {
      convexPolygons = new Array();
      convexPolygonsIndices = new Array();
      indicesArray = new Array();
      isConcaveArray = new Array();
      triangles = new Array();
      polygonPool = new lt(() => new Array());
      polygonIndicesPool = new lt(() => new Array());
      triangulate(e) {
        let t = e,
          i = e.length >> 1,
          s = this.indicesArray;
        s.length = 0;
        for (let a = 0; a < i; a++) s[a] = a;
        let n = this.isConcaveArray;
        n.length = 0;
        for (let a = 0, r = i; a < r; ++a) n[a] = Ce.isConcave(a, i, t, s);
        let d = this.triangles;
        for (d.length = 0; i > 3; ) {
          let a = i - 1,
            r = 0,
            o = 1;
          for (;;) {
            e: if (!n[r]) {
              let u = s[a] << 1,
                c = s[r] << 1,
                f = s[o] << 1,
                m = t[u],
                g = t[u + 1],
                b = t[c],
                x = t[c + 1],
                p = t[f],
                w = t[f + 1];
              for (let y = (o + 1) % i; y != a; y = (y + 1) % i) {
                if (!n[y]) continue;
                let v = s[y] << 1,
                  A = t[v],
                  C = t[v + 1];
                if (
                  Ce.positiveArea(p, w, m, g, A, C) &&
                  Ce.positiveArea(m, g, b, x, A, C) &&
                  Ce.positiveArea(b, x, p, w, A, C)
                )
                  break e;
              }
              break;
            }
            if (o == 0) {
              do {
                if (!n[r]) break;
                r--;
              } while (r > 0);
              break;
            }
            (a = r), (r = o), (o = (o + 1) % i);
          }
          d.push(s[(i + r - 1) % i]),
            d.push(s[r]),
            d.push(s[(r + 1) % i]),
            s.splice(r, 1),
            n.splice(r, 1),
            i--;
          let l = (i + r - 1) % i,
            h = r == i ? 0 : r;
          (n[l] = Ce.isConcave(l, i, t, s)), (n[h] = Ce.isConcave(h, i, t, s));
        }
        return i == 3 && (d.push(s[2]), d.push(s[0]), d.push(s[1])), d;
      }
      decompose(e, t) {
        let i = e,
          s = this.convexPolygons;
        this.polygonPool.freeAll(s), (s.length = 0);
        let n = this.convexPolygonsIndices;
        this.polygonIndicesPool.freeAll(n), (n.length = 0);
        let d = this.polygonIndicesPool.obtain();
        d.length = 0;
        let a = this.polygonPool.obtain();
        a.length = 0;
        let r = -1,
          o = 0;
        for (let l = 0, h = t.length; l < h; l += 3) {
          let u = t[l] << 1,
            c = t[l + 1] << 1,
            f = t[l + 2] << 1,
            m = i[u],
            g = i[u + 1],
            b = i[c],
            x = i[c + 1],
            p = i[f],
            w = i[f + 1],
            y = !1;
          if (r == u) {
            let v = a.length - 4,
              A = Ce.winding(a[v], a[v + 1], a[v + 2], a[v + 3], p, w),
              C = Ce.winding(p, w, a[0], a[1], a[2], a[3]);
            A == o && C == o && (a.push(p), a.push(w), d.push(f), (y = !0));
          }
          y ||
            (a.length > 0
              ? (s.push(a), n.push(d))
              : (this.polygonPool.free(a), this.polygonIndicesPool.free(d)),
            (a = this.polygonPool.obtain()),
            (a.length = 0),
            a.push(m),
            a.push(g),
            a.push(b),
            a.push(x),
            a.push(p),
            a.push(w),
            (d = this.polygonIndicesPool.obtain()),
            (d.length = 0),
            d.push(u),
            d.push(c),
            d.push(f),
            (o = Ce.winding(m, g, b, x, p, w)),
            (r = u));
        }
        a.length > 0 && (s.push(a), n.push(d));
        for (let l = 0, h = s.length; l < h; l++) {
          if (((d = n[l]), d.length == 0)) continue;
          let u = d[0],
            c = d[d.length - 1];
          a = s[l];
          let f = a.length - 4,
            m = a[f],
            g = a[f + 1],
            b = a[f + 2],
            x = a[f + 3],
            p = a[0],
            w = a[1],
            y = a[2],
            v = a[3],
            A = Ce.winding(m, g, b, x, p, w);
          for (let C = 0; C < h; C++) {
            if (C == l) continue;
            let T = n[C];
            if (T.length != 3) continue;
            let I = T[0],
              M = T[1],
              F = T[2],
              L = s[C],
              k = L[L.length - 2],
              R = L[L.length - 1];
            if (I != u || M != c) continue;
            let Y = Ce.winding(m, g, b, x, k, R),
              ie = Ce.winding(k, R, p, w, y, v);
            Y == A &&
              ie == A &&
              ((L.length = 0),
              (T.length = 0),
              a.push(k),
              a.push(R),
              d.push(F),
              (m = b),
              (g = x),
              (b = k),
              (x = R),
              (C = 0));
          }
        }
        for (let l = s.length - 1; l >= 0; l--)
          (a = s[l]),
            a.length == 0 &&
              (s.splice(l, 1),
              this.polygonPool.free(a),
              (d = n[l]),
              n.splice(l, 1),
              this.polygonIndicesPool.free(d));
        return s;
      }
      static isConcave(e, t, i, s) {
        let n = s[(t + e - 1) % t] << 1,
          d = s[e] << 1,
          a = s[(e + 1) % t] << 1;
        return !this.positiveArea(
          i[n],
          i[n + 1],
          i[d],
          i[d + 1],
          i[a],
          i[a + 1]
        );
      }
      static positiveArea(e, t, i, s, n, d) {
        return e * (d - s) + i * (t - d) + n * (s - t) >= 0;
      }
      static winding(e, t, i, s, n, d) {
        let a = i - e,
          r = s - t;
        return n * r - d * a + a * t - e * r >= 0 ? 1 : -1;
      }
    },
    Xt = class {
      triangulator = new Ce();
      clippingPolygon = new Array();
      clipOutput = new Array();
      clippedVertices = new Array();
      clippedUVs = new Array();
      clippedTriangles = new Array();
      scratch = new Array();
      clipAttachment = null;
      clippingPolygons = null;
      clipStart(e, t) {
        if (this.clipAttachment) return 0;
        this.clipAttachment = t;
        let i = t.worldVerticesLength,
          s = P.setArraySize(this.clippingPolygon, i);
        t.computeWorldVertices(e, 0, i, s, 0, 2);
        let n = this.clippingPolygon;
        Xt.makeClockwise(n);
        let d = (this.clippingPolygons = this.triangulator.decompose(
          n,
          this.triangulator.triangulate(n)
        ));
        for (let a = 0, r = d.length; a < r; a++) {
          let o = d[a];
          Xt.makeClockwise(o), o.push(o[0]), o.push(o[1]);
        }
        return d.length;
      }
      clipEndWithSlot(e) {
        this.clipAttachment &&
          this.clipAttachment.endSlot == e.data &&
          this.clipEnd();
      }
      clipEnd() {
        this.clipAttachment &&
          ((this.clipAttachment = null),
          (this.clippingPolygons = null),
          (this.clippedVertices.length = 0),
          (this.clippedTriangles.length = 0),
          (this.clippingPolygon.length = 0));
      }
      isClipping() {
        return this.clipAttachment != null;
      }
      clipTriangles(e, t, i, s, n, d, a, r) {
        let o, l, h, u, c, f;
        typeof t == "number"
          ? ((o = i), (l = s), (h = n), (u = d), (c = a), (f = r))
          : ((o = t), (l = i), (h = s), (u = n), (c = d), (f = a)),
          h && u && c && typeof f == "boolean"
            ? this.clipTrianglesRender(e, o, l, h, u, c, f)
            : this.clipTrianglesNoRender(e, o, l);
      }
      clipTrianglesNoRender(e, t, i) {
        let s = this.clipOutput,
          n = this.clippedVertices,
          d = this.clippedTriangles,
          a = this.clippingPolygons,
          r = a.length,
          o = 0;
        (n.length = 0), (d.length = 0);
        for (let l = 0; l < i; l += 3) {
          let h = t[l] << 1,
            u = e[h],
            c = e[h + 1];
          h = t[l + 1] << 1;
          let f = e[h],
            m = e[h + 1];
          h = t[l + 2] << 1;
          let g = e[h],
            b = e[h + 1];
          for (let x = 0; x < r; x++) {
            let p = n.length;
            if (this.clip(u, c, f, m, g, b, a[x], s)) {
              let w = s.length;
              if (w == 0) continue;
              let y = w >> 1,
                v = this.clipOutput,
                A = P.setArraySize(n, p + y * 2);
              for (let T = 0; T < w; T += 2, p += 2) {
                let I = v[T],
                  M = v[T + 1];
                (A[p] = I), (A[p + 1] = M);
              }
              p = d.length;
              let C = P.setArraySize(d, p + 3 * (y - 2));
              y--;
              for (let T = 1; T < y; T++, p += 3)
                (C[p] = o), (C[p + 1] = o + T), (C[p + 2] = o + T + 1);
              o += y + 1;
            } else {
              let w = P.setArraySize(n, p + 6);
              (w[p] = u),
                (w[p + 1] = c),
                (w[p + 2] = f),
                (w[p + 3] = m),
                (w[p + 4] = g),
                (w[p + 5] = b),
                (p = d.length);
              let y = P.setArraySize(d, p + 3);
              (y[p] = o), (y[p + 1] = o + 1), (y[p + 2] = o + 2), (o += 3);
              break;
            }
          }
        }
      }
      clipTrianglesRender(e, t, i, s, n, d, a) {
        let r = this.clipOutput,
          o = this.clippedVertices,
          l = this.clippedTriangles,
          h = this.clippingPolygons,
          u = h.length,
          c = a ? 12 : 8,
          f = 0;
        (o.length = 0), (l.length = 0);
        for (let m = 0; m < i; m += 3) {
          let g = t[m] << 1,
            b = e[g],
            x = e[g + 1],
            p = s[g],
            w = s[g + 1];
          g = t[m + 1] << 1;
          let y = e[g],
            v = e[g + 1],
            A = s[g],
            C = s[g + 1];
          g = t[m + 2] << 1;
          let T = e[g],
            I = e[g + 1],
            M = s[g],
            F = s[g + 1];
          for (let L = 0; L < u; L++) {
            let k = o.length;
            if (this.clip(b, x, y, v, T, I, h[L], r)) {
              let R = r.length;
              if (R == 0) continue;
              let Y = v - I,
                ie = T - y,
                se = b - T,
                te = I - x,
                de = 1 / (Y * se + ie * (x - I)),
                ce = R >> 1,
                ue = this.clipOutput,
                J = P.setArraySize(o, k + ce * c);
              for (let ae = 0; ae < R; ae += 2, k += c) {
                let Ee = ue[ae],
                  Te = ue[ae + 1];
                (J[k] = Ee),
                  (J[k + 1] = Te),
                  (J[k + 2] = n.r),
                  (J[k + 3] = n.g),
                  (J[k + 4] = n.b),
                  (J[k + 5] = n.a);
                let ge = Ee - T,
                  be = Te - I,
                  ve = (Y * ge + ie * be) * de,
                  qe = (te * ge + se * be) * de,
                  pt = 1 - ve - qe;
                (J[k + 6] = p * ve + A * qe + M * pt),
                  (J[k + 7] = w * ve + C * qe + F * pt),
                  a &&
                    ((J[k + 8] = d.r),
                    (J[k + 9] = d.g),
                    (J[k + 10] = d.b),
                    (J[k + 11] = d.a));
              }
              k = l.length;
              let ne = P.setArraySize(l, k + 3 * (ce - 2));
              ce--;
              for (let ae = 1; ae < ce; ae++, k += 3)
                (ne[k] = f), (ne[k + 1] = f + ae), (ne[k + 2] = f + ae + 1);
              f += ce + 1;
            } else {
              let R = P.setArraySize(o, k + 3 * c);
              (R[k] = b),
                (R[k + 1] = x),
                (R[k + 2] = n.r),
                (R[k + 3] = n.g),
                (R[k + 4] = n.b),
                (R[k + 5] = n.a),
                a
                  ? ((R[k + 6] = p),
                    (R[k + 7] = w),
                    (R[k + 8] = d.r),
                    (R[k + 9] = d.g),
                    (R[k + 10] = d.b),
                    (R[k + 11] = d.a),
                    (R[k + 12] = y),
                    (R[k + 13] = v),
                    (R[k + 14] = n.r),
                    (R[k + 15] = n.g),
                    (R[k + 16] = n.b),
                    (R[k + 17] = n.a),
                    (R[k + 18] = A),
                    (R[k + 19] = C),
                    (R[k + 20] = d.r),
                    (R[k + 21] = d.g),
                    (R[k + 22] = d.b),
                    (R[k + 23] = d.a),
                    (R[k + 24] = T),
                    (R[k + 25] = I),
                    (R[k + 26] = n.r),
                    (R[k + 27] = n.g),
                    (R[k + 28] = n.b),
                    (R[k + 29] = n.a),
                    (R[k + 30] = M),
                    (R[k + 31] = F),
                    (R[k + 32] = d.r),
                    (R[k + 33] = d.g),
                    (R[k + 34] = d.b),
                    (R[k + 35] = d.a))
                  : ((R[k + 6] = p),
                    (R[k + 7] = w),
                    (R[k + 8] = y),
                    (R[k + 9] = v),
                    (R[k + 10] = n.r),
                    (R[k + 11] = n.g),
                    (R[k + 12] = n.b),
                    (R[k + 13] = n.a),
                    (R[k + 14] = A),
                    (R[k + 15] = C),
                    (R[k + 16] = T),
                    (R[k + 17] = I),
                    (R[k + 18] = n.r),
                    (R[k + 19] = n.g),
                    (R[k + 20] = n.b),
                    (R[k + 21] = n.a),
                    (R[k + 22] = M),
                    (R[k + 23] = F)),
                (k = l.length);
              let Y = P.setArraySize(l, k + 3);
              (Y[k] = f), (Y[k + 1] = f + 1), (Y[k + 2] = f + 2), (f += 3);
              break;
            }
          }
        }
      }
      clipTrianglesUnpacked(e, t, i, s) {
        let n = this.clipOutput,
          d = this.clippedVertices,
          a = this.clippedUVs,
          r = this.clippedTriangles,
          o = this.clippingPolygons,
          l = o.length,
          h = 0;
        (d.length = 0), (a.length = 0), (r.length = 0);
        for (let u = 0; u < i; u += 3) {
          let c = t[u] << 1,
            f = e[c],
            m = e[c + 1],
            g = s[c],
            b = s[c + 1];
          c = t[u + 1] << 1;
          let x = e[c],
            p = e[c + 1],
            w = s[c],
            y = s[c + 1];
          c = t[u + 2] << 1;
          let v = e[c],
            A = e[c + 1],
            C = s[c],
            T = s[c + 1];
          for (let I = 0; I < l; I++) {
            let M = d.length;
            if (this.clip(f, m, x, p, v, A, o[I], n)) {
              let F = n.length;
              if (F == 0) continue;
              let L = p - A,
                k = v - x,
                R = f - v,
                Y = A - m,
                ie = 1 / (L * R + k * (m - A)),
                se = F >> 1,
                te = this.clipOutput,
                de = P.setArraySize(d, M + se * 2),
                ce = P.setArraySize(a, M + se * 2);
              for (let J = 0; J < F; J += 2, M += 2) {
                let ne = te[J],
                  ae = te[J + 1];
                (de[M] = ne), (de[M + 1] = ae);
                let Ee = ne - v,
                  Te = ae - A,
                  ge = (L * Ee + k * Te) * ie,
                  be = (Y * Ee + R * Te) * ie,
                  ve = 1 - ge - be;
                (ce[M] = g * ge + w * be + C * ve),
                  (ce[M + 1] = b * ge + y * be + T * ve);
              }
              M = r.length;
              let ue = P.setArraySize(r, M + 3 * (se - 2));
              se--;
              for (let J = 1; J < se; J++, M += 3)
                (ue[M] = h), (ue[M + 1] = h + J), (ue[M + 2] = h + J + 1);
              h += se + 1;
            } else {
              let F = P.setArraySize(d, M + 6);
              (F[M] = f),
                (F[M + 1] = m),
                (F[M + 2] = x),
                (F[M + 3] = p),
                (F[M + 4] = v),
                (F[M + 5] = A);
              let L = P.setArraySize(a, M + 3 * 2);
              (L[M] = g),
                (L[M + 1] = b),
                (L[M + 2] = w),
                (L[M + 3] = y),
                (L[M + 4] = C),
                (L[M + 5] = T),
                (M = r.length);
              let k = P.setArraySize(r, M + 3);
              (k[M] = h), (k[M + 1] = h + 1), (k[M + 2] = h + 2), (h += 3);
              break;
            }
          }
        }
      }
      clip(e, t, i, s, n, d, a, r) {
        let o = r,
          l = !1,
          h;
        a.length % 4 >= 2 ? ((h = r), (r = this.scratch)) : (h = this.scratch),
          (h.length = 0),
          h.push(e),
          h.push(t),
          h.push(i),
          h.push(s),
          h.push(n),
          h.push(d),
          h.push(e),
          h.push(t),
          (r.length = 0);
        let u = a.length - 4,
          c = a;
        for (let f = 0; ; f += 2) {
          let m = c[f],
            g = c[f + 1],
            b = m - c[f + 2],
            x = g - c[f + 3],
            p = r.length,
            w = h;
          for (let v = 0, A = h.length - 2; v < A; ) {
            let C = w[v],
              T = w[v + 1];
            v += 2;
            let I = w[v],
              M = w[v + 1],
              F = x * (m - I) > b * (g - M),
              L = x * (m - C) - b * (g - T);
            if (L > 0) {
              if (F) {
                r.push(I), r.push(M);
                continue;
              }
              let k = I - C,
                R = M - T,
                Y = L / (k * x - R * b);
              if (Y >= 0 && Y <= 1) r.push(C + k * Y), r.push(T + R * Y);
              else {
                r.push(I), r.push(M);
                continue;
              }
            } else if (F) {
              let k = I - C,
                R = M - T,
                Y = L / (k * x - R * b);
              if (Y >= 0 && Y <= 1)
                r.push(C + k * Y), r.push(T + R * Y), r.push(I), r.push(M);
              else {
                r.push(I), r.push(M);
                continue;
              }
            }
            l = !0;
          }
          if (p == r.length) return (o.length = 0), !0;
          if ((r.push(r[0]), r.push(r[1]), f == u)) break;
          let y = r;
          (r = h), (r.length = 0), (h = y);
        }
        if (o != r) {
          o.length = 0;
          for (let f = 0, m = r.length - 2; f < m; f++) o[f] = r[f];
        } else o.length = o.length - 2;
        return l;
      }
      static makeClockwise(e) {
        let t = e,
          i = e.length,
          s = t[i - 2] * t[1] - t[0] * t[i - 1],
          n = 0,
          d = 0,
          a = 0,
          r = 0;
        for (let o = 0, l = i - 3; o < l; o += 2)
          (n = t[o]),
            (d = t[o + 1]),
            (a = t[o + 2]),
            (r = t[o + 3]),
            (s += n * r - a * d);
        if (!(s < 0))
          for (let o = 0, l = i - 2, h = i >> 1; o < h; o += 2) {
            let u = t[o],
              c = t[o + 1],
              f = l - o;
            (t[o] = t[f]), (t[o + 1] = t[f + 1]), (t[f] = u), (t[f + 1] = c);
          }
      }
    },
    Ys = class {
      attachmentLoader;
      scale = 1;
      linkedMeshes = new Array();
      constructor(e) {
        this.attachmentLoader = e;
      }
      readSkeletonData(e) {
        let t = this.scale,
          i = new Oi(),
          s = typeof e == "string" ? JSON.parse(e) : e,
          n = s.skeleton;
        if (
          (n &&
            ((i.hash = n.hash),
            (i.version = n.spine),
            (i.x = n.x),
            (i.y = n.y),
            (i.width = n.width),
            (i.height = n.height),
            (i.referenceScale = E(n, "referenceScale", 100) * t),
            (i.fps = n.fps),
            (i.imagesPath = n.images ?? null),
            (i.audioPath = n.audio ?? null)),
          s.bones)
        )
          for (let d = 0; d < s.bones.length; d++) {
            let a = s.bones[d],
              r = null,
              o = E(a, "parent", null);
            o && (r = i.findBone(o));
            let l = new Ri(i.bones.length, a.name, r);
            (l.length = E(a, "length", 0) * t),
              (l.x = E(a, "x", 0) * t),
              (l.y = E(a, "y", 0) * t),
              (l.rotation = E(a, "rotation", 0)),
              (l.scaleX = E(a, "scaleX", 1)),
              (l.scaleY = E(a, "scaleY", 1)),
              (l.shearX = E(a, "shearX", 0)),
              (l.shearY = E(a, "shearY", 0)),
              (l.inherit = P.enumValue(dt, E(a, "inherit", "Normal"))),
              (l.skinRequired = E(a, "skin", !1));
            let h = E(a, "color", null);
            h && l.color.setFromString(h), i.bones.push(l);
          }
        if (s.slots)
          for (let d = 0; d < s.slots.length; d++) {
            let a = s.slots[d],
              r = a.name,
              o = i.findBone(a.bone);
            if (!o)
              throw new Error(`Couldn't find bone ${a.bone} for slot ${r}`);
            let l = new Ni(i.slots.length, r, o),
              h = E(a, "color", null);
            h && l.color.setFromString(h);
            let u = E(a, "dark", null);
            u && (l.darkColor = V.fromString(u)),
              (l.attachmentName = E(a, "attachment", null)),
              (l.blendMode = P.enumValue(Lt, E(a, "blend", "normal"))),
              (l.visible = E(a, "visible", !0)),
              i.slots.push(l);
          }
        if (s.ik)
          for (let d = 0; d < s.ik.length; d++) {
            let a = s.ik[d],
              r = new Pi(a.name);
            (r.order = E(a, "order", 0)), (r.skinRequired = E(a, "skin", !1));
            for (let l = 0; l < a.bones.length; l++) {
              let h = i.findBone(a.bones[l]);
              if (!h)
                throw new Error(
                  `Couldn't find bone ${a.bones[l]} for IK constraint ${a.name}.`
                );
              r.bones.push(h);
            }
            let o = i.findBone(a.target);
            if (!o)
              throw new Error(
                `Couldn't find target bone ${a.target} for IK constraint ${a.name}.`
              );
            (r.target = o),
              (r.mix = E(a, "mix", 1)),
              (r.softness = E(a, "softness", 0) * t),
              (r.bendDirection = E(a, "bendPositive", !0) ? 1 : -1),
              (r.compress = E(a, "compress", !1)),
              (r.stretch = E(a, "stretch", !1)),
              (r.uniform = E(a, "uniform", !1)),
              i.ikConstraints.push(r);
          }
        if (s.transform)
          for (let d = 0; d < s.transform.length; d++) {
            let a = s.transform[d],
              r = new Ui(a.name);
            (r.order = E(a, "order", 0)), (r.skinRequired = E(a, "skin", !1));
            for (let h = 0; h < a.bones.length; h++) {
              let u = a.bones[h],
                c = i.findBone(u);
              if (!c)
                throw new Error(
                  `Couldn't find bone ${u} for transform constraint ${a.name}.`
                );
              r.bones.push(c);
            }
            let o = a.target,
              l = i.findBone(o);
            if (!l)
              throw new Error(
                `Couldn't find target bone ${o} for transform constraint ${a.name}.`
              );
            (r.target = l),
              (r.local = E(a, "local", !1)),
              (r.relative = E(a, "relative", !1)),
              (r.offsetRotation = E(a, "rotation", 0)),
              (r.offsetX = E(a, "x", 0) * t),
              (r.offsetY = E(a, "y", 0) * t),
              (r.offsetScaleX = E(a, "scaleX", 0)),
              (r.offsetScaleY = E(a, "scaleY", 0)),
              (r.offsetShearY = E(a, "shearY", 0)),
              (r.mixRotate = E(a, "mixRotate", 1)),
              (r.mixX = E(a, "mixX", 1)),
              (r.mixY = E(a, "mixY", r.mixX)),
              (r.mixScaleX = E(a, "mixScaleX", 1)),
              (r.mixScaleY = E(a, "mixScaleY", r.mixScaleX)),
              (r.mixShearY = E(a, "mixShearY", 1)),
              i.transformConstraints.push(r);
          }
        if (s.path)
          for (let d = 0; d < s.path.length; d++) {
            let a = s.path[d],
              r = new Bi(a.name);
            (r.order = E(a, "order", 0)), (r.skinRequired = E(a, "skin", !1));
            for (let h = 0; h < a.bones.length; h++) {
              let u = a.bones[h],
                c = i.findBone(u);
              if (!c)
                throw new Error(
                  `Couldn't find bone ${u} for path constraint ${a.name}.`
                );
              r.bones.push(c);
            }
            let o = a.target,
              l = i.findSlot(o);
            if (!l)
              throw new Error(
                `Couldn't find target slot ${o} for path constraint ${a.name}.`
              );
            (r.target = l),
              (r.positionMode = P.enumValue(
                Mt,
                E(a, "positionMode", "Percent")
              )),
              (r.spacingMode = P.enumValue(Rt, E(a, "spacingMode", "Length"))),
              (r.rotateMode = P.enumValue(Ft, E(a, "rotateMode", "Tangent"))),
              (r.offsetRotation = E(a, "rotation", 0)),
              (r.position = E(a, "position", 0)),
              r.positionMode == 0 && (r.position *= t),
              (r.spacing = E(a, "spacing", 0)),
              (r.spacingMode == 0 || r.spacingMode == 1) && (r.spacing *= t),
              (r.mixRotate = E(a, "mixRotate", 1)),
              (r.mixX = E(a, "mixX", 1)),
              (r.mixY = E(a, "mixY", r.mixX)),
              i.pathConstraints.push(r);
          }
        if (s.physics)
          for (let d = 0; d < s.physics.length; d++) {
            const a = s.physics[d],
              r = new Is(a.name);
            (r.order = E(a, "order", 0)), (r.skinRequired = E(a, "skin", !1));
            const o = a.bone,
              l = i.findBone(o);
            if (l == null) throw new Error("Physics bone not found: " + o);
            (r.bone = l),
              (r.x = E(a, "x", 0)),
              (r.y = E(a, "y", 0)),
              (r.rotate = E(a, "rotate", 0)),
              (r.scaleX = E(a, "scaleX", 0)),
              (r.shearX = E(a, "shearX", 0)),
              (r.limit = E(a, "limit", 5e3) * t),
              (r.step = 1 / E(a, "fps", 60)),
              (r.inertia = E(a, "inertia", 1)),
              (r.strength = E(a, "strength", 100)),
              (r.damping = E(a, "damping", 1)),
              (r.massInverse = 1 / E(a, "mass", 1)),
              (r.wind = E(a, "wind", 0)),
              (r.gravity = E(a, "gravity", 0)),
              (r.mix = E(a, "mix", 1)),
              (r.inertiaGlobal = E(a, "inertiaGlobal", !1)),
              (r.strengthGlobal = E(a, "strengthGlobal", !1)),
              (r.dampingGlobal = E(a, "dampingGlobal", !1)),
              (r.massGlobal = E(a, "massGlobal", !1)),
              (r.windGlobal = E(a, "windGlobal", !1)),
              (r.gravityGlobal = E(a, "gravityGlobal", !1)),
              (r.mixGlobal = E(a, "mixGlobal", !1)),
              i.physicsConstraints.push(r);
          }
        if (s.skins)
          for (let d = 0; d < s.skins.length; d++) {
            let a = s.skins[d],
              r = new Yt(a.name);
            if (a.bones)
              for (let o = 0; o < a.bones.length; o++) {
                let l = a.bones[o],
                  h = i.findBone(l);
                if (!h)
                  throw new Error(
                    `Couldn't find bone ${l} for skin ${a.name}.`
                  );
                r.bones.push(h);
              }
            if (a.ik)
              for (let o = 0; o < a.ik.length; o++) {
                let l = a.ik[o],
                  h = i.findIkConstraint(l);
                if (!h)
                  throw new Error(
                    `Couldn't find IK constraint ${l} for skin ${a.name}.`
                  );
                r.constraints.push(h);
              }
            if (a.transform)
              for (let o = 0; o < a.transform.length; o++) {
                let l = a.transform[o],
                  h = i.findTransformConstraint(l);
                if (!h)
                  throw new Error(
                    `Couldn't find transform constraint ${l} for skin ${a.name}.`
                  );
                r.constraints.push(h);
              }
            if (a.path)
              for (let o = 0; o < a.path.length; o++) {
                let l = a.path[o],
                  h = i.findPathConstraint(l);
                if (!h)
                  throw new Error(
                    `Couldn't find path constraint ${l} for skin ${a.name}.`
                  );
                r.constraints.push(h);
              }
            if (a.physics)
              for (let o = 0; o < a.physics.length; o++) {
                let l = a.physics[o],
                  h = i.findPhysicsConstraint(l);
                if (!h)
                  throw new Error(
                    `Couldn't find physics constraint ${l} for skin ${a.name}.`
                  );
                r.constraints.push(h);
              }
            for (let o in a.attachments) {
              let l = i.findSlot(o);
              if (!l)
                throw new Error(`Couldn't find slot ${o} for skin ${a.name}.`);
              let h = a.attachments[o];
              for (let u in h) {
                let c = this.readAttachment(h[u], r, l.index, u, i);
                c && r.setAttachment(l.index, u, c);
              }
            }
            i.skins.push(r), r.name == "default" && (i.defaultSkin = r);
          }
        for (let d = 0, a = this.linkedMeshes.length; d < a; d++) {
          let r = this.linkedMeshes[d],
            o = r.skin ? i.findSkin(r.skin) : i.defaultSkin;
          if (!o) throw new Error(`Skin not found: ${r.skin}`);
          let l = o.getAttachment(r.slotIndex, r.parent);
          if (!l) throw new Error(`Parent mesh not found: ${r.parent}`);
          (r.mesh.timelineAttachment = r.inheritTimeline ? l : r.mesh),
            r.mesh.setParentMesh(l),
            r.mesh.region != null && r.mesh.updateRegion();
        }
        if (((this.linkedMeshes.length = 0), s.events))
          for (let d in s.events) {
            let a = s.events[d],
              r = new Xi(d);
            (r.intValue = E(a, "int", 0)),
              (r.floatValue = E(a, "float", 0)),
              (r.stringValue = E(a, "string", "")),
              (r.audioPath = E(a, "audio", null)),
              r.audioPath &&
                ((r.volume = E(a, "volume", 1)),
                (r.balance = E(a, "balance", 0))),
              i.events.push(r);
          }
        if (s.animations)
          for (let d in s.animations) {
            let a = s.animations[d];
            this.readAnimation(a, d, i);
          }
        return i;
      }
      readAttachment(e, t, i, s, n) {
        let d = this.scale;
        switch (((s = E(e, "name", s)), E(e, "type", "region"))) {
          case "region": {
            let a = E(e, "path", s),
              r = this.readSequence(E(e, "sequence", null)),
              o = this.attachmentLoader.newRegionAttachment(t, s, a, r);
            if (!o) return null;
            (o.path = a),
              (o.x = E(e, "x", 0) * d),
              (o.y = E(e, "y", 0) * d),
              (o.scaleX = E(e, "scaleX", 1)),
              (o.scaleY = E(e, "scaleY", 1)),
              (o.rotation = E(e, "rotation", 0)),
              (o.width = e.width * d),
              (o.height = e.height * d),
              (o.sequence = r);
            let l = E(e, "color", null);
            return (
              l && o.color.setFromString(l),
              o.region != null && o.updateRegion(),
              o
            );
          }
          case "boundingbox": {
            let a = this.attachmentLoader.newBoundingBoxAttachment(t, s);
            if (!a) return null;
            this.readVertices(e, a, e.vertexCount << 1);
            let r = E(e, "color", null);
            return r && a.color.setFromString(r), a;
          }
          case "mesh":
          case "linkedmesh": {
            let a = E(e, "path", s),
              r = this.readSequence(E(e, "sequence", null)),
              o = this.attachmentLoader.newMeshAttachment(t, s, a, r);
            if (!o) return null;
            o.path = a;
            let l = E(e, "color", null);
            l && o.color.setFromString(l),
              (o.width = E(e, "width", 0) * d),
              (o.height = E(e, "height", 0) * d),
              (o.sequence = r);
            let h = E(e, "parent", null);
            if (h)
              return (
                this.linkedMeshes.push(
                  new Gr(o, E(e, "skin", null), i, h, E(e, "timelines", !0))
                ),
                o
              );
            let u = e.uvs;
            return (
              this.readVertices(e, o, u.length),
              (o.triangles = e.triangles),
              (o.regionUVs = u),
              o.region != null && o.updateRegion(),
              (o.edges = E(e, "edges", null)),
              (o.hullLength = E(e, "hull", 0) * 2),
              o
            );
          }
          case "path": {
            let a = this.attachmentLoader.newPathAttachment(t, s);
            if (!a) return null;
            (a.closed = E(e, "closed", !1)),
              (a.constantSpeed = E(e, "constantSpeed", !0));
            let r = e.vertexCount;
            this.readVertices(e, a, r << 1);
            let o = P.newArray(r / 3, 0);
            for (let h = 0; h < e.lengths.length; h++) o[h] = e.lengths[h] * d;
            a.lengths = o;
            let l = E(e, "color", null);
            return l && a.color.setFromString(l), a;
          }
          case "point": {
            let a = this.attachmentLoader.newPointAttachment(t, s);
            if (!a) return null;
            (a.x = E(e, "x", 0) * d),
              (a.y = E(e, "y", 0) * d),
              (a.rotation = E(e, "rotation", 0));
            let r = E(e, "color", null);
            return r && a.color.setFromString(r), a;
          }
          case "clipping": {
            let a = this.attachmentLoader.newClippingAttachment(t, s);
            if (!a) return null;
            let r = E(e, "end", null);
            r && (a.endSlot = n.findSlot(r));
            let o = e.vertexCount;
            this.readVertices(e, a, o << 1);
            let l = E(e, "color", null);
            return l && a.color.setFromString(l), a;
          }
        }
        return null;
      }
      readSequence(e) {
        if (e == null) return null;
        let t = new jt(E(e, "count", 0));
        return (
          (t.start = E(e, "start", 1)),
          (t.digits = E(e, "digits", 0)),
          (t.setupIndex = E(e, "setup", 0)),
          t
        );
      }
      readVertices(e, t, i) {
        let s = this.scale;
        t.worldVerticesLength = i;
        let n = e.vertices;
        if (i == n.length) {
          let r = P.toFloatArray(n);
          if (s != 1) for (let o = 0, l = n.length; o < l; o++) r[o] *= s;
          t.vertices = r;
          return;
        }
        let d = new Array(),
          a = new Array();
        for (let r = 0, o = n.length; r < o; ) {
          let l = n[r++];
          a.push(l);
          for (let h = r + l * 4; r < h; r += 4)
            a.push(n[r]),
              d.push(n[r + 1] * s),
              d.push(n[r + 2] * s),
              d.push(n[r + 3]);
        }
        (t.bones = a), (t.vertices = P.toFloatArray(d));
      }
      readAnimation(e, t, i) {
        let s = this.scale,
          n = new Array();
        if (e.slots)
          for (let a in e.slots) {
            let r = e.slots[a],
              o = i.findSlot(a);
            if (!o) throw new Error("Slot not found: " + a);
            let l = o.index;
            for (let h in r) {
              let u = r[h];
              if (!u) continue;
              let c = u.length;
              if (h == "attachment") {
                let f = new He(c, l);
                for (let m = 0; m < c; m++) {
                  let g = u[m];
                  f.setFrame(m, E(g, "time", 0), E(g, "name", null));
                }
                n.push(f);
              } else if (h == "rgba") {
                let f = new ni(c, c << 2, l),
                  m = u[0],
                  g = E(m, "time", 0),
                  b = V.fromString(m.color);
                for (let x = 0, p = 0; ; x++) {
                  f.setFrame(x, g, b.r, b.g, b.b, b.a);
                  let w = u[x + 1];
                  if (!w) {
                    f.shrink(p);
                    break;
                  }
                  let y = E(w, "time", 0),
                    v = V.fromString(w.color),
                    A = m.curve;
                  A &&
                    ((p = he(A, f, p, x, 0, g, y, b.r, v.r, 1)),
                    (p = he(A, f, p, x, 1, g, y, b.g, v.g, 1)),
                    (p = he(A, f, p, x, 2, g, y, b.b, v.b, 1)),
                    (p = he(A, f, p, x, 3, g, y, b.a, v.a, 1))),
                    (g = y),
                    (b = v),
                    (m = w);
                }
                n.push(f);
              } else if (h == "rgb") {
                let f = new ai(c, c * 3, l),
                  m = u[0],
                  g = E(m, "time", 0),
                  b = V.fromString(m.color);
                for (let x = 0, p = 0; ; x++) {
                  f.setFrame(x, g, b.r, b.g, b.b);
                  let w = u[x + 1];
                  if (!w) {
                    f.shrink(p);
                    break;
                  }
                  let y = E(w, "time", 0),
                    v = V.fromString(w.color),
                    A = m.curve;
                  A &&
                    ((p = he(A, f, p, x, 0, g, y, b.r, v.r, 1)),
                    (p = he(A, f, p, x, 1, g, y, b.g, v.g, 1)),
                    (p = he(A, f, p, x, 2, g, y, b.b, v.b, 1))),
                    (g = y),
                    (b = v),
                    (m = w);
                }
                n.push(f);
              } else if (h == "alpha") n.push(Ye(u, new li(c, c, l), 0, 1));
              else if (h == "rgba2") {
                let f = new oi(c, c * 7, l),
                  m = u[0],
                  g = E(m, "time", 0),
                  b = V.fromString(m.light),
                  x = V.fromString(m.dark);
                for (let p = 0, w = 0; ; p++) {
                  f.setFrame(p, g, b.r, b.g, b.b, b.a, x.r, x.g, x.b);
                  let y = u[p + 1];
                  if (!y) {
                    f.shrink(w);
                    break;
                  }
                  let v = E(y, "time", 0),
                    A = V.fromString(y.light),
                    C = V.fromString(y.dark),
                    T = m.curve;
                  T &&
                    ((w = he(T, f, w, p, 0, g, v, b.r, A.r, 1)),
                    (w = he(T, f, w, p, 1, g, v, b.g, A.g, 1)),
                    (w = he(T, f, w, p, 2, g, v, b.b, A.b, 1)),
                    (w = he(T, f, w, p, 3, g, v, b.a, A.a, 1)),
                    (w = he(T, f, w, p, 4, g, v, x.r, C.r, 1)),
                    (w = he(T, f, w, p, 5, g, v, x.g, C.g, 1)),
                    (w = he(T, f, w, p, 6, g, v, x.b, C.b, 1))),
                    (g = v),
                    (b = A),
                    (x = C),
                    (m = y);
                }
                n.push(f);
              } else if (h == "rgb2") {
                let f = new hi(c, c * 6, l),
                  m = u[0],
                  g = E(m, "time", 0),
                  b = V.fromString(m.light),
                  x = V.fromString(m.dark);
                for (let p = 0, w = 0; ; p++) {
                  f.setFrame(p, g, b.r, b.g, b.b, x.r, x.g, x.b);
                  let y = u[p + 1];
                  if (!y) {
                    f.shrink(w);
                    break;
                  }
                  let v = E(y, "time", 0),
                    A = V.fromString(y.light),
                    C = V.fromString(y.dark),
                    T = m.curve;
                  T &&
                    ((w = he(T, f, w, p, 0, g, v, b.r, A.r, 1)),
                    (w = he(T, f, w, p, 1, g, v, b.g, A.g, 1)),
                    (w = he(T, f, w, p, 2, g, v, b.b, A.b, 1)),
                    (w = he(T, f, w, p, 3, g, v, x.r, C.r, 1)),
                    (w = he(T, f, w, p, 4, g, v, x.g, C.g, 1)),
                    (w = he(T, f, w, p, 5, g, v, x.b, C.b, 1))),
                    (g = v),
                    (b = A),
                    (x = C),
                    (m = y);
                }
                n.push(f);
              }
            }
          }
        if (e.bones)
          for (let a in e.bones) {
            let r = e.bones[a],
              o = i.findBone(a);
            if (!o) throw new Error("Bone not found: " + a);
            let l = o.index;
            for (let h in r) {
              let u = r[h],
                c = u.length;
              if (c != 0) {
                if (h === "rotate") n.push(Ye(u, new ot(c, c, l), 0, 1));
                else if (h === "translate") {
                  let f = new Zt(c, c << 1, l);
                  n.push(Wi(u, f, "x", "y", 0, s));
                } else if (h === "translatex") {
                  let f = new Jt(c, c, l);
                  n.push(Ye(u, f, 0, s));
                } else if (h === "translatey") {
                  let f = new Kt(c, c, l);
                  n.push(Ye(u, f, 0, s));
                } else if (h === "scale") {
                  let f = new Qt(c, c << 1, l);
                  n.push(Wi(u, f, "x", "y", 1, 1));
                } else if (h === "scalex") {
                  let f = new $t(c, c, l);
                  n.push(Ye(u, f, 1, 1));
                } else if (h === "scaley") {
                  let f = new ei(c, c, l);
                  n.push(Ye(u, f, 1, 1));
                } else if (h === "shear") {
                  let f = new ti(c, c << 1, l);
                  n.push(Wi(u, f, "x", "y", 0, 1));
                } else if (h === "shearx") {
                  let f = new ii(c, c, l);
                  n.push(Ye(u, f, 0, 1));
                } else if (h === "sheary") {
                  let f = new si(c, c, l);
                  n.push(Ye(u, f, 0, 1));
                } else if (h === "inherit") {
                  let f = new ri(c, o.index);
                  for (let m = 0; m < u.length; m++) {
                    let g = u[m];
                    f.setFrame(
                      m,
                      E(g, "time", 0),
                      P.enumValue(dt, E(g, "inherit", "Normal"))
                    );
                  }
                  n.push(f);
                }
              }
            }
          }
        if (e.ik)
          for (let a in e.ik) {
            let r = e.ik[a],
              o = r[0];
            if (!o) continue;
            let l = i.findIkConstraint(a);
            if (!l) throw new Error("IK Constraint not found: " + a);
            let h = i.ikConstraints.indexOf(l),
              u = new ci(r.length, r.length << 1, h),
              c = E(o, "time", 0),
              f = E(o, "mix", 1),
              m = E(o, "softness", 0) * s;
            for (let g = 0, b = 0; ; g++) {
              u.setFrame(
                g,
                c,
                f,
                m,
                E(o, "bendPositive", !0) ? 1 : -1,
                E(o, "compress", !1),
                E(o, "stretch", !1)
              );
              let x = r[g + 1];
              if (!x) {
                u.shrink(b);
                break;
              }
              let p = E(x, "time", 0),
                w = E(x, "mix", 1),
                y = E(x, "softness", 0) * s,
                v = o.curve;
              v &&
                ((b = he(v, u, b, g, 0, c, p, f, w, 1)),
                (b = he(v, u, b, g, 1, c, p, m, y, s))),
                (c = p),
                (f = w),
                (m = y),
                (o = x);
            }
            n.push(u);
          }
        if (e.transform)
          for (let a in e.transform) {
            let r = e.transform[a],
              o = r[0];
            if (!o) continue;
            let l = i.findTransformConstraint(a);
            if (!l) throw new Error("Transform constraint not found: " + a);
            let h = i.transformConstraints.indexOf(l),
              u = new ui(r.length, r.length * 6, h),
              c = E(o, "time", 0),
              f = E(o, "mixRotate", 1),
              m = E(o, "mixX", 1),
              g = E(o, "mixY", m),
              b = E(o, "mixScaleX", 1),
              x = E(o, "mixScaleY", b),
              p = E(o, "mixShearY", 1);
            for (let w = 0, y = 0; ; w++) {
              u.setFrame(w, c, f, m, g, b, x, p);
              let v = r[w + 1];
              if (!v) {
                u.shrink(y);
                break;
              }
              let A = E(v, "time", 0),
                C = E(v, "mixRotate", 1),
                T = E(v, "mixX", 1),
                I = E(v, "mixY", T),
                M = E(v, "mixScaleX", 1),
                F = E(v, "mixScaleY", M),
                L = E(v, "mixShearY", 1),
                k = o.curve;
              k &&
                ((y = he(k, u, y, w, 0, c, A, f, C, 1)),
                (y = he(k, u, y, w, 1, c, A, m, T, 1)),
                (y = he(k, u, y, w, 2, c, A, g, I, 1)),
                (y = he(k, u, y, w, 3, c, A, b, M, 1)),
                (y = he(k, u, y, w, 4, c, A, x, F, 1)),
                (y = he(k, u, y, w, 5, c, A, p, L, 1))),
                (c = A),
                (f = C),
                (m = T),
                (g = I),
                (b = M),
                (x = F),
                (b = M),
                (o = v);
            }
            n.push(u);
          }
        if (e.path)
          for (let a in e.path) {
            let r = e.path[a],
              o = i.findPathConstraint(a);
            if (!o) throw new Error("Path constraint not found: " + a);
            let l = i.pathConstraints.indexOf(o);
            for (let h in r) {
              let u = r[h],
                c = u[0];
              if (!c) continue;
              let f = u.length;
              if (h === "position") {
                let m = new fi(f, f, l);
                n.push(Ye(u, m, 0, o.positionMode == 0 ? s : 1));
              } else if (h === "spacing") {
                let m = new mi(f, f, l);
                n.push(
                  Ye(u, m, 0, o.spacingMode == 0 || o.spacingMode == 1 ? s : 1)
                );
              } else if (h === "mix") {
                let m = new gi(f, f * 3, l),
                  g = E(c, "time", 0),
                  b = E(c, "mixRotate", 1),
                  x = E(c, "mixX", 1),
                  p = E(c, "mixY", x);
                for (let w = 0, y = 0; ; w++) {
                  m.setFrame(w, g, b, x, p);
                  let v = u[w + 1];
                  if (!v) {
                    m.shrink(y);
                    break;
                  }
                  let A = E(v, "time", 0),
                    C = E(v, "mixRotate", 1),
                    T = E(v, "mixX", 1),
                    I = E(v, "mixY", T),
                    M = c.curve;
                  M &&
                    ((y = he(M, m, y, w, 0, g, A, b, C, 1)),
                    (y = he(M, m, y, w, 1, g, A, x, T, 1)),
                    (y = he(M, m, y, w, 2, g, A, p, I, 1))),
                    (g = A),
                    (b = C),
                    (x = T),
                    (p = I),
                    (c = v);
                }
                n.push(m);
              }
            }
          }
        if (e.physics)
          for (let a in e.physics) {
            let r = e.physics[a],
              o = -1;
            if (a.length > 0) {
              let l = i.findPhysicsConstraint(a);
              if (!l) throw new Error("Physics constraint not found: " + a);
              o = i.physicsConstraints.indexOf(l);
            }
            for (let l in r) {
              let h = r[l],
                u = h[0];
              if (!u) continue;
              let c = h.length;
              if (l == "reset") {
                const m = new Ct(c, o);
                for (let g = 0; u != null; u = h[g + 1], g++)
                  m.setFrame(g, E(u, "time", 0));
                n.push(m);
                continue;
              }
              let f;
              if (l == "inertia") f = new pi(c, c, o);
              else if (l == "strength") f = new wi(c, c, o);
              else if (l == "damping") f = new xi(c, c, o);
              else if (l == "mass") f = new bi(c, c, o);
              else if (l == "wind") f = new vi(c, c, o);
              else if (l == "gravity") f = new yi(c, c, o);
              else if (l == "mix") f = new Ai(c, c, o);
              else continue;
              n.push(Ye(h, f, 0, 1));
            }
          }
        if (e.attachments)
          for (let a in e.attachments) {
            let r = e.attachments[a],
              o = i.findSkin(a);
            if (!o) throw new Error("Skin not found: " + a);
            for (let l in r) {
              let h = r[l],
                u = i.findSlot(l);
              if (!u) throw new Error("Slot not found: " + l);
              let c = u.index;
              for (let f in h) {
                let m = h[f],
                  g = o.getAttachment(c, f);
                for (let b in m) {
                  let x = m[b],
                    p = x[0];
                  if (p) {
                    if (b == "deform") {
                      let w = g.bones,
                        y = g.vertices,
                        v = w ? (y.length / 3) * 2 : y.length,
                        A = new di(x.length, x.length, c, g),
                        C = E(p, "time", 0);
                      for (let T = 0, I = 0; ; T++) {
                        let M,
                          F = E(p, "vertices", null);
                        if (!F) M = w ? P.newFloatArray(v) : y;
                        else {
                          M = P.newFloatArray(v);
                          let Y = E(p, "offset", 0);
                          if ((P.arrayCopy(F, 0, M, Y, F.length), s != 1))
                            for (let ie = Y, se = ie + F.length; ie < se; ie++)
                              M[ie] *= s;
                          if (!w) for (let ie = 0; ie < v; ie++) M[ie] += y[ie];
                        }
                        A.setFrame(T, C, M);
                        let L = x[T + 1];
                        if (!L) {
                          A.shrink(I);
                          break;
                        }
                        let k = E(L, "time", 0),
                          R = p.curve;
                        R && (I = he(R, A, I, T, 0, C, k, 0, 1, 1)),
                          (C = k),
                          (p = L);
                      }
                      n.push(A);
                    } else if (b == "sequence") {
                      let w = new et(x.length, c, g),
                        y = 0;
                      for (let v = 0; v < x.length; v++) {
                        let A = E(p, "delay", y),
                          C = E(p, "time", 0),
                          T = ns[E(p, "mode", "hold")],
                          I = E(p, "index", 0);
                        w.setFrame(v, C, T, I, A), (y = A), (p = x[v + 1]);
                      }
                      n.push(w);
                    }
                  }
                }
              }
            }
          }
        if (e.drawOrder) {
          let a = new je(e.drawOrder.length),
            r = i.slots.length,
            o = 0;
          for (let l = 0; l < e.drawOrder.length; l++, o++) {
            let h = e.drawOrder[l],
              u = null,
              c = E(h, "offsets", null);
            if (c) {
              u = P.newArray(r, -1);
              let f = P.newArray(r - c.length, 0),
                m = 0,
                g = 0;
              for (let b = 0; b < c.length; b++) {
                let x = c[b],
                  p = i.findSlot(x.slot);
                if (!p) throw new Error("Slot not found: " + p);
                let w = p.index;
                for (; m != w; ) f[g++] = m++;
                u[m + x.offset] = m++;
              }
              for (; m < r; ) f[g++] = m++;
              for (let b = r - 1; b >= 0; b--) u[b] == -1 && (u[b] = f[--g]);
            }
            a.setFrame(o, E(h, "time", 0), u);
          }
          n.push(a);
        }
        if (e.events) {
          let a = new ht(e.events.length),
            r = 0;
          for (let o = 0; o < e.events.length; o++, r++) {
            let l = e.events[o],
              h = i.findEvent(l.name);
            if (!h) throw new Error("Event not found: " + l.name);
            let u = new Li(P.toSinglePrecision(E(l, "time", 0)), h);
            (u.intValue = E(l, "int", h.intValue)),
              (u.floatValue = E(l, "float", h.floatValue)),
              (u.stringValue = E(l, "string", h.stringValue)),
              u.data.audioPath &&
                ((u.volume = E(l, "volume", 1)),
                (u.balance = E(l, "balance", 0))),
              a.setFrame(r, u);
          }
          n.push(a);
        }
        let d = 0;
        for (let a = 0, r = n.length; a < r; a++)
          d = Math.max(d, n[a].getDuration());
        i.animations.push(new yt(t, n, d));
      }
    },
    Gr = class {
      parent;
      skin;
      slotIndex;
      mesh;
      inheritTimeline;
      constructor(e, t, i, s, n) {
        (this.mesh = e),
          (this.skin = t),
          (this.slotIndex = i),
          (this.parent = s),
          (this.inheritTimeline = n);
      }
    };
  function Ye(e, t, i, s) {
    let n = e[0],
      d = E(n, "time", 0),
      a = E(n, "value", i) * s,
      r = 0;
    for (let o = 0; ; o++) {
      t.setFrame(o, d, a);
      let l = e[o + 1];
      if (!l) return t.shrink(r), t;
      let h = E(l, "time", 0),
        u = E(l, "value", i) * s;
      n.curve && (r = he(n.curve, t, r, o, 0, d, h, a, u, s)),
        (d = h),
        (a = u),
        (n = l);
    }
  }
  function Wi(e, t, i, s, n, d) {
    let a = e[0],
      r = E(a, "time", 0),
      o = E(a, i, n) * d,
      l = E(a, s, n) * d,
      h = 0;
    for (let u = 0; ; u++) {
      t.setFrame(u, r, o, l);
      let c = e[u + 1];
      if (!c) return t.shrink(h), t;
      let f = E(c, "time", 0),
        m = E(c, i, n) * d,
        g = E(c, s, n) * d,
        b = a.curve;
      b &&
        ((h = he(b, t, h, u, 0, r, f, o, m, d)),
        (h = he(b, t, h, u, 1, r, f, l, g, d))),
        (r = f),
        (o = m),
        (l = g),
        (a = c);
    }
  }
  function he(e, t, i, s, n, d, a, r, o, l) {
    if (e == "stepped") return t.setStepped(s), i;
    let h = n << 2,
      u = e[h],
      c = e[h + 1] * l,
      f = e[h + 2],
      m = e[h + 3] * l;
    return t.setBezier(i, s, n, d, r, u, c, f, m, a, o), i + 1;
  }
  function E(e, t, i) {
    return e[t] !== void 0 ? e[t] : i;
  }
  typeof Math.fround > "u" &&
    (Math.fround = (function (e) {
      return function (t) {
        return (e[0] = t), e[0];
      };
    })(new Float32Array(1)));
  var pe = class {
      canvas;
      gl;
      restorables = new Array();
      constructor(e, t = { alpha: "true" }) {
        if (
          e instanceof WebGLRenderingContext ||
          (typeof WebGL2RenderingContext < "u" &&
            e instanceof WebGL2RenderingContext)
        )
          (this.gl = e), (this.canvas = this.gl.canvas);
        else {
          let i = e;
          (this.gl = i.getContext("webgl2", t) || i.getContext("webgl", t)),
            (this.canvas = i),
            i.addEventListener("webglcontextlost", (s) => {
              let n = s;
              s && s.preventDefault();
            }),
            i.addEventListener("webglcontextrestored", (s) => {
              for (let n = 0, d = this.restorables.length; n < d; n++)
                this.restorables[n].restore();
            });
        }
      }
      addRestorable(e) {
        this.restorables.push(e);
      }
      removeRestorable(e) {
        let t = this.restorables.indexOf(e);
        t > -1 && this.restorables.splice(t, 1);
      }
    },
    Pt = class extends Ei {
      context;
      texture = null;
      boundUnit = 0;
      useMipMaps = !1;
      constructor(e, t, i = !1) {
        super(t),
          (this.context = e instanceof pe ? e : new pe(e)),
          (this.useMipMaps = i),
          this.restore(),
          this.context.addRestorable(this);
      }
      setFilters(e, t) {
        let i = this.context.gl;
        this.bind(),
          i.texParameteri(i.TEXTURE_2D, i.TEXTURE_MIN_FILTER, e),
          i.texParameteri(
            i.TEXTURE_2D,
            i.TEXTURE_MAG_FILTER,
            Pt.validateMagFilter(t)
          ),
          (this.useMipMaps = Pt.usesMipMaps(e)),
          this.useMipMaps && i.generateMipmap(i.TEXTURE_2D);
      }
      static validateMagFilter(e) {
        switch (e) {
          case 9987:
          case 9985:
          case 9986:
          case 9984:
            return 9729;
          default:
            return e;
        }
      }
      static usesMipMaps(e) {
        switch (e) {
          case 9987:
          case 9985:
          case 9986:
          case 9984:
            return !0;
          default:
            return !1;
        }
      }
      setWraps(e, t) {
        let i = this.context.gl;
        this.bind(),
          i.texParameteri(i.TEXTURE_2D, i.TEXTURE_WRAP_S, e),
          i.texParameteri(i.TEXTURE_2D, i.TEXTURE_WRAP_T, t);
      }
      update(e) {
        let t = this.context.gl;
        this.texture || (this.texture = this.context.gl.createTexture()),
          this.bind(),
          Pt.DISABLE_UNPACK_PREMULTIPLIED_ALPHA_WEBGL &&
            t.pixelStorei(t.UNPACK_PREMULTIPLY_ALPHA_WEBGL, !1),
          t.texImage2D(
            t.TEXTURE_2D,
            0,
            t.RGBA,
            t.RGBA,
            t.UNSIGNED_BYTE,
            this._image
          ),
          t.texParameteri(t.TEXTURE_2D, t.TEXTURE_MAG_FILTER, t.LINEAR),
          t.texParameteri(
            t.TEXTURE_2D,
            t.TEXTURE_MIN_FILTER,
            e ? t.LINEAR_MIPMAP_LINEAR : t.LINEAR
          ),
          t.texParameteri(t.TEXTURE_2D, t.TEXTURE_WRAP_S, t.CLAMP_TO_EDGE),
          t.texParameteri(t.TEXTURE_2D, t.TEXTURE_WRAP_T, t.CLAMP_TO_EDGE),
          e && t.generateMipmap(t.TEXTURE_2D);
      }
      restore() {
        (this.texture = null), this.update(this.useMipMaps);
      }
      bind(e = 0) {
        let t = this.context.gl;
        (this.boundUnit = e),
          t.activeTexture(t.TEXTURE0 + e),
          t.bindTexture(t.TEXTURE_2D, this.texture);
      }
      unbind() {
        let e = this.context.gl;
        e.activeTexture(e.TEXTURE0 + this.boundUnit),
          e.bindTexture(e.TEXTURE_2D, null);
      }
      dispose() {
        this.context.removeRestorable(this),
          this.context.gl.deleteTexture(this.texture);
      }
    },
    ft = Pt;
  B(ft, "DISABLE_UNPACK_PREMULTIPLIED_ALPHA_WEBGL", !1);
  var zi = class extends Cs {
      constructor(e, t = "", i = new Yi()) {
        super((s) => new ft(e, s), t, i);
      }
    },
    Se = class {
      x = 0;
      y = 0;
      z = 0;
      constructor(e = 0, t = 0, i = 0) {
        (this.x = e), (this.y = t), (this.z = i);
      }
      setFrom(e) {
        return (this.x = e.x), (this.y = e.y), (this.z = e.z), this;
      }
      set(e, t, i) {
        return (this.x = e), (this.y = t), (this.z = i), this;
      }
      add(e) {
        return (this.x += e.x), (this.y += e.y), (this.z += e.z), this;
      }
      sub(e) {
        return (this.x -= e.x), (this.y -= e.y), (this.z -= e.z), this;
      }
      scale(e) {
        return (this.x *= e), (this.y *= e), (this.z *= e), this;
      }
      normalize() {
        let e = this.length();
        return e == 0
          ? this
          : ((e = 1 / e), (this.x *= e), (this.y *= e), (this.z *= e), this);
      }
      cross(e) {
        return this.set(
          this.y * e.z - this.z * e.y,
          this.z * e.x - this.x * e.z,
          this.x * e.y - this.y * e.x
        );
      }
      multiply(e) {
        let t = e.values;
        return this.set(
          this.x * t[O] + this.y * t[z] + this.z * t[q] + t[D],
          this.x * t[G] + this.y * t[N] + this.z * t[H] + t[U],
          this.x * t[j] + this.y * t[Z] + this.z * t[_] + t[W]
        );
      }
      project(e) {
        let t = e.values,
          i = 1 / (this.x * t[Q] + this.y * t[$] + this.z * t[ee] + t[K]);
        return this.set(
          (this.x * t[O] + this.y * t[z] + this.z * t[q] + t[D]) * i,
          (this.x * t[G] + this.y * t[N] + this.z * t[H] + t[U]) * i,
          (this.x * t[j] + this.y * t[Z] + this.z * t[_] + t[W]) * i
        );
      }
      dot(e) {
        return this.x * e.x + this.y * e.y + this.z * e.z;
      }
      length() {
        return Math.sqrt(this.x * this.x + this.y * this.y + this.z * this.z);
      }
      distance(e) {
        let t = e.x - this.x,
          i = e.y - this.y,
          s = e.z - this.z;
        return Math.sqrt(t * t + i * i + s * s);
      }
    },
    O = 0,
    z = 4,
    q = 8,
    D = 12,
    G = 1,
    N = 5,
    H = 9,
    U = 13,
    j = 2,
    Z = 6,
    _ = 10,
    W = 14,
    Q = 3,
    $ = 7,
    ee = 11,
    K = 15,
    Le = class {
      temp = new Float32Array(16);
      values = new Float32Array(16);
      constructor() {
        let e = this.values;
        (e[O] = 1), (e[N] = 1), (e[_] = 1), (e[K] = 1);
      }
      set(e) {
        return this.values.set(e), this;
      }
      transpose() {
        let e = this.temp,
          t = this.values;
        return (
          (e[O] = t[O]),
          (e[z] = t[G]),
          (e[q] = t[j]),
          (e[D] = t[Q]),
          (e[G] = t[z]),
          (e[N] = t[N]),
          (e[H] = t[Z]),
          (e[U] = t[$]),
          (e[j] = t[q]),
          (e[Z] = t[H]),
          (e[_] = t[_]),
          (e[W] = t[ee]),
          (e[Q] = t[D]),
          (e[$] = t[U]),
          (e[ee] = t[W]),
          (e[K] = t[K]),
          this.set(e)
        );
      }
      identity() {
        let e = this.values;
        return (
          (e[O] = 1),
          (e[z] = 0),
          (e[q] = 0),
          (e[D] = 0),
          (e[G] = 0),
          (e[N] = 1),
          (e[H] = 0),
          (e[U] = 0),
          (e[j] = 0),
          (e[Z] = 0),
          (e[_] = 1),
          (e[W] = 0),
          (e[Q] = 0),
          (e[$] = 0),
          (e[ee] = 0),
          (e[K] = 1),
          this
        );
      }
      invert() {
        let e = this.values,
          t = this.temp,
          i =
            e[Q] * e[Z] * e[H] * e[D] -
            e[j] * e[$] * e[H] * e[D] -
            e[Q] * e[N] * e[_] * e[D] +
            e[G] * e[$] * e[_] * e[D] +
            e[j] * e[N] * e[ee] * e[D] -
            e[G] * e[Z] * e[ee] * e[D] -
            e[Q] * e[Z] * e[q] * e[U] +
            e[j] * e[$] * e[q] * e[U] +
            e[Q] * e[z] * e[_] * e[U] -
            e[O] * e[$] * e[_] * e[U] -
            e[j] * e[z] * e[ee] * e[U] +
            e[O] * e[Z] * e[ee] * e[U] +
            e[Q] * e[N] * e[q] * e[W] -
            e[G] * e[$] * e[q] * e[W] -
            e[Q] * e[z] * e[H] * e[W] +
            e[O] * e[$] * e[H] * e[W] +
            e[G] * e[z] * e[ee] * e[W] -
            e[O] * e[N] * e[ee] * e[W] -
            e[j] * e[N] * e[q] * e[K] +
            e[G] * e[Z] * e[q] * e[K] +
            e[j] * e[z] * e[H] * e[K] -
            e[O] * e[Z] * e[H] * e[K] -
            e[G] * e[z] * e[_] * e[K] +
            e[O] * e[N] * e[_] * e[K];
        if (i == 0) throw new Error("non-invertible matrix");
        let s = 1 / i;
        return (
          (t[O] =
            e[H] * e[W] * e[$] -
            e[U] * e[_] * e[$] +
            e[U] * e[Z] * e[ee] -
            e[N] * e[W] * e[ee] -
            e[H] * e[Z] * e[K] +
            e[N] * e[_] * e[K]),
          (t[z] =
            e[D] * e[_] * e[$] -
            e[q] * e[W] * e[$] -
            e[D] * e[Z] * e[ee] +
            e[z] * e[W] * e[ee] +
            e[q] * e[Z] * e[K] -
            e[z] * e[_] * e[K]),
          (t[q] =
            e[q] * e[U] * e[$] -
            e[D] * e[H] * e[$] +
            e[D] * e[N] * e[ee] -
            e[z] * e[U] * e[ee] -
            e[q] * e[N] * e[K] +
            e[z] * e[H] * e[K]),
          (t[D] =
            e[D] * e[H] * e[Z] -
            e[q] * e[U] * e[Z] -
            e[D] * e[N] * e[_] +
            e[z] * e[U] * e[_] +
            e[q] * e[N] * e[W] -
            e[z] * e[H] * e[W]),
          (t[G] =
            e[U] * e[_] * e[Q] -
            e[H] * e[W] * e[Q] -
            e[U] * e[j] * e[ee] +
            e[G] * e[W] * e[ee] +
            e[H] * e[j] * e[K] -
            e[G] * e[_] * e[K]),
          (t[N] =
            e[q] * e[W] * e[Q] -
            e[D] * e[_] * e[Q] +
            e[D] * e[j] * e[ee] -
            e[O] * e[W] * e[ee] -
            e[q] * e[j] * e[K] +
            e[O] * e[_] * e[K]),
          (t[H] =
            e[D] * e[H] * e[Q] -
            e[q] * e[U] * e[Q] -
            e[D] * e[G] * e[ee] +
            e[O] * e[U] * e[ee] +
            e[q] * e[G] * e[K] -
            e[O] * e[H] * e[K]),
          (t[U] =
            e[q] * e[U] * e[j] -
            e[D] * e[H] * e[j] +
            e[D] * e[G] * e[_] -
            e[O] * e[U] * e[_] -
            e[q] * e[G] * e[W] +
            e[O] * e[H] * e[W]),
          (t[j] =
            e[N] * e[W] * e[Q] -
            e[U] * e[Z] * e[Q] +
            e[U] * e[j] * e[$] -
            e[G] * e[W] * e[$] -
            e[N] * e[j] * e[K] +
            e[G] * e[Z] * e[K]),
          (t[Z] =
            e[D] * e[Z] * e[Q] -
            e[z] * e[W] * e[Q] -
            e[D] * e[j] * e[$] +
            e[O] * e[W] * e[$] +
            e[z] * e[j] * e[K] -
            e[O] * e[Z] * e[K]),
          (t[_] =
            e[z] * e[U] * e[Q] -
            e[D] * e[N] * e[Q] +
            e[D] * e[G] * e[$] -
            e[O] * e[U] * e[$] -
            e[z] * e[G] * e[K] +
            e[O] * e[N] * e[K]),
          (t[W] =
            e[D] * e[N] * e[j] -
            e[z] * e[U] * e[j] -
            e[D] * e[G] * e[Z] +
            e[O] * e[U] * e[Z] +
            e[z] * e[G] * e[W] -
            e[O] * e[N] * e[W]),
          (t[Q] =
            e[H] * e[Z] * e[Q] -
            e[N] * e[_] * e[Q] -
            e[H] * e[j] * e[$] +
            e[G] * e[_] * e[$] +
            e[N] * e[j] * e[ee] -
            e[G] * e[Z] * e[ee]),
          (t[$] =
            e[z] * e[_] * e[Q] -
            e[q] * e[Z] * e[Q] +
            e[q] * e[j] * e[$] -
            e[O] * e[_] * e[$] -
            e[z] * e[j] * e[ee] +
            e[O] * e[Z] * e[ee]),
          (t[ee] =
            e[q] * e[N] * e[Q] -
            e[z] * e[H] * e[Q] -
            e[q] * e[G] * e[$] +
            e[O] * e[H] * e[$] +
            e[z] * e[G] * e[ee] -
            e[O] * e[N] * e[ee]),
          (t[K] =
            e[z] * e[H] * e[j] -
            e[q] * e[N] * e[j] +
            e[q] * e[G] * e[Z] -
            e[O] * e[H] * e[Z] -
            e[z] * e[G] * e[_] +
            e[O] * e[N] * e[_]),
          (e[O] = t[O] * s),
          (e[z] = t[z] * s),
          (e[q] = t[q] * s),
          (e[D] = t[D] * s),
          (e[G] = t[G] * s),
          (e[N] = t[N] * s),
          (e[H] = t[H] * s),
          (e[U] = t[U] * s),
          (e[j] = t[j] * s),
          (e[Z] = t[Z] * s),
          (e[_] = t[_] * s),
          (e[W] = t[W] * s),
          (e[Q] = t[Q] * s),
          (e[$] = t[$] * s),
          (e[ee] = t[ee] * s),
          (e[K] = t[K] * s),
          this
        );
      }
      determinant() {
        let e = this.values;
        return (
          e[Q] * e[Z] * e[H] * e[D] -
          e[j] * e[$] * e[H] * e[D] -
          e[Q] * e[N] * e[_] * e[D] +
          e[G] * e[$] * e[_] * e[D] +
          e[j] * e[N] * e[ee] * e[D] -
          e[G] * e[Z] * e[ee] * e[D] -
          e[Q] * e[Z] * e[q] * e[U] +
          e[j] * e[$] * e[q] * e[U] +
          e[Q] * e[z] * e[_] * e[U] -
          e[O] * e[$] * e[_] * e[U] -
          e[j] * e[z] * e[ee] * e[U] +
          e[O] * e[Z] * e[ee] * e[U] +
          e[Q] * e[N] * e[q] * e[W] -
          e[G] * e[$] * e[q] * e[W] -
          e[Q] * e[z] * e[H] * e[W] +
          e[O] * e[$] * e[H] * e[W] +
          e[G] * e[z] * e[ee] * e[W] -
          e[O] * e[N] * e[ee] * e[W] -
          e[j] * e[N] * e[q] * e[K] +
          e[G] * e[Z] * e[q] * e[K] +
          e[j] * e[z] * e[H] * e[K] -
          e[O] * e[Z] * e[H] * e[K] -
          e[G] * e[z] * e[_] * e[K] +
          e[O] * e[N] * e[_] * e[K]
        );
      }
      translate(e, t, i) {
        let s = this.values;
        return (s[D] += e), (s[U] += t), (s[W] += i), this;
      }
      copy() {
        return new Le().set(this.values);
      }
      projection(e, t, i, s) {
        this.identity();
        let n = 1 / Math.tan((i * (Math.PI / 180)) / 2),
          d = (t + e) / (e - t),
          a = (2 * t * e) / (e - t),
          r = this.values;
        return (
          (r[O] = n / s),
          (r[G] = 0),
          (r[j] = 0),
          (r[Q] = 0),
          (r[z] = 0),
          (r[N] = n),
          (r[Z] = 0),
          (r[$] = 0),
          (r[q] = 0),
          (r[H] = 0),
          (r[_] = d),
          (r[ee] = -1),
          (r[D] = 0),
          (r[U] = 0),
          (r[W] = a),
          (r[K] = 0),
          this
        );
      }
      ortho2d(e, t, i, s) {
        return this.ortho(e, e + i, t, t + s, 0, 1);
      }
      ortho(e, t, i, s, n, d) {
        this.identity();
        let a = 2 / (t - e),
          r = 2 / (s - i),
          o = -2 / (d - n),
          l = -(t + e) / (t - e),
          h = -(s + i) / (s - i),
          u = -(d + n) / (d - n),
          c = this.values;
        return (
          (c[O] = a),
          (c[G] = 0),
          (c[j] = 0),
          (c[Q] = 0),
          (c[z] = 0),
          (c[N] = r),
          (c[Z] = 0),
          (c[$] = 0),
          (c[q] = 0),
          (c[H] = 0),
          (c[_] = o),
          (c[ee] = 0),
          (c[D] = l),
          (c[U] = h),
          (c[W] = u),
          (c[K] = 1),
          this
        );
      }
      multiply(e) {
        let t = this.temp,
          i = this.values,
          s = e.values;
        return (
          (t[O] = i[O] * s[O] + i[z] * s[G] + i[q] * s[j] + i[D] * s[Q]),
          (t[z] = i[O] * s[z] + i[z] * s[N] + i[q] * s[Z] + i[D] * s[$]),
          (t[q] = i[O] * s[q] + i[z] * s[H] + i[q] * s[_] + i[D] * s[ee]),
          (t[D] = i[O] * s[D] + i[z] * s[U] + i[q] * s[W] + i[D] * s[K]),
          (t[G] = i[G] * s[O] + i[N] * s[G] + i[H] * s[j] + i[U] * s[Q]),
          (t[N] = i[G] * s[z] + i[N] * s[N] + i[H] * s[Z] + i[U] * s[$]),
          (t[H] = i[G] * s[q] + i[N] * s[H] + i[H] * s[_] + i[U] * s[ee]),
          (t[U] = i[G] * s[D] + i[N] * s[U] + i[H] * s[W] + i[U] * s[K]),
          (t[j] = i[j] * s[O] + i[Z] * s[G] + i[_] * s[j] + i[W] * s[Q]),
          (t[Z] = i[j] * s[z] + i[Z] * s[N] + i[_] * s[Z] + i[W] * s[$]),
          (t[_] = i[j] * s[q] + i[Z] * s[H] + i[_] * s[_] + i[W] * s[ee]),
          (t[W] = i[j] * s[D] + i[Z] * s[U] + i[_] * s[W] + i[W] * s[K]),
          (t[Q] = i[Q] * s[O] + i[$] * s[G] + i[ee] * s[j] + i[K] * s[Q]),
          (t[$] = i[Q] * s[z] + i[$] * s[N] + i[ee] * s[Z] + i[K] * s[$]),
          (t[ee] = i[Q] * s[q] + i[$] * s[H] + i[ee] * s[_] + i[K] * s[ee]),
          (t[K] = i[Q] * s[D] + i[$] * s[U] + i[ee] * s[W] + i[K] * s[K]),
          this.set(this.temp)
        );
      }
      multiplyLeft(e) {
        let t = this.temp,
          i = this.values,
          s = e.values;
        return (
          (t[O] = s[O] * i[O] + s[z] * i[G] + s[q] * i[j] + s[D] * i[Q]),
          (t[z] = s[O] * i[z] + s[z] * i[N] + s[q] * i[Z] + s[D] * i[$]),
          (t[q] = s[O] * i[q] + s[z] * i[H] + s[q] * i[_] + s[D] * i[ee]),
          (t[D] = s[O] * i[D] + s[z] * i[U] + s[q] * i[W] + s[D] * i[K]),
          (t[G] = s[G] * i[O] + s[N] * i[G] + s[H] * i[j] + s[U] * i[Q]),
          (t[N] = s[G] * i[z] + s[N] * i[N] + s[H] * i[Z] + s[U] * i[$]),
          (t[H] = s[G] * i[q] + s[N] * i[H] + s[H] * i[_] + s[U] * i[ee]),
          (t[U] = s[G] * i[D] + s[N] * i[U] + s[H] * i[W] + s[U] * i[K]),
          (t[j] = s[j] * i[O] + s[Z] * i[G] + s[_] * i[j] + s[W] * i[Q]),
          (t[Z] = s[j] * i[z] + s[Z] * i[N] + s[_] * i[Z] + s[W] * i[$]),
          (t[_] = s[j] * i[q] + s[Z] * i[H] + s[_] * i[_] + s[W] * i[ee]),
          (t[W] = s[j] * i[D] + s[Z] * i[U] + s[_] * i[W] + s[W] * i[K]),
          (t[Q] = s[Q] * i[O] + s[$] * i[G] + s[ee] * i[j] + s[K] * i[Q]),
          (t[$] = s[Q] * i[z] + s[$] * i[N] + s[ee] * i[Z] + s[K] * i[$]),
          (t[ee] = s[Q] * i[q] + s[$] * i[H] + s[ee] * i[_] + s[K] * i[ee]),
          (t[K] = s[Q] * i[D] + s[$] * i[U] + s[ee] * i[W] + s[K] * i[K]),
          this.set(this.temp)
        );
      }
      lookAt(e, t, i) {
        let s = Le.xAxis,
          n = Le.yAxis,
          d = Le.zAxis;
        d.setFrom(t).normalize(),
          s.setFrom(t).normalize(),
          s.cross(i).normalize(),
          n.setFrom(s).cross(d).normalize(),
          this.identity();
        let a = this.values;
        return (
          (a[O] = s.x),
          (a[z] = s.y),
          (a[q] = s.z),
          (a[G] = n.x),
          (a[N] = n.y),
          (a[H] = n.z),
          (a[j] = -d.x),
          (a[Z] = -d.y),
          (a[_] = -d.z),
          Le.tmpMatrix.identity(),
          (Le.tmpMatrix.values[D] = -e.x),
          (Le.tmpMatrix.values[U] = -e.y),
          (Le.tmpMatrix.values[W] = -e.z),
          this.multiply(Le.tmpMatrix),
          this
        );
      }
    },
    Be = Le;
  B(Be, "xAxis", new Se()),
    B(Be, "yAxis", new Se()),
    B(Be, "zAxis", new Se()),
    B(Be, "tmpMatrix", new Le());
  var Ls = class {
      position = new Se(0, 0, 0);
      direction = new Se(0, 0, -1);
      up = new Se(0, 1, 0);
      near = 0;
      far = 100;
      zoom = 1;
      viewportWidth = 0;
      viewportHeight = 0;
      projectionView = new Be();
      inverseProjectionView = new Be();
      projection = new Be();
      view = new Be();
      constructor(e, t) {
        (this.viewportWidth = e), (this.viewportHeight = t), this.update();
      }
      update() {
        let e = this.projection,
          t = this.view,
          i = this.projectionView,
          s = this.inverseProjectionView,
          n = this.zoom,
          d = this.viewportWidth,
          a = this.viewportHeight;
        e.ortho(
          n * (-d / 2),
          n * (d / 2),
          n * (-a / 2),
          n * (a / 2),
          this.near,
          this.far
        ),
          t.lookAt(this.position, this.direction, this.up),
          i.set(e.values),
          i.multiply(t),
          s.set(i.values).invert();
      }
      screenToWorld(e, t, i) {
        let s = e.x,
          n = i - e.y - 1;
        return (
          (e.x = (2 * s) / t - 1),
          (e.y = (2 * n) / i - 1),
          (e.z = 2 * e.z - 1),
          e.project(this.inverseProjectionView),
          e
        );
      }
      worldToScreen(e, t, i) {
        return (
          e.project(this.projectionView),
          (e.x = (t * (e.x + 1)) / 2),
          (e.y = (i * (e.y + 1)) / 2),
          (e.z = (e.z + 1) / 2),
          e
        );
      }
      setViewport(e, t) {
        (this.viewportWidth = e), (this.viewportHeight = t);
      }
    },
    mt = class {
      element;
      mouseX = 0;
      mouseY = 0;
      buttonDown = !1;
      touch0 = null;
      touch1 = null;
      initialPinchDistance = 0;
      listeners = new Array();
      eventListeners = [];
      constructor(e) {
        (this.element = e), this.setupCallbacks(e);
      }
      setupCallbacks(e) {
        let t = (a) => {
            if (a instanceof MouseEvent) {
              let r = e.getBoundingClientRect();
              (this.mouseX = a.clientX - r.left),
                (this.mouseY = a.clientY - r.top),
                (this.buttonDown = !0),
                this.listeners.map((o) => {
                  o.down && o.down(this.mouseX, this.mouseY);
                }),
                document.addEventListener("mousemove", i),
                document.addEventListener("mouseup", s);
            }
          },
          i = (a) => {
            if (a instanceof MouseEvent) {
              let r = e.getBoundingClientRect();
              (this.mouseX = a.clientX - r.left),
                (this.mouseY = a.clientY - r.top),
                this.listeners.map((o) => {
                  this.buttonDown
                    ? o.dragged && o.dragged(this.mouseX, this.mouseY)
                    : o.moved && o.moved(this.mouseX, this.mouseY);
                });
            }
          },
          s = (a) => {
            if (a instanceof MouseEvent) {
              let r = e.getBoundingClientRect();
              (this.mouseX = a.clientX - r.left),
                (this.mouseY = a.clientY - r.top),
                (this.buttonDown = !1),
                this.listeners.map((o) => {
                  o.up && o.up(this.mouseX, this.mouseY);
                }),
                document.removeEventListener("mousemove", i),
                document.removeEventListener("mouseup", s);
            }
          },
          n = (a) => {
            a.preventDefault();
            let r = a.deltaY;
            a.deltaMode == WheelEvent.DOM_DELTA_LINE && (r *= 8),
              a.deltaMode == WheelEvent.DOM_DELTA_PAGE && (r *= 24),
              this.listeners.map((o) => {
                o.wheel && o.wheel(a.deltaY);
              });
          };
        e.addEventListener("mousedown", t, !0),
          e.addEventListener("mousemove", i, !0),
          e.addEventListener("mouseup", s, !0),
          e.addEventListener("wheel", n, !0),
          e.addEventListener(
            "touchstart",
            (a) => {
              if (!this.touch0 || !this.touch1) {
                var r = a.changedTouches;
                let o = r.item(0);
                if (!o) return;
                let l = e.getBoundingClientRect(),
                  h = o.clientX - l.left,
                  u = o.clientY - l.top,
                  c = new Xs(o.identifier, h, u);
                if (
                  ((this.mouseX = h),
                  (this.mouseY = u),
                  (this.buttonDown = !0),
                  !this.touch0)
                )
                  (this.touch0 = c),
                    this.listeners.map((f) => {
                      f.down && f.down(c.x, c.y);
                    });
                else if (!this.touch1) {
                  this.touch1 = c;
                  let f = this.touch1.x - this.touch0.x,
                    m = this.touch1.x - this.touch0.x;
                  (this.initialPinchDistance = Math.sqrt(f * f + m * m)),
                    this.listeners.map((g) => {
                      g.zoom &&
                        g.zoom(
                          this.initialPinchDistance,
                          this.initialPinchDistance
                        );
                    });
                }
              }
              a.preventDefault();
            },
            !1
          ),
          e.addEventListener(
            "touchmove",
            (a) => {
              if (this.touch0) {
                var r = a.changedTouches;
                let h = e.getBoundingClientRect();
                for (var o = 0; o < r.length; o++) {
                  var l = r[o];
                  let u = l.clientX - h.left,
                    c = l.clientY - h.top;
                  this.touch0.identifier === l.identifier &&
                    ((this.touch0.x = this.mouseX = u),
                    (this.touch0.y = this.mouseY = c),
                    this.listeners.map((f) => {
                      f.dragged && f.dragged(u, c);
                    })),
                    this.touch1 &&
                      this.touch1.identifier === l.identifier &&
                      ((this.touch1.x = this.mouseX = u),
                      (this.touch1.y = this.mouseY = c));
                }
                if (this.touch0 && this.touch1) {
                  let u = this.touch1.x - this.touch0.x,
                    c = this.touch1.x - this.touch0.x,
                    f = Math.sqrt(u * u + c * c);
                  this.listeners.map((m) => {
                    m.zoom && m.zoom(this.initialPinchDistance, f);
                  });
                }
              }
              a.preventDefault();
            },
            !1
          );
        let d = (a) => {
          if (this.touch0) {
            var r = a.changedTouches;
            let h = e.getBoundingClientRect();
            for (var o = 0; o < r.length; o++) {
              var l = r[o];
              let u = l.clientX - h.left,
                c = l.clientY - h.top;
              if (this.touch0.identifier === l.identifier)
                if (
                  ((this.touch0 = null),
                  (this.mouseX = u),
                  (this.mouseY = c),
                  this.listeners.map((f) => {
                    f.up && f.up(u, c);
                  }),
                  this.touch1)
                )
                  (this.touch0 = this.touch1),
                    (this.touch1 = null),
                    (this.mouseX = this.touch0.x),
                    (this.mouseX = this.touch0.x),
                    (this.buttonDown = !0),
                    this.listeners.map((f) => {
                      f.down && f.down(this.touch0.x, this.touch0.y);
                    });
                else {
                  this.buttonDown = !1;
                  break;
                }
              this.touch1 && this.touch1.identifier && (this.touch1 = null);
            }
          }
          a.preventDefault();
        };
        e.addEventListener("touchend", d, !1),
          e.addEventListener("touchcancel", d);
      }
      addListener(e) {
        this.listeners.push(e);
      }
      removeListener(e) {
        let t = this.listeners.indexOf(e);
        t > -1 && this.listeners.splice(t, 1);
      }
    },
    Xs = class {
      constructor(e, t, i) {
        (this.identifier = e), (this.x = t), (this.y = i);
      }
    },
    Hr = class {
      constructor(e, t) {
        (this.canvas = e), (this.camera = t);
        let i = 0,
          s = 0,
          n = 0,
          d = 0,
          a = 0,
          r = 0,
          o = 0,
          l = 0;
        new mt(e).addListener({
          down: (h, u) => {
            (i = t.position.x),
              (s = t.position.y),
              (d = r = h),
              (a = o = u),
              (l = t.zoom);
          },
          dragged: (h, u) => {
            let c = h - d,
              f = u - a,
              m = t.screenToWorld(new Se(0, 0), e.clientWidth, e.clientHeight),
              g = t
                .screenToWorld(new Se(c, f), e.clientWidth, e.clientHeight)
                .sub(m);
            t.position.set(i - g.x, s - g.y, 0), t.update(), (r = h), (o = u);
          },
          wheel: (h) => {
            let u = (h / 200) * t.zoom,
              c = t.zoom + u;
            if (c > 0) {
              let f = 0,
                m = 0;
              if (h < 0) (f = r), (m = o);
              else {
                let x = new Se(e.clientWidth / 2 + 15, e.clientHeight / 2),
                  p = r - x.x,
                  w = e.clientHeight - 1 - o - x.y;
                (f = x.x - p), (m = e.clientHeight - 1 - x.y + w);
              }
              let g = t.screenToWorld(
                new Se(f, m),
                e.clientWidth,
                e.clientHeight
              );
              (t.zoom = c), t.update();
              let b = t.screenToWorld(
                new Se(f, m),
                e.clientWidth,
                e.clientHeight
              );
              t.position.add(g.sub(b)), t.update();
            }
          },
          zoom: (h, u) => {
            let c = h / u;
            t.zoom = l * c;
          },
          up: (h, u) => {
            (r = h), (o = u);
          },
          moved: (h, u) => {
            (r = h), (o = u);
          },
        });
      }
    },
    fe = class {
      constructor(e, t, i) {
        (this.vertexShader = t),
          (this.fragmentShader = i),
          (this.vsSource = t),
          (this.fsSource = i),
          (this.context = e instanceof pe ? e : new pe(e)),
          this.context.addRestorable(this),
          this.compile();
      }
      context;
      vs = null;
      vsSource;
      fs = null;
      fsSource;
      program = null;
      tmp2x2 = new Float32Array(2 * 2);
      tmp3x3 = new Float32Array(3 * 3);
      tmp4x4 = new Float32Array(4 * 4);
      getProgram() {
        return this.program;
      }
      getVertexShader() {
        return this.vertexShader;
      }
      getFragmentShader() {
        return this.fragmentShader;
      }
      getVertexShaderSource() {
        return this.vsSource;
      }
      getFragmentSource() {
        return this.fsSource;
      }
      compile() {
        let e = this.context.gl;
        try {
          if (
            ((this.vs = this.compileShader(e.VERTEX_SHADER, this.vertexShader)),
            !this.vs)
          )
            throw new Error("Couldn't compile vertex shader.");
          if (
            ((this.fs = this.compileShader(
              e.FRAGMENT_SHADER,
              this.fragmentShader
            )),
            !this.fs)
          )
            throw new Error("Couldn#t compile fragment shader.");
          this.program = this.compileProgram(this.vs, this.fs);
        } catch (t) {
          throw (this.dispose(), t);
        }
      }
      compileShader(e, t) {
        let i = this.context.gl,
          s = i.createShader(e);
        if (!s) throw new Error("Couldn't create shader.");
        if (
          (i.shaderSource(s, t),
          i.compileShader(s),
          !i.getShaderParameter(s, i.COMPILE_STATUS))
        ) {
          let n = "Couldn't compile shader: " + i.getShaderInfoLog(s);
          if ((i.deleteShader(s), !i.isContextLost())) throw new Error(n);
        }
        return s;
      }
      compileProgram(e, t) {
        let i = this.context.gl,
          s = i.createProgram();
        if (!s) throw new Error("Couldn't compile program.");
        if (
          (i.attachShader(s, e),
          i.attachShader(s, t),
          i.linkProgram(s),
          !i.getProgramParameter(s, i.LINK_STATUS))
        ) {
          let n = "Couldn't compile shader program: " + i.getProgramInfoLog(s);
          if ((i.deleteProgram(s), !i.isContextLost())) throw new Error(n);
        }
        return s;
      }
      restore() {
        this.compile();
      }
      bind() {
        this.context.gl.useProgram(this.program);
      }
      unbind() {
        this.context.gl.useProgram(null);
      }
      setUniformi(e, t) {
        this.context.gl.uniform1i(this.getUniformLocation(e), t);
      }
      setUniformf(e, t) {
        this.context.gl.uniform1f(this.getUniformLocation(e), t);
      }
      setUniform2f(e, t, i) {
        this.context.gl.uniform2f(this.getUniformLocation(e), t, i);
      }
      setUniform3f(e, t, i, s) {
        this.context.gl.uniform3f(this.getUniformLocation(e), t, i, s);
      }
      setUniform4f(e, t, i, s, n) {
        this.context.gl.uniform4f(this.getUniformLocation(e), t, i, s, n);
      }
      setUniform2x2f(e, t) {
        let i = this.context.gl;
        this.tmp2x2.set(t),
          i.uniformMatrix2fv(this.getUniformLocation(e), !1, this.tmp2x2);
      }
      setUniform3x3f(e, t) {
        let i = this.context.gl;
        this.tmp3x3.set(t),
          i.uniformMatrix3fv(this.getUniformLocation(e), !1, this.tmp3x3);
      }
      setUniform4x4f(e, t) {
        let i = this.context.gl;
        this.tmp4x4.set(t),
          i.uniformMatrix4fv(this.getUniformLocation(e), !1, this.tmp4x4);
      }
      getUniformLocation(e) {
        let t = this.context.gl;
        if (!this.program) throw new Error("Shader not compiled.");
        let i = t.getUniformLocation(this.program, e);
        if (!i && !t.isContextLost())
          throw new Error(`Couldn't find location for uniform ${e}`);
        return i;
      }
      getAttributeLocation(e) {
        let t = this.context.gl;
        if (!this.program) throw new Error("Shader not compiled.");
        let i = t.getAttribLocation(this.program, e);
        if (i == -1 && !t.isContextLost())
          throw new Error(`Couldn't find location for attribute ${e}`);
        return i;
      }
      dispose() {
        this.context.removeRestorable(this);
        let e = this.context.gl;
        this.vs && (e.deleteShader(this.vs), (this.vs = null)),
          this.fs && (e.deleteShader(this.fs), (this.fs = null)),
          this.program &&
            (e.deleteProgram(this.program), (this.program = null));
      }
      static newColoredTextured(e) {
        let t = `
attribute vec4 ${fe.POSITION};
attribute vec4 ${fe.COLOR};
attribute vec2 ${fe.TEXCOORDS};
uniform mat4 ${fe.MVP_MATRIX};
varying vec4 v_color;
varying vec2 v_texCoords;

void main () {
	v_color = ${fe.COLOR};
	v_texCoords = ${fe.TEXCOORDS};
	gl_Position = ${fe.MVP_MATRIX} * ${fe.POSITION};
}
`,
          i = `
#ifdef GL_ES
	#define LOWP lowp
	precision mediump float;
#else
	#define LOWP
#endif
varying LOWP vec4 v_color;
varying vec2 v_texCoords;
uniform sampler2D u_texture;

void main () {
	gl_FragColor = v_color * texture2D(u_texture, v_texCoords);
}
`;
        return new fe(e, t, i);
      }
      static newTwoColoredTextured(e) {
        let t = `
attribute vec4 ${fe.POSITION};
attribute vec4 ${fe.COLOR};
attribute vec4 ${fe.COLOR2};
attribute vec2 ${fe.TEXCOORDS};
uniform mat4 ${fe.MVP_MATRIX};
varying vec4 v_light;
varying vec4 v_dark;
varying vec2 v_texCoords;

void main () {
	v_light = ${fe.COLOR};
	v_dark = ${fe.COLOR2};
	v_texCoords = ${fe.TEXCOORDS};
	gl_Position = ${fe.MVP_MATRIX} * ${fe.POSITION};
}
`,
          i = `
#ifdef GL_ES
	#define LOWP lowp
	precision mediump float;
#else
	#define LOWP
#endif
varying LOWP vec4 v_light;
varying LOWP vec4 v_dark;
varying vec2 v_texCoords;
uniform sampler2D u_texture;

void main () {
	vec4 texColor = texture2D(u_texture, v_texCoords);
	gl_FragColor.a = texColor.a * v_light.a;
	gl_FragColor.rgb = ((texColor.a - 1.0) * v_dark.a + 1.0 - texColor.rgb) * v_dark.rgb + texColor.rgb * v_light.rgb;
}
`;
        return new fe(e, t, i);
      }
      static newColored(e) {
        let t = `
attribute vec4 ${fe.POSITION};
attribute vec4 ${fe.COLOR};
uniform mat4 ${fe.MVP_MATRIX};
varying vec4 v_color;

void main () {
	v_color = ${fe.COLOR};
	gl_Position = ${fe.MVP_MATRIX} * ${fe.POSITION};
}
`,
          i = `
#ifdef GL_ES
	#define LOWP lowp
	precision mediump float;
#else
	#define LOWP
#endif
varying LOWP vec4 v_color;

void main () {
	gl_FragColor = v_color;
}
`;
        return new fe(e, t, i);
      }
    },
    we = fe;
  B(we, "MVP_MATRIX", "u_projTrans"),
    B(we, "POSITION", "a_position"),
    B(we, "COLOR", "a_color"),
    B(we, "COLOR2", "a_color2"),
    B(we, "TEXCOORDS", "a_texCoords"),
    B(we, "SAMPLER", "u_texture");
  var qi = class {
      constructor(e, t, i, s) {
        (this.attributes = t),
          (this.context = e instanceof pe ? e : new pe(e)),
          (this.elementsPerVertex = 0);
        for (let n = 0; n < t.length; n++)
          this.elementsPerVertex += t[n].numElements;
        (this.vertices = new Float32Array(i * this.elementsPerVertex)),
          (this.indices = new Uint16Array(s)),
          this.context.addRestorable(this);
      }
      context;
      vertices;
      verticesBuffer = null;
      verticesLength = 0;
      dirtyVertices = !1;
      indices;
      indicesBuffer = null;
      indicesLength = 0;
      dirtyIndices = !1;
      elementsPerVertex = 0;
      getAttributes() {
        return this.attributes;
      }
      maxVertices() {
        return this.vertices.length / this.elementsPerVertex;
      }
      numVertices() {
        return this.verticesLength / this.elementsPerVertex;
      }
      setVerticesLength(e) {
        (this.dirtyVertices = !0), (this.verticesLength = e);
      }
      getVertices() {
        return this.vertices;
      }
      maxIndices() {
        return this.indices.length;
      }
      numIndices() {
        return this.indicesLength;
      }
      setIndicesLength(e) {
        (this.dirtyIndices = !0), (this.indicesLength = e);
      }
      getIndices() {
        return this.indices;
      }
      getVertexSizeInFloats() {
        let e = 0;
        for (var t = 0; t < this.attributes.length; t++) {
          let i = this.attributes[t];
          e += i.numElements;
        }
        return e;
      }
      setVertices(e) {
        if (((this.dirtyVertices = !0), e.length > this.vertices.length))
          throw Error(
            "Mesh can't store more than " + this.maxVertices() + " vertices"
          );
        this.vertices.set(e, 0), (this.verticesLength = e.length);
      }
      setIndices(e) {
        if (((this.dirtyIndices = !0), e.length > this.indices.length))
          throw Error(
            "Mesh can't store more than " + this.maxIndices() + " indices"
          );
        this.indices.set(e, 0), (this.indicesLength = e.length);
      }
      draw(e, t) {
        this.drawWithOffset(
          e,
          t,
          0,
          this.indicesLength > 0
            ? this.indicesLength
            : this.verticesLength / this.elementsPerVertex
        );
      }
      drawWithOffset(e, t, i, s) {
        let n = this.context.gl;
        (this.dirtyVertices || this.dirtyIndices) && this.update(),
          this.bind(e),
          this.indicesLength > 0
            ? n.drawElements(t, s, n.UNSIGNED_SHORT, i * 2)
            : n.drawArrays(t, i, s),
          this.unbind(e);
      }
      bind(e) {
        let t = this.context.gl;
        t.bindBuffer(t.ARRAY_BUFFER, this.verticesBuffer);
        let i = 0;
        for (let s = 0; s < this.attributes.length; s++) {
          let n = this.attributes[s],
            d = e.getAttributeLocation(n.name);
          t.enableVertexAttribArray(d),
            t.vertexAttribPointer(
              d,
              n.numElements,
              t.FLOAT,
              !1,
              this.elementsPerVertex * 4,
              i * 4
            ),
            (i += n.numElements);
        }
        this.indicesLength > 0 &&
          t.bindBuffer(t.ELEMENT_ARRAY_BUFFER, this.indicesBuffer);
      }
      unbind(e) {
        let t = this.context.gl;
        for (let i = 0; i < this.attributes.length; i++) {
          let s = this.attributes[i],
            n = e.getAttributeLocation(s.name);
          t.disableVertexAttribArray(n);
        }
        t.bindBuffer(t.ARRAY_BUFFER, null),
          this.indicesLength > 0 && t.bindBuffer(t.ELEMENT_ARRAY_BUFFER, null);
      }
      update() {
        let e = this.context.gl;
        this.dirtyVertices &&
          (this.verticesBuffer || (this.verticesBuffer = e.createBuffer()),
          e.bindBuffer(e.ARRAY_BUFFER, this.verticesBuffer),
          e.bufferData(
            e.ARRAY_BUFFER,
            this.vertices.subarray(0, this.verticesLength),
            e.DYNAMIC_DRAW
          ),
          (this.dirtyVertices = !1)),
          this.dirtyIndices &&
            (this.indicesBuffer || (this.indicesBuffer = e.createBuffer()),
            e.bindBuffer(e.ELEMENT_ARRAY_BUFFER, this.indicesBuffer),
            e.bufferData(
              e.ELEMENT_ARRAY_BUFFER,
              this.indices.subarray(0, this.indicesLength),
              e.DYNAMIC_DRAW
            ),
            (this.dirtyIndices = !1));
      }
      restore() {
        (this.verticesBuffer = null),
          (this.indicesBuffer = null),
          this.update();
      }
      dispose() {
        this.context.removeRestorable(this);
        let e = this.context.gl;
        e.deleteBuffer(this.verticesBuffer), e.deleteBuffer(this.indicesBuffer);
      }
    },
    st = class {
      constructor(e, t, i) {
        (this.name = e), (this.type = t), (this.numElements = i);
      }
    },
    Bt = class extends st {
      constructor() {
        super(we.POSITION, Je.Float, 2);
      }
    },
    jr = class extends st {
      constructor() {
        super(we.POSITION, Je.Float, 3);
      }
    },
    Gi = class extends st {
      constructor(e = 0) {
        super(we.TEXCOORDS + (e == 0 ? "" : e), Je.Float, 2);
      }
    },
    Vt = class extends st {
      constructor() {
        super(we.COLOR, Je.Float, 4);
      }
    },
    Ps = class extends st {
      constructor() {
        super(we.COLOR2, Je.Float, 4);
      }
    },
    Je = ((e) => ((e[(e.Float = 0)] = "Float"), e))(Je || {}),
    Ve = 1,
    Zr = 769,
    Bs = 770,
    Vs = 771,
    Os = 774,
    Ke = class {
      context;
      drawCalls = 0;
      isDrawing = !1;
      mesh;
      shader = null;
      lastTexture = null;
      verticesLength = 0;
      indicesLength = 0;
      srcColorBlend;
      srcAlphaBlend;
      dstBlend;
      cullWasEnabled = !1;
      constructor(e, t = !0, i = 10920) {
        if (i > 10920)
          throw new Error(
            "Can't have more than 10920 triangles per batch: " + i
          );
        this.context = e instanceof pe ? e : new pe(e);
        let s = t
          ? [new Bt(), new Vt(), new Gi(), new Ps()]
          : [new Bt(), new Vt(), new Gi()];
        this.mesh = new qi(e, s, i, i * 3);
        let n = this.context.gl;
        (this.srcColorBlend = n.SRC_ALPHA),
          (this.srcAlphaBlend = n.ONE),
          (this.dstBlend = n.ONE_MINUS_SRC_ALPHA);
      }
      begin(e) {
        if (this.isDrawing)
          throw new Error(
            "PolygonBatch is already drawing. Call PolygonBatch.end() before calling PolygonBatch.begin()"
          );
        (this.drawCalls = 0),
          (this.shader = e),
          (this.lastTexture = null),
          (this.isDrawing = !0);
        let t = this.context.gl;
        t.enable(t.BLEND),
          t.blendFuncSeparate(
            this.srcColorBlend,
            this.dstBlend,
            this.srcAlphaBlend,
            this.dstBlend
          ),
          Ke.disableCulling &&
            ((this.cullWasEnabled = t.isEnabled(t.CULL_FACE)),
            this.cullWasEnabled && t.disable(t.CULL_FACE));
      }
      setBlendMode(e, t) {
        const i = Ke.blendModesGL[e],
          s = t ? i.srcRgbPma : i.srcRgb,
          n = i.srcAlpha,
          d = i.dstRgb;
        if (
          this.srcColorBlend == s &&
          this.srcAlphaBlend == n &&
          this.dstBlend == d
        )
          return;
        (this.srcColorBlend = s),
          (this.srcAlphaBlend = n),
          (this.dstBlend = d),
          this.isDrawing && this.flush(),
          this.context.gl.blendFuncSeparate(s, d, n, d);
      }
      draw(e, t, i) {
        e != this.lastTexture
          ? (this.flush(), (this.lastTexture = e))
          : (this.verticesLength + t.length > this.mesh.getVertices().length ||
              this.indicesLength + i.length > this.mesh.getIndices().length) &&
            this.flush();
        let s = this.mesh.numVertices();
        this.mesh.getVertices().set(t, this.verticesLength),
          (this.verticesLength += t.length),
          this.mesh.setVerticesLength(this.verticesLength);
        let n = this.mesh.getIndices();
        for (let d = this.indicesLength, a = 0; a < i.length; d++, a++)
          n[d] = i[a] + s;
        (this.indicesLength += i.length),
          this.mesh.setIndicesLength(this.indicesLength);
      }
      flush() {
        if (this.verticesLength != 0) {
          if (!this.lastTexture) throw new Error("No texture set.");
          if (!this.shader) throw new Error("No shader set.");
          this.lastTexture.bind(),
            this.mesh.draw(this.shader, this.context.gl.TRIANGLES),
            (this.verticesLength = 0),
            (this.indicesLength = 0),
            this.mesh.setVerticesLength(0),
            this.mesh.setIndicesLength(0),
            this.drawCalls++,
            Ke.globalDrawCalls++;
        }
      }
      end() {
        if (!this.isDrawing)
          throw new Error(
            "PolygonBatch is not drawing. Call PolygonBatch.begin() before calling PolygonBatch.end()"
          );
        (this.verticesLength > 0 || this.indicesLength > 0) && this.flush(),
          (this.shader = null),
          (this.lastTexture = null),
          (this.isDrawing = !1);
        let e = this.context.gl;
        e.disable(e.BLEND),
          Ke.disableCulling && this.cullWasEnabled && e.enable(e.CULL_FACE);
      }
      getDrawCalls() {
        return this.drawCalls;
      }
      static getAndResetGlobalDrawCalls() {
        let e = Ke.globalDrawCalls;
        return (Ke.globalDrawCalls = 0), e;
      }
      dispose() {
        this.mesh.dispose();
      }
    },
    rt = Ke;
  B(rt, "disableCulling", !1),
    B(rt, "globalDrawCalls", 0),
    B(rt, "blendModesGL", [
      { srcRgb: Bs, srcRgbPma: Ve, dstRgb: Vs, srcAlpha: Ve },
      { srcRgb: Bs, srcRgbPma: Ve, dstRgb: Ve, srcAlpha: Ve },
      { srcRgb: Os, srcRgbPma: Os, dstRgb: Vs, srcAlpha: Ve },
      { srcRgb: Ve, srcRgbPma: Ve, dstRgb: Zr, srcAlpha: Ve },
    ]);
  var Hi = class {
      context;
      isDrawing = !1;
      mesh;
      shapeType = ye.Filled;
      color = new V(1, 1, 1, 1);
      shader = null;
      vertexIndex = 0;
      tmp = new Me();
      srcColorBlend;
      srcAlphaBlend;
      dstBlend;
      constructor(e, t = 10920) {
        if (t > 10920)
          throw new Error(
            "Can't have more than 10920 triangles per batch: " + t
          );
        (this.context = e instanceof pe ? e : new pe(e)),
          (this.mesh = new qi(e, [new Bt(), new Vt()], t, 0));
        let i = this.context.gl;
        (this.srcColorBlend = i.SRC_ALPHA),
          (this.srcAlphaBlend = i.ONE),
          (this.dstBlend = i.ONE_MINUS_SRC_ALPHA);
      }
      begin(e) {
        if (this.isDrawing)
          throw new Error("ShapeRenderer.begin() has already been called");
        (this.shader = e), (this.vertexIndex = 0), (this.isDrawing = !0);
        let t = this.context.gl;
        t.enable(t.BLEND),
          t.blendFuncSeparate(
            this.srcColorBlend,
            this.dstBlend,
            this.srcAlphaBlend,
            this.dstBlend
          );
      }
      setBlendMode(e, t, i) {
        (this.srcColorBlend = e),
          (this.srcAlphaBlend = t),
          (this.dstBlend = i),
          this.isDrawing &&
            (this.flush(), this.context.gl.blendFuncSeparate(e, i, t, i));
      }
      setColor(e) {
        this.color.setFromColor(e);
      }
      setColorWith(e, t, i, s) {
        this.color.set(e, t, i, s);
      }
      point(e, t, i) {
        this.check(ye.Point, 1), i || (i = this.color), this.vertex(e, t, i);
      }
      line(e, t, i, s, n) {
        this.check(ye.Line, 2);
        let d = this.mesh.getVertices(),
          a = this.vertexIndex;
        n || (n = this.color), this.vertex(e, t, n), this.vertex(i, s, n);
      }
      triangle(e, t, i, s, n, d, a, r, o, l) {
        this.check(e ? ye.Filled : ye.Line, 3);
        let h = this.mesh.getVertices(),
          u = this.vertexIndex;
        r || (r = this.color),
          o || (o = this.color),
          l || (l = this.color),
          e
            ? (this.vertex(t, i, r), this.vertex(s, n, o), this.vertex(d, a, l))
            : (this.vertex(t, i, r),
              this.vertex(s, n, o),
              this.vertex(s, n, r),
              this.vertex(d, a, o),
              this.vertex(d, a, r),
              this.vertex(t, i, o));
      }
      quad(e, t, i, s, n, d, a, r, o, l, h, u, c) {
        this.check(e ? ye.Filled : ye.Line, 3);
        let f = this.mesh.getVertices(),
          m = this.vertexIndex;
        l || (l = this.color),
          h || (h = this.color),
          u || (u = this.color),
          c || (c = this.color),
          e
            ? (this.vertex(t, i, l),
              this.vertex(s, n, h),
              this.vertex(d, a, u),
              this.vertex(d, a, u),
              this.vertex(r, o, c),
              this.vertex(t, i, l))
            : (this.vertex(t, i, l),
              this.vertex(s, n, h),
              this.vertex(s, n, h),
              this.vertex(d, a, u),
              this.vertex(d, a, u),
              this.vertex(r, o, c),
              this.vertex(r, o, c),
              this.vertex(t, i, l));
      }
      rect(e, t, i, s, n, d) {
        this.quad(e, t, i, t + s, i, t + s, i + n, t, i + n, d, d, d, d);
      }
      rectLine(e, t, i, s, n, d, a) {
        this.check(e ? ye.Filled : ye.Line, 8), a || (a = this.color);
        let r = this.tmp.set(n - i, t - s);
        r.normalize(), (d *= 0.5);
        let o = r.x * d,
          l = r.y * d;
        e
          ? (this.vertex(t + o, i + l, a),
            this.vertex(t - o, i - l, a),
            this.vertex(s + o, n + l, a),
            this.vertex(s - o, n - l, a),
            this.vertex(s + o, n + l, a),
            this.vertex(t - o, i - l, a))
          : (this.vertex(t + o, i + l, a),
            this.vertex(t - o, i - l, a),
            this.vertex(s + o, n + l, a),
            this.vertex(s - o, n - l, a),
            this.vertex(s + o, n + l, a),
            this.vertex(t + o, i + l, a),
            this.vertex(s - o, n - l, a),
            this.vertex(t - o, i - l, a));
      }
      x(e, t, i) {
        this.line(e - i, t - i, e + i, t + i),
          this.line(e - i, t + i, e + i, t - i);
      }
      polygon(e, t, i, s) {
        if (i < 3) throw new Error("Polygon must contain at least 3 vertices");
        this.check(ye.Line, i * 2), s || (s = this.color);
        let n = this.mesh.getVertices(),
          d = this.vertexIndex;
        (t <<= 1), (i <<= 1);
        let a = e[t],
          r = e[t + 1],
          o = t + i;
        for (let l = t, h = t + i - 2; l < h; l += 2) {
          let u = e[l],
            c = e[l + 1],
            f = 0,
            m = 0;
          l + 2 >= o ? ((f = a), (m = r)) : ((f = e[l + 2]), (m = e[l + 3])),
            this.vertex(u, c, s),
            this.vertex(f, m, s);
        }
      }
      circle(e, t, i, s, n, d = 0) {
        if ((d == 0 && (d = Math.max(1, (6 * X.cbrt(s)) | 0)), d <= 0))
          throw new Error("segments must be > 0.");
        n || (n = this.color);
        let a = (2 * X.PI) / d,
          r = Math.cos(a),
          o = Math.sin(a),
          l = s,
          h = 0;
        if (e) {
          this.check(ye.Filled, d * 3 + 3), d--;
          for (let c = 0; c < d; c++) {
            this.vertex(t, i, n), this.vertex(t + l, i + h, n);
            let f = l;
            (l = r * l - o * h),
              (h = o * f + r * h),
              this.vertex(t + l, i + h, n);
          }
          this.vertex(t, i, n), this.vertex(t + l, i + h, n);
        } else {
          this.check(ye.Line, d * 2 + 2);
          for (let c = 0; c < d; c++) {
            this.vertex(t + l, i + h, n);
            let f = l;
            (l = r * l - o * h),
              (h = o * f + r * h),
              this.vertex(t + l, i + h, n);
          }
          this.vertex(t + l, i + h, n);
        }
        let u = l;
        (l = s), (h = 0), this.vertex(t + l, i + h, n);
      }
      curve(e, t, i, s, n, d, a, r, o, l) {
        this.check(ye.Line, o * 2 + 2), l || (l = this.color);
        let h = 1 / o,
          u = h * h,
          c = h * h * h,
          f = 3 * h,
          m = 3 * u,
          g = 6 * u,
          b = 6 * c,
          x = e - i * 2 + n,
          p = t - s * 2 + d,
          w = (i - n) * 3 - e + a,
          y = (s - d) * 3 - t + r,
          v = e,
          A = t,
          C = (i - e) * f + x * m + w * c,
          T = (s - t) * f + p * m + y * c,
          I = x * g + w * b,
          M = p * g + y * b,
          F = w * b,
          L = y * b;
        for (; o-- > 0; )
          this.vertex(v, A, l),
            (v += C),
            (A += T),
            (C += I),
            (T += M),
            (I += F),
            (M += L),
            this.vertex(v, A, l);
        this.vertex(v, A, l), this.vertex(a, r, l);
      }
      vertex(e, t, i) {
        let s = this.vertexIndex,
          n = this.mesh.getVertices();
        (n[s++] = e),
          (n[s++] = t),
          (n[s++] = i.r),
          (n[s++] = i.g),
          (n[s++] = i.b),
          (n[s++] = i.a),
          (this.vertexIndex = s);
      }
      end() {
        if (!this.isDrawing)
          throw new Error("ShapeRenderer.begin() has not been called");
        this.flush();
        let e = this.context.gl;
        e.disable(e.BLEND), (this.isDrawing = !1);
      }
      flush() {
        if (this.vertexIndex != 0) {
          if (!this.shader) throw new Error("No shader set.");
          this.mesh.setVerticesLength(this.vertexIndex),
            this.mesh.draw(this.shader, this.shapeType),
            (this.vertexIndex = 0);
        }
      }
      check(e, t) {
        if (!this.isDrawing)
          throw new Error("ShapeRenderer.begin() has not been called");
        if (this.shapeType == e)
          if (this.mesh.maxVertices() - this.mesh.numVertices() < t)
            this.flush();
          else return;
        else this.flush(), (this.shapeType = e);
      }
      dispose() {
        this.mesh.dispose();
      }
    },
    ye = ((e) => (
      (e[(e.Point = 0)] = "Point"),
      (e[(e.Line = 1)] = "Line"),
      (e[(e.Filled = 4)] = "Filled"),
      e
    ))(ye || {}),
    ji = class {
      boneLineColor = new V(1, 0, 0, 1);
      boneOriginColor = new V(0, 1, 0, 1);
      attachmentLineColor = new V(0, 0, 1, 0.5);
      triangleLineColor = new V(1, 0.64, 0, 0.5);
      pathColor = new V().setFromString("FF7F00");
      clipColor = new V(0.8, 0, 0, 2);
      aabbColor = new V(0, 1, 0, 0.5);
      drawBones = !0;
      drawRegionAttachments = !0;
      drawBoundingBoxes = !0;
      drawMeshHull = !0;
      drawMeshTriangles = !0;
      drawPaths = !0;
      drawSkeletonXY = !1;
      drawClipping = !0;
      premultipliedAlpha = !1;
      scale = 1;
      boneWidth = 2;
      context;
      bounds = new Fs();
      temp = new Array();
      vertices = P.newFloatArray(2 * 1024);
      constructor(e) {
        this.context = e instanceof pe ? e : new pe(e);
      }
      draw(e, t, i) {
        let s = t.x,
          n = t.y,
          d = this.context.gl,
          a = this.premultipliedAlpha ? d.ONE : d.SRC_ALPHA;
        e.setBlendMode(a, d.ONE, d.ONE_MINUS_SRC_ALPHA);
        let r = t.bones;
        if (this.drawBones) {
          e.setColor(this.boneLineColor);
          for (let o = 0, l = r.length; o < l; o++) {
            let h = r[o];
            if ((i && i.indexOf(h.data.name) > -1) || !h.parent) continue;
            let u = h.data.length * h.a + h.worldX,
              c = h.data.length * h.c + h.worldY;
            e.rectLine(
              !0,
              h.worldX,
              h.worldY,
              u,
              c,
              this.boneWidth * this.scale
            );
          }
          this.drawSkeletonXY && e.x(s, n, 4 * this.scale);
        }
        if (this.drawRegionAttachments) {
          e.setColor(this.attachmentLineColor);
          let o = t.slots;
          for (let l = 0, h = o.length; l < h; l++) {
            let u = o[l],
              c = u.getAttachment();
            if (c instanceof le) {
              let f = c,
                m = this.vertices;
              f.computeWorldVertices(u, m, 0, 2),
                e.line(m[0], m[1], m[2], m[3]),
                e.line(m[2], m[3], m[4], m[5]),
                e.line(m[4], m[5], m[6], m[7]),
                e.line(m[6], m[7], m[0], m[1]);
            }
          }
        }
        if (this.drawMeshHull || this.drawMeshTriangles) {
          let o = t.slots;
          for (let l = 0, h = o.length; l < h; l++) {
            let u = o[l];
            if (!u.bone.active) continue;
            let c = u.getAttachment();
            if (!(c instanceof Ue)) continue;
            let f = c,
              m = this.vertices;
            f.computeWorldVertices(u, 0, f.worldVerticesLength, m, 0, 2);
            let g = f.triangles,
              b = f.hullLength;
            if (this.drawMeshTriangles) {
              e.setColor(this.triangleLineColor);
              for (let x = 0, p = g.length; x < p; x += 3) {
                let w = g[x] * 2,
                  y = g[x + 1] * 2,
                  v = g[x + 2] * 2;
                e.triangle(!1, m[w], m[w + 1], m[y], m[y + 1], m[v], m[v + 1]);
              }
            }
            if (this.drawMeshHull && b > 0) {
              e.setColor(this.attachmentLineColor), (b = (b >> 1) * 2);
              let x = m[b - 2],
                p = m[b - 1];
              for (let w = 0, y = b; w < y; w += 2) {
                let v = m[w],
                  A = m[w + 1];
                e.line(v, A, x, p), (x = v), (p = A);
              }
            }
          }
        }
        if (this.drawBoundingBoxes) {
          let o = this.bounds;
          o.update(t, !0),
            e.setColor(this.aabbColor),
            e.rect(!1, o.minX, o.minY, o.getWidth(), o.getHeight());
          let l = o.polygons,
            h = o.boundingBoxes;
          for (let u = 0, c = l.length; u < c; u++) {
            let f = l[u];
            e.setColor(h[u].color), e.polygon(f, 0, f.length);
          }
        }
        if (this.drawPaths) {
          let o = t.slots;
          for (let l = 0, h = o.length; l < h; l++) {
            let u = o[l];
            if (!u.bone.active) continue;
            let c = u.getAttachment();
            if (!(c instanceof Ze)) continue;
            let f = c,
              m = f.worldVerticesLength,
              g = (this.temp = P.setArraySize(this.temp, m, 0));
            f.computeWorldVertices(u, 0, m, g, 0, 2);
            let b = this.pathColor,
              x = g[2],
              p = g[3],
              w = 0,
              y = 0;
            if (f.closed) {
              e.setColor(b);
              let v = g[0],
                A = g[1],
                C = g[m - 2],
                T = g[m - 1];
              (w = g[m - 4]),
                (y = g[m - 3]),
                e.curve(x, p, v, A, C, T, w, y, 32),
                e.setColor(ji.LIGHT_GRAY),
                e.line(x, p, v, A),
                e.line(w, y, C, T);
            }
            m -= 4;
            for (let v = 4; v < m; v += 6) {
              let A = g[v],
                C = g[v + 1],
                T = g[v + 2],
                I = g[v + 3];
              (w = g[v + 4]),
                (y = g[v + 5]),
                e.setColor(b),
                e.curve(x, p, A, C, T, I, w, y, 32),
                e.setColor(ji.LIGHT_GRAY),
                e.line(x, p, A, C),
                e.line(w, y, T, I),
                (x = w),
                (p = y);
            }
          }
        }
        if (this.drawBones) {
          e.setColor(this.boneOriginColor);
          for (let o = 0, l = r.length; o < l; o++) {
            let h = r[o];
            (i && i.indexOf(h.data.name) > -1) ||
              e.circle(
                !0,
                h.worldX,
                h.worldY,
                3 * this.scale,
                this.boneOriginColor,
                8
              );
          }
        }
        if (this.drawClipping) {
          let o = t.slots;
          e.setColor(this.clipColor);
          for (let l = 0, h = o.length; l < h; l++) {
            let u = o[l];
            if (!u.bone.active) continue;
            let c = u.getAttachment();
            if (!(c instanceof tt)) continue;
            let f = c,
              m = f.worldVerticesLength,
              g = (this.temp = P.setArraySize(this.temp, m, 0));
            f.computeWorldVertices(u, 0, m, g, 0, 2);
            for (let b = 0, x = g.length; b < x; b += 2) {
              let p = g[b],
                w = g[b + 1],
                y = g[(b + 2) % g.length],
                v = g[(b + 3) % g.length];
              e.line(p, w, y, v);
            }
          }
        }
      }
      dispose() {}
    },
    Ot = ji;
  B(
    Ot,
    "LIGHT_GRAY",
    new V(0.7529411764705882, 0.7529411764705882, 0.7529411764705882, 1)
  ),
    B(Ot, "GREEN", new V(0, 1, 0, 1));
  var Jr = class {
      constructor(e, t, i) {
        (this.vertices = e), (this.numVertices = t), (this.numFloats = i);
      }
    },
    Ds = class {
      premultipliedAlpha = !1;
      tempColor = new V();
      tempColor2 = new V();
      vertices;
      vertexSize = 2 + 2 + 4;
      twoColorTint = !1;
      renderable = new Jr([], 0, 0);
      clipper = new Xt();
      temp = new Me();
      temp2 = new Me();
      temp3 = new V();
      temp4 = new V();
      constructor(e, t = !0) {
        (this.twoColorTint = t),
          t && (this.vertexSize += 4),
          (this.vertices = P.newFloatArray(this.vertexSize * 1024));
      }
      draw(e, t, i = -1, s = -1, n = null) {
        let d = this.clipper,
          a = this.premultipliedAlpha,
          r = this.twoColorTint,
          o = null,
          l = this.renderable,
          h,
          u,
          c = t.drawOrder,
          f,
          m = t.color,
          g = r ? 12 : 8,
          b = !1;
        i == -1 && (b = !0);
        for (let x = 0, p = c.length; x < p; x++) {
          let w = d.isClipping() ? 2 : g,
            y = c[x];
          if (!y.bone.active) {
            d.clipEndWithSlot(y);
            continue;
          }
          if ((i >= 0 && i == y.data.index && (b = !0), !b)) {
            d.clipEndWithSlot(y);
            continue;
          }
          s >= 0 && s == y.data.index && (b = !1);
          let v = y.getAttachment(),
            A;
          if (v instanceof le) {
            let C = v;
            (l.vertices = this.vertices),
              (l.numVertices = 4),
              (l.numFloats = w << 2),
              C.computeWorldVertices(y, l.vertices, 0, w),
              (u = Ds.QUAD_TRIANGLES),
              (h = C.uvs),
              (A = C.region.texture),
              (f = C.color);
          } else if (v instanceof Ue) {
            let C = v;
            (l.vertices = this.vertices),
              (l.numVertices = C.worldVerticesLength >> 1),
              (l.numFloats = l.numVertices * w),
              l.numFloats > l.vertices.length &&
                (l.vertices = this.vertices = P.newFloatArray(l.numFloats)),
              C.computeWorldVertices(
                y,
                0,
                C.worldVerticesLength,
                l.vertices,
                0,
                w
              ),
              (u = C.triangles),
              (A = C.region.texture),
              (h = C.uvs),
              (f = C.color);
          } else if (v instanceof tt) {
            let C = v;
            d.clipStart(y, C);
            continue;
          } else {
            d.clipEndWithSlot(y);
            continue;
          }
          if (A) {
            let C = y.color,
              T = this.tempColor;
            (T.r = m.r * C.r * f.r),
              (T.g = m.g * C.g * f.g),
              (T.b = m.b * C.b * f.b),
              (T.a = m.a * C.a * f.a),
              a && ((T.r *= T.a), (T.g *= T.a), (T.b *= T.a));
            let I = this.tempColor2;
            y.darkColor
              ? (a
                  ? ((I.r = y.darkColor.r * T.a),
                    (I.g = y.darkColor.g * T.a),
                    (I.b = y.darkColor.b * T.a))
                  : I.setFromColor(y.darkColor),
                (I.a = a ? 1 : 0))
              : I.set(0, 0, 0, 1);
            let M = y.data.blendMode;
            if ((M != o && ((o = M), e.setBlendMode(o, a)), d.isClipping())) {
              d.clipTriangles(l.vertices, u, u.length, h, T, I, r);
              let F = new Float32Array(d.clippedVertices),
                L = d.clippedTriangles;
              n && n(F, F.length, g), e.draw(A, F, L);
            } else {
              let F = l.vertices;
              if (r)
                for (let k = 2, R = 0, Y = l.numFloats; k < Y; k += g, R += 2)
                  (F[k] = T.r),
                    (F[k + 1] = T.g),
                    (F[k + 2] = T.b),
                    (F[k + 3] = T.a),
                    (F[k + 4] = h[R]),
                    (F[k + 5] = h[R + 1]),
                    (F[k + 6] = I.r),
                    (F[k + 7] = I.g),
                    (F[k + 8] = I.b),
                    (F[k + 9] = I.a);
              else
                for (let k = 2, R = 0, Y = l.numFloats; k < Y; k += g, R += 2)
                  (F[k] = T.r),
                    (F[k + 1] = T.g),
                    (F[k + 2] = T.b),
                    (F[k + 3] = T.a),
                    (F[k + 4] = h[R]),
                    (F[k + 5] = h[R + 1]);
              let L = l.vertices.subarray(0, l.numFloats);
              n && n(l.vertices, l.numFloats, g), e.draw(A, L, u);
            }
          }
          d.clipEndWithSlot(y);
        }
        d.clipEnd();
      }
      getSkeletonClipping() {
        return this.clipper;
      }
    },
    Zi = Ds;
  B(Zi, "QUAD_TRIANGLES", [0, 1, 2, 2, 3, 0]);
  var S = [
      0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0,
      0, 1, 1, 1, 1, 0, 0,
    ],
    Dt = [0, 1, 2, 2, 3, 0],
    Nt = new V(1, 1, 1, 1),
    Ji = class {
      context;
      canvas;
      camera;
      batcher;
      twoColorTint = !1;
      batcherShader;
      shapes;
      shapesShader;
      activeRenderer = null;
      skeletonRenderer;
      skeletonDebugRenderer;
      constructor(e, t, i = !0) {
        (this.canvas = e),
          (this.context = t instanceof pe ? t : new pe(t)),
          (this.twoColorTint = i),
          (this.camera = new Ls(e.width, e.height)),
          (this.batcherShader = i
            ? we.newTwoColoredTextured(this.context)
            : we.newColoredTextured(this.context)),
          (this.batcher = new rt(this.context, i)),
          (this.shapesShader = we.newColored(this.context)),
          (this.shapes = new Hi(this.context)),
          (this.skeletonRenderer = new Zi(this.context, i)),
          (this.skeletonDebugRenderer = new Ot(this.context));
      }
      dispose() {
        this.batcher.dispose(),
          this.batcherShader.dispose(),
          this.shapes.dispose(),
          this.shapesShader.dispose(),
          this.skeletonDebugRenderer.dispose();
      }
      begin() {
        this.camera.update(), this.enableRenderer(this.batcher);
      }
      drawSkeleton(e, t = !1, i = -1, s = -1, n = null) {
        this.enableRenderer(this.batcher),
          (this.skeletonRenderer.premultipliedAlpha = t),
          this.skeletonRenderer.draw(this.batcher, e, i, s, n);
      }
      drawSkeletonDebug(e, t = !1, i) {
        this.enableRenderer(this.shapes),
          (this.skeletonDebugRenderer.premultipliedAlpha = t),
          this.skeletonDebugRenderer.draw(this.shapes, e, i);
      }
      drawTexture(e, t, i, s, n, d) {
        this.enableRenderer(this.batcher), d || (d = Nt);
        var a = 0;
        (S[a++] = t),
          (S[a++] = i),
          (S[a++] = d.r),
          (S[a++] = d.g),
          (S[a++] = d.b),
          (S[a++] = d.a),
          (S[a++] = 0),
          (S[a++] = 1),
          this.twoColorTint &&
            ((S[a++] = 0), (S[a++] = 0), (S[a++] = 0), (S[a++] = 0)),
          (S[a++] = t + s),
          (S[a++] = i),
          (S[a++] = d.r),
          (S[a++] = d.g),
          (S[a++] = d.b),
          (S[a++] = d.a),
          (S[a++] = 1),
          (S[a++] = 1),
          this.twoColorTint &&
            ((S[a++] = 0), (S[a++] = 0), (S[a++] = 0), (S[a++] = 0)),
          (S[a++] = t + s),
          (S[a++] = i + n),
          (S[a++] = d.r),
          (S[a++] = d.g),
          (S[a++] = d.b),
          (S[a++] = d.a),
          (S[a++] = 1),
          (S[a++] = 0),
          this.twoColorTint &&
            ((S[a++] = 0), (S[a++] = 0), (S[a++] = 0), (S[a++] = 0)),
          (S[a++] = t),
          (S[a++] = i + n),
          (S[a++] = d.r),
          (S[a++] = d.g),
          (S[a++] = d.b),
          (S[a++] = d.a),
          (S[a++] = 0),
          (S[a++] = 0),
          this.twoColorTint &&
            ((S[a++] = 0), (S[a++] = 0), (S[a++] = 0), (S[a] = 0)),
          this.batcher.draw(e, S, Dt);
      }
      drawTextureUV(e, t, i, s, n, d, a, r, o, l) {
        this.enableRenderer(this.batcher), l || (l = Nt);
        var h = 0;
        (S[h++] = t),
          (S[h++] = i),
          (S[h++] = l.r),
          (S[h++] = l.g),
          (S[h++] = l.b),
          (S[h++] = l.a),
          (S[h++] = d),
          (S[h++] = a),
          this.twoColorTint &&
            ((S[h++] = 0), (S[h++] = 0), (S[h++] = 0), (S[h++] = 0)),
          (S[h++] = t + s),
          (S[h++] = i),
          (S[h++] = l.r),
          (S[h++] = l.g),
          (S[h++] = l.b),
          (S[h++] = l.a),
          (S[h++] = r),
          (S[h++] = a),
          this.twoColorTint &&
            ((S[h++] = 0), (S[h++] = 0), (S[h++] = 0), (S[h++] = 0)),
          (S[h++] = t + s),
          (S[h++] = i + n),
          (S[h++] = l.r),
          (S[h++] = l.g),
          (S[h++] = l.b),
          (S[h++] = l.a),
          (S[h++] = r),
          (S[h++] = o),
          this.twoColorTint &&
            ((S[h++] = 0), (S[h++] = 0), (S[h++] = 0), (S[h++] = 0)),
          (S[h++] = t),
          (S[h++] = i + n),
          (S[h++] = l.r),
          (S[h++] = l.g),
          (S[h++] = l.b),
          (S[h++] = l.a),
          (S[h++] = d),
          (S[h++] = o),
          this.twoColorTint &&
            ((S[h++] = 0), (S[h++] = 0), (S[h++] = 0), (S[h] = 0)),
          this.batcher.draw(e, S, Dt);
      }
      drawTextureRotated(e, t, i, s, n, d, a, r, o) {
        this.enableRenderer(this.batcher), o || (o = Nt);
        let l = t + d,
          h = i + a,
          u = -d,
          c = -a,
          f = s - d,
          m = n - a,
          g = u,
          b = c,
          x = u,
          p = m,
          w = f,
          y = m,
          v = f,
          A = c,
          C = 0,
          T = 0,
          I = 0,
          M = 0,
          F = 0,
          L = 0,
          k = 0,
          R = 0;
        if (r != 0) {
          let ie = X.cosDeg(r),
            se = X.sinDeg(r);
          (C = ie * g - se * b),
            (T = se * g + ie * b),
            (k = ie * x - se * p),
            (R = se * x + ie * p),
            (F = ie * w - se * y),
            (L = se * w + ie * y),
            (I = F + (C - k)),
            (M = L + (T - R));
        } else
          (C = g),
            (T = b),
            (k = x),
            (R = p),
            (F = w),
            (L = y),
            (I = v),
            (M = A);
        (C += l),
          (T += h),
          (I += l),
          (M += h),
          (F += l),
          (L += h),
          (k += l),
          (R += h);
        var Y = 0;
        (S[Y++] = C),
          (S[Y++] = T),
          (S[Y++] = o.r),
          (S[Y++] = o.g),
          (S[Y++] = o.b),
          (S[Y++] = o.a),
          (S[Y++] = 0),
          (S[Y++] = 1),
          this.twoColorTint &&
            ((S[Y++] = 0), (S[Y++] = 0), (S[Y++] = 0), (S[Y++] = 0)),
          (S[Y++] = I),
          (S[Y++] = M),
          (S[Y++] = o.r),
          (S[Y++] = o.g),
          (S[Y++] = o.b),
          (S[Y++] = o.a),
          (S[Y++] = 1),
          (S[Y++] = 1),
          this.twoColorTint &&
            ((S[Y++] = 0), (S[Y++] = 0), (S[Y++] = 0), (S[Y++] = 0)),
          (S[Y++] = F),
          (S[Y++] = L),
          (S[Y++] = o.r),
          (S[Y++] = o.g),
          (S[Y++] = o.b),
          (S[Y++] = o.a),
          (S[Y++] = 1),
          (S[Y++] = 0),
          this.twoColorTint &&
            ((S[Y++] = 0), (S[Y++] = 0), (S[Y++] = 0), (S[Y++] = 0)),
          (S[Y++] = k),
          (S[Y++] = R),
          (S[Y++] = o.r),
          (S[Y++] = o.g),
          (S[Y++] = o.b),
          (S[Y++] = o.a),
          (S[Y++] = 0),
          (S[Y++] = 0),
          this.twoColorTint &&
            ((S[Y++] = 0), (S[Y++] = 0), (S[Y++] = 0), (S[Y] = 0)),
          this.batcher.draw(e, S, Dt);
      }
      drawRegion(e, t, i, s, n, d) {
        this.enableRenderer(this.batcher), d || (d = Nt);
        var a = 0;
        (S[a++] = t),
          (S[a++] = i),
          (S[a++] = d.r),
          (S[a++] = d.g),
          (S[a++] = d.b),
          (S[a++] = d.a),
          (S[a++] = e.u),
          (S[a++] = e.v2),
          this.twoColorTint &&
            ((S[a++] = 0), (S[a++] = 0), (S[a++] = 0), (S[a++] = 0)),
          (S[a++] = t + s),
          (S[a++] = i),
          (S[a++] = d.r),
          (S[a++] = d.g),
          (S[a++] = d.b),
          (S[a++] = d.a),
          (S[a++] = e.u2),
          (S[a++] = e.v2),
          this.twoColorTint &&
            ((S[a++] = 0), (S[a++] = 0), (S[a++] = 0), (S[a++] = 0)),
          (S[a++] = t + s),
          (S[a++] = i + n),
          (S[a++] = d.r),
          (S[a++] = d.g),
          (S[a++] = d.b),
          (S[a++] = d.a),
          (S[a++] = e.u2),
          (S[a++] = e.v),
          this.twoColorTint &&
            ((S[a++] = 0), (S[a++] = 0), (S[a++] = 0), (S[a++] = 0)),
          (S[a++] = t),
          (S[a++] = i + n),
          (S[a++] = d.r),
          (S[a++] = d.g),
          (S[a++] = d.b),
          (S[a++] = d.a),
          (S[a++] = e.u),
          (S[a++] = e.v),
          this.twoColorTint &&
            ((S[a++] = 0), (S[a++] = 0), (S[a++] = 0), (S[a] = 0)),
          this.batcher.draw(e.page.texture, S, Dt);
      }
      line(e, t, i, s, n, d) {
        this.enableRenderer(this.shapes), this.shapes.line(e, t, i, s, n);
      }
      triangle(e, t, i, s, n, d, a, r, o, l) {
        this.enableRenderer(this.shapes),
          this.shapes.triangle(e, t, i, s, n, d, a, r, o, l);
      }
      quad(e, t, i, s, n, d, a, r, o, l, h, u, c) {
        this.enableRenderer(this.shapes),
          this.shapes.quad(e, t, i, s, n, d, a, r, o, l, h, u, c);
      }
      rect(e, t, i, s, n, d) {
        this.enableRenderer(this.shapes), this.shapes.rect(e, t, i, s, n, d);
      }
      rectLine(e, t, i, s, n, d, a) {
        this.enableRenderer(this.shapes),
          this.shapes.rectLine(e, t, i, s, n, d, a);
      }
      polygon(e, t, i, s) {
        this.enableRenderer(this.shapes), this.shapes.polygon(e, t, i, s);
      }
      circle(e, t, i, s, n, d = 0) {
        this.enableRenderer(this.shapes), this.shapes.circle(e, t, i, s, n, d);
      }
      curve(e, t, i, s, n, d, a, r, o, l) {
        this.enableRenderer(this.shapes),
          this.shapes.curve(e, t, i, s, n, d, a, r, o, l);
      }
      end() {
        this.activeRenderer === this.batcher
          ? this.batcher.end()
          : this.activeRenderer === this.shapes && this.shapes.end(),
          (this.activeRenderer = null);
      }
      resize(e) {
        let t = this.canvas;
        var i = window.devicePixelRatio || 1,
          s = Math.round(t.clientWidth * i),
          n = Math.round(t.clientHeight * i);
        if (
          ((t.width != s || t.height != n) && ((t.width = s), (t.height = n)),
          this.context.gl.viewport(0, 0, t.width, t.height),
          e === Ut.Expand)
        )
          this.camera.setViewport(s, n);
        else if (e === Ut.Fit) {
          let d = t.width,
            a = t.height,
            r = this.camera.viewportWidth,
            o = this.camera.viewportHeight,
            l = o / r,
            h = a / d,
            u = l < h ? r / d : o / a;
          this.camera.setViewport(d * u, a * u);
        }
        this.camera.update();
      }
      enableRenderer(e) {
        this.activeRenderer !== e &&
          (this.end(),
          e instanceof rt
            ? (this.batcherShader.bind(),
              this.batcherShader.setUniform4x4f(
                we.MVP_MATRIX,
                this.camera.projectionView.values
              ),
              this.batcherShader.setUniformi("u_texture", 0),
              this.batcher.begin(this.batcherShader),
              (this.activeRenderer = this.batcher))
            : e instanceof Hi
            ? (this.shapesShader.bind(),
              this.shapesShader.setUniform4x4f(
                we.MVP_MATRIX,
                this.camera.projectionView.values
              ),
              this.shapes.begin(this.shapesShader),
              (this.activeRenderer = this.shapes))
            : (this.activeRenderer = this.skeletonDebugRenderer));
      }
    },
    Ut = ((e) => (
      (e[(e.Stretch = 0)] = "Stretch"),
      (e[(e.Expand = 1)] = "Expand"),
      (e[(e.Fit = 2)] = "Fit"),
      e
    ))(Ut || {}),
    gt,
    nt,
    Ns = 0,
    Kr = 1,
    Ki = 1,
    Us = 165,
    _s = 108,
    Qe = 163,
    Ws = class {
      renderer;
      logo = null;
      spinner = null;
      angle = 0;
      fadeOut = 0;
      fadeIn = 0;
      timeKeeper = new bt();
      backgroundColor = new V(0.135, 0.135, 0.135, 1);
      tempColor = new V();
      constructor(e) {
        if (((this.renderer = e), (this.timeKeeper.maxDelta = 9), !nt)) {
          let t = navigator.userAgent.indexOf("Safari") > -1,
            i = () => Ns++;
          (nt = new Image()),
            (nt.src = $r),
            t || (nt.crossOrigin = "anonymous"),
            (nt.onload = i),
            (gt = new Image()),
            (gt.src = Qr),
            t || (gt.crossOrigin = "anonymous"),
            (gt.onload = i);
        }
      }
      dispose() {
        this.logo?.dispose(), this.spinner?.dispose();
      }
      draw(e = !1) {
        if (Ns < 2 || (e && this.fadeOut > Ki)) return;
        this.timeKeeper.update();
        let t = Math.abs(Math.sin(this.timeKeeper.totalTime + 0.25));
        this.angle -= this.timeKeeper.delta * 200 * (1 + 1.5 * Math.pow(t, 5));
        let i = this.tempColor,
          s = this.renderer,
          n = s.canvas,
          d = s.context.gl;
        if (
          (s.resize(1),
          s.camera.position.set(n.width / 2, n.height / 2, 0),
          s.batcher.setBlendMode(0, !0),
          e)
        ) {
          if (
            ((this.fadeOut +=
              this.timeKeeper.delta * (this.timeKeeper.totalTime < 1 ? 2 : 1)),
            this.fadeOut > Ki)
          )
            return;
          i.setFromColor(this.backgroundColor),
            (t = 1 - this.fadeOut / Ki),
            (t = 1 - (t - 1) * (t - 1)),
            (i.a *= t),
            i.a > 0 &&
              ((s.camera.zoom = 1),
              s.begin(),
              s.quad(
                !0,
                0,
                0,
                n.width,
                0,
                n.width,
                n.height,
                0,
                n.height,
                i,
                i,
                i,
                i
              ),
              s.end());
        } else
          (this.fadeIn += this.timeKeeper.delta),
            this.backgroundColor.a > 0 &&
              (d.clearColor(
                this.backgroundColor.r,
                this.backgroundColor.g,
                this.backgroundColor.b,
                this.backgroundColor.a
              ),
              d.clear(d.COLOR_BUFFER_BIT)),
            (t = 1);
        (t *= Math.min(this.fadeIn / Kr, 1)),
          i.set(t, t, t, t),
          this.logo ||
            ((this.logo = new ft(s.context, nt)),
            (this.spinner = new ft(s.context, gt))),
          (s.camera.zoom = Math.max(1, Qe / n.height)),
          s.begin(),
          s.drawTexture(
            this.logo,
            (n.width - Us) / 2,
            (n.height - _s) / 2,
            Us,
            _s,
            i
          ),
          this.spinner &&
            s.drawTextureRotated(
              this.spinner,
              (n.width - Qe) / 2,
              (n.height - Qe) / 2,
              Qe,
              Qe,
              Qe / 2,
              Qe / 2,
              this.angle,
              i
            ),
          s.end();
      }
    },
    Qr =
      "data:image/png;base64,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",
    $r =
      "data:image/png;base64,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",
    en = class {
      constructor(e, t) {
        (this.config = t),
          t.pathPrefix || (t.pathPrefix = ""),
          t.app ||
            (t.app = {
              loadAssets: () => {},
              initialize: () => {},
              update: () => {},
              render: () => {},
              error: () => {},
              dispose: () => {},
            }),
          t.webglConfig || (t.webglConfig = { alpha: !0 }),
          (this.htmlCanvas = e),
          (this.context = new pe(e, t.webglConfig)),
          (this.renderer = new Ji(e, this.context)),
          (this.gl = this.context.gl),
          (this.assetManager = new zi(this.context, t.pathPrefix)),
          (this.input = new mt(e)),
          t.app.loadAssets && t.app.loadAssets(this);
        let i = () => {
            this.disposed ||
              (requestAnimationFrame(i),
              this.time.update(),
              t.app.update && t.app.update(this, this.time.delta),
              t.app.render && t.app.render(this));
          },
          s = () => {
            if (!this.disposed) {
              if (this.assetManager.isLoadingComplete()) {
                this.assetManager.hasErrors()
                  ? t.app.error &&
                    t.app.error(this, this.assetManager.getErrors())
                  : (t.app.initialize && t.app.initialize(this), i());
                return;
              }
              requestAnimationFrame(s);
            }
          };
        requestAnimationFrame(s);
      }
      context;
      time = new bt();
      htmlCanvas;
      gl;
      renderer;
      assetManager;
      input;
      disposed = !1;
      clear(e, t, i, s) {
        this.gl.clearColor(e, t, i, s), this.gl.clear(this.gl.COLOR_BUFFER_BIT);
      }
      dispose() {
        this.config.app.dispose && this.config.app.dispose(this),
          (this.disposed = !0);
      }
    },
    tn = class {
      constructor(e, t) {
        this.config = t;
        let i = typeof e == "string" ? document.getElementById(e) : e;
        if (i == null) throw new Error("SpinePlayer parent not found: " + e);
        (this.parent = i), t.showControls === void 0 && (t.showControls = !0);
        let s = t.showControls
          ? `
<div class="spine-player-controls spine-player-popup-parent spine-player-controls-hidden">
<div class="spine-player-timeline"></div>
<div class="spine-player-buttons">
<button class="spine-player-button spine-player-button-icon-pause"></button>
<div class="spine-player-button-spacer"></div>
<button class="spine-player-button spine-player-button-icon-speed"></button>
<button class="spine-player-button spine-player-button-icon-animations"></button>
<button class="spine-player-button spine-player-button-icon-skins"></button>
<button class="spine-player-button spine-player-button-icon-settings"></button>
<button class="spine-player-button spine-player-button-icon-fullscreen"></button>
<img class="spine-player-button-icon-spine-logo" src="data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%20104%2031.16%22%3E%3Cpath%20d%3D%22M104%2012.68a1.31%201.31%200%200%201-.37%201%201.28%201.28%200%200%201-.85.31H91.57a10.51%2010.51%200%200%200%20.29%202.55%204.92%204.92%200%200%200%201%202%204.27%204.27%200%200%200%201.64%201.26%206.89%206.89%200%200%200%202.6.44%2010.66%2010.66%200%200%200%202.17-.2%2012.81%2012.81%200%200%200%201.64-.44q.69-.25%201.14-.44a1.87%201.87%200%200%201%20.68-.2.44.44%200%200%201%20.27.04.43.43%200%200%201%20.16.2%201.38%201.38%200%200%201%20.09.37%204.89%204.89%200%200%201%200%20.58%204.14%204.14%200%200%201%200%20.43v.32a.83.83%200%200%201-.09.26%201.1%201.1%200%200%201-.17.22%202.77%202.77%200%200%201-.61.34%208.94%208.94%200%200%201-1.32.46%2018.54%2018.54%200%200%201-1.88.41%2013.78%2013.78%200%200%201-2.28.18%2010.55%2010.55%200%200%201-3.68-.59%206.82%206.82%200%200%201-2.66-1.74%207.44%207.44%200%200%201-1.63-2.89%2013.48%2013.48%200%200%201-.55-4%2012.76%2012.76%200%200%201%20.57-3.94%208.35%208.35%200%200%201%201.64-3%207.15%207.15%200%200%201%202.58-1.87%208.47%208.47%200%200%201%203.39-.65%208.19%208.19%200%200%201%203.41.64%206.46%206.46%200%200%201%202.32%201.73%207%207%200%200%201%201.3%202.54%2011.17%2011.17%200%200%201%20.43%203.13zm-3.14-.93a5.69%205.69%200%200%200-1.09-3.86%204.17%204.17%200%200%200-3.42-1.4%204.52%204.52%200%200%200-2%20.44%204.41%204.41%200%200%200-1.47%201.15A5.29%205.29%200%200%200%2092%209.75a7%207%200%200%200-.36%202zM80.68%2021.94a.42.42%200%200%201-.08.26.59.59%200%200%201-.25.18%201.74%201.74%200%200%201-.47.11%206.31%206.31%200%200%201-.76%200%206.5%206.5%200%200%201-.78%200%201.74%201.74%200%200%201-.47-.11.59.59%200%200%201-.25-.18.42.42%200%200%201-.08-.26V12a9.8%209.8%200%200%200-.23-2.35%204.86%204.86%200%200%200-.66-1.53%202.88%202.88%200%200%200-1.13-1%203.57%203.57%200%200%200-1.6-.34%204%204%200%200%200-2.35.83A12.71%2012.71%200%200%200%2069.11%2010v11.9a.42.42%200%200%201-.08.26.59.59%200%200%201-.25.18%201.74%201.74%200%200%201-.47.11%206.51%206.51%200%200%201-.78%200%206.31%206.31%200%200%201-.76%200%201.88%201.88%200%200%201-.48-.11.52.52%200%200%201-.25-.18.46.46%200%200%201-.07-.26v-17a.53.53%200%200%201%20.03-.21.5.5%200%200%201%20.23-.19%201.28%201.28%200%200%201%20.44-.11%208.53%208.53%200%200%201%201.39%200%201.12%201.12%200%200%201%20.43.11.6.6%200%200%201%20.22.19.47.47%200%200%201%20.07.26V7.2a10.46%2010.46%200%200%201%202.87-2.36%206.17%206.17%200%200%201%202.88-.75%206.41%206.41%200%200%201%202.87.58%205.16%205.16%200%200%201%201.88%201.54%206.15%206.15%200%200%201%201%202.26%2013.46%2013.46%200%200%201%20.31%203.11z%22%20fill%3D%22%23fff%22%2F%3E%3Cpath%20d%3D%22M43.35%202.86c.09%202.6%201.89%204%205.48%204.61%203%20.48%205.79.24%206.69-2.37%201.75-5.09-2.4-3.82-6-4.39s-6.31-2.03-6.17%202.15zm1.08%2010.69c.33%201.94%202.14%203.06%204.91%203s4.84-1.16%205.13-3.25c.53-3.88-2.53-2.38-5.3-2.3s-5.4-1.26-4.74%202.55zM48%2022.44c.55%201.45%202.06%202.06%204.1%201.63s3.45-1.11%203.33-2.76c-.21-3.06-2.22-2.1-4.26-1.66S47%2019.6%2048%2022.44zm1.78%206.78c.16%201.22%201.22%202%202.88%201.93s2.92-.67%203.13-2c.4-2.43-1.46-1.53-3.12-1.51s-3.17-.82-2.89%201.58z%22%20fill%3D%22%23ff4000%22%2F%3E%3Cpath%20d%3D%22M35.28%2013.16a15.33%2015.33%200%200%201-.48%204%208.75%208.75%200%200%201-1.42%203%206.35%206.35%200%200%201-2.32%201.91%207.14%207.14%200%200%201-3.16.67%206.1%206.1%200%200%201-1.4-.15%205.34%205.34%200%200%201-1.26-.47%207.29%207.29%200%200%201-1.24-.81q-.61-.49-1.29-1.15v8.51a.47.47%200%200%201-.08.26.56.56%200%200%201-.25.19%201.74%201.74%200%200%201-.47.11%206.47%206.47%200%200%201-.78%200%206.26%206.26%200%200%201-.76%200%201.89%201.89%200%200%201-.48-.11.49.49%200%200%201-.25-.19.51.51%200%200%201-.07-.26V4.91a.57.57%200%200%201%20.06-.27.46.46%200%200%201%20.23-.18%201.47%201.47%200%200%201%20.44-.1%207.41%207.41%200%200%201%201.3%200%201.45%201.45%200%200%201%20.43.1.52.52%200%200%201%20.24.18.51.51%200%200%201%20.07.27V7.2a18.06%2018.06%200%200%201%201.49-1.38%209%209%200%200%201%201.45-1%206.82%206.82%200%200%201%201.49-.59%207.09%207.09%200%200%201%204.78.52%206%206%200%200%201%202.13%202%208.79%208.79%200%200%201%201.2%202.9%2015.72%2015.72%200%200%201%20.4%203.51zm-3.28.36a15.64%2015.64%200%200%200-.2-2.53%207.32%207.32%200%200%200-.69-2.17%204.06%204.06%200%200%200-1.3-1.51%203.49%203.49%200%200%200-2-.57%204.1%204.1%200%200%200-1.2.18%204.92%204.92%200%200%200-1.2.57%208.54%208.54%200%200%200-1.28%201A15.77%2015.77%200%200%200%2022.76%2010v6.77a13.53%2013.53%200%200%200%202.46%202.4%204.12%204.12%200%200%200%202.44.83%203.56%203.56%200%200%200%202-.57A4.28%204.28%200%200%200%2031%2018a7.58%207.58%200%200%200%20.77-2.12%2011.43%2011.43%200%200%200%20.23-2.36zM12%2017.3a5.39%205.39%200%200%201-.48%202.33%204.73%204.73%200%200%201-1.37%201.72%206.19%206.19%200%200%201-2.12%201.06%209.62%209.62%200%200%201-2.71.36%2010.38%2010.38%200%200%201-3.21-.5A7.63%207.63%200%200%201%201%2021.82a3.25%203.25%200%200%201-.66-.43%201.09%201.09%200%200%201-.3-.53%203.59%203.59%200%200%201-.04-.93%204.06%204.06%200%200%201%200-.61%202%202%200%200%201%20.09-.4.42.42%200%200%201%20.16-.22.43.43%200%200%201%20.24-.07%201.35%201.35%200%200%201%20.61.26q.41.26%201%20.56a9.22%209.22%200%200%200%201.41.55%206.25%206.25%200%200%200%201.87.26%205.62%205.62%200%200%200%201.44-.17%203.48%203.48%200%200%200%201.12-.5%202.23%202.23%200%200%200%20.73-.84%202.68%202.68%200%200%200%20.26-1.21%202%202%200%200%200-.37-1.21%203.55%203.55%200%200%200-1-.87%208.09%208.09%200%200%200-1.36-.66l-1.56-.61a16%2016%200%200%201-1.57-.73%206%206%200%200%201-1.37-1%204.52%204.52%200%200%201-1-1.4%204.69%204.69%200%200%201-.37-2%204.88%204.88%200%200%201%20.39-1.87%204.46%204.46%200%200%201%201.16-1.61%205.83%205.83%200%200%201%201.94-1.11A8.06%208.06%200%200%201%206.53%204a8.28%208.28%200%200%201%201.36.11%209.36%209.36%200%200%201%201.23.28%205.92%205.92%200%200%201%20.94.37%204.09%204.09%200%200%201%20.59.35%201%201%200%200%201%20.26.26.83.83%200%200%201%20.09.26%201.32%201.32%200%200%200%20.06.35%203.87%203.87%200%200%201%200%20.51%204.76%204.76%200%200%201%200%20.56%201.39%201.39%200%200%201-.09.39.5.5%200%200%201-.16.22.35.35%200%200%201-.21.07%201%201%200%200%201-.49-.21%207%207%200%200%200-.83-.44%209.26%209.26%200%200%200-1.2-.44%205.49%205.49%200%200%200-1.58-.16%204.93%204.93%200%200%200-1.4.18%202.69%202.69%200%200%200-1%20.51%202.16%202.16%200%200%200-.59.83%202.43%202.43%200%200%200-.2%201%202%202%200%200%200%20.38%201.24%203.6%203.6%200%200%200%201%20.88%208.25%208.25%200%200%200%201.38.68l1.58.62q.8.32%201.59.72a6%206%200%200%201%201.39%201%204.37%204.37%200%200%201%201%201.36%204.46%204.46%200%200%201%20.37%201.8z%22%20fill%3D%22%23fff%22%2F%3E%3C%2Fsvg%3E">
</div></div>`
          : "";
        this.parent.appendChild(
          (this.dom = ze(
            `<div class="spine-player" style="position:relative;height:100%"><canvas class="spine-player-canvas" style="display:block;width:100%;height:100%"></canvas>${s}</div>`
          ))
        );
        try {
          this.validateConfig(t);
        } catch (n) {
          this.showError(n.message, n);
        }
        this.initialize(),
          this.addEventListener(window, "resize", () => this.drawFrame(!1)),
          requestAnimationFrame(() => this.drawFrame());
      }
      parent;
      dom;
      canvas = null;
      context = null;
      sceneRenderer = null;
      loadingScreen = null;
      assetManager = null;
      bg = new V();
      bgFullscreen = new V();
      playerControls = null;
      timelineSlider = null;
      playButton = null;
      skinButton = null;
      animationButton = null;
      playTime = 0;
      selectedBones = [];
      cancelId = 0;
      popup = null;
      error = !1;
      skeleton = null;
      animationState = null;
      paused = !0;
      speed = 1;
      time = new bt();
      stopRequestAnimationFrame = !1;
      disposed = !1;
      viewport = {};
      currentViewport = {};
      previousViewport = {};
      viewportTransitionStart = 0;
      eventListeners = [];
      dispose() {
        this.sceneRenderer?.dispose(),
          this.loadingScreen?.dispose(),
          this.assetManager?.dispose();
        for (var e = 0; e < this.eventListeners.length; e++) {
          var t = this.eventListeners[e];
          t.target.removeEventListener(t.event, t.func);
        }
        this.parent.removeChild(this.dom), (this.disposed = !0);
      }
      addEventListener(e, t, i) {
        this.eventListeners.push({ target: e, event: t, func: i }),
          e.addEventListener(t, i);
      }
      validateConfig(e) {
        if (!e)
          throw new Error(
            "A configuration object must be passed to to new SpinePlayer()."
          );
        if (
          (e.skelUrl && (e.skeleton = e.skelUrl),
          !e.skeleton && !e.jsonUrl && !e.binaryUrl)
        )
          throw new Error(
            "A URL must be specified for the skeleton JSON or binary file."
          );
        if ((e.scale || (e.scale = 1), !e.atlas && !e.atlasUrl))
          throw new Error("A URL must be specified for the atlas file.");
        if (
          (e.jsonUrl && !e.skeleton && (e.skeleton = e.jsonUrl),
          e.binaryUrl && !e.skeleton && (e.skeleton = e.binaryUrl),
          e.atlasUrl && !e.atlas && (e.atlas = e.atlasUrl),
          e.backgroundColor ||
            (e.backgroundColor = e.alpha ? "00000000" : "000000"),
          e.fullScreenBackgroundColor ||
            (e.fullScreenBackgroundColor = e.backgroundColor),
          e.backgroundImage &&
            !e.backgroundImage.url &&
            (e.backgroundImage = void 0),
          e.premultipliedAlpha === void 0 && (e.premultipliedAlpha = !0),
          e.preserveDrawingBuffer === void 0 && (e.preserveDrawingBuffer = !1),
          e.mipmaps === void 0 && (e.mipmaps = !0),
          e.debug ||
            (e.debug = {
              bones: !1,
              clipping: !1,
              bounds: !1,
              hulls: !1,
              meshes: !1,
              paths: !1,
              points: !1,
              regions: !1,
            }),
          e.animations && e.animation && e.animations.indexOf(e.animation) < 0)
        )
          throw new Error(
            "Animation '" +
              e.animation +
              "' is not in the config animation list: " +
              Qi(e.animations)
          );
        if (e.skins && e.skin && e.skins.indexOf(e.skin) < 0)
          throw new Error(
            "Default skin '" +
              e.skin +
              "' is not in the config skins list: " +
              Qi(e.skins)
          );
        e.viewport || (e.viewport = {}),
          e.viewport.animations || (e.viewport.animations = {}),
          e.viewport.debugRender === void 0 && (e.viewport.debugRender = !1),
          e.viewport.transitionTime === void 0 &&
            (e.viewport.transitionTime = 0.25),
          e.controlBones || (e.controlBones = []),
          e.showLoading === void 0 && (e.showLoading = !0),
          e.defaultMix === void 0 && (e.defaultMix = 0.25);
      }
      initialize() {
        let e = this.config,
          t = this.dom;
        if (!e.alpha) {
          let i = e.backgroundColor;
          this.dom.style.backgroundColor = (
            i.charAt(0) == "#" ? i : "#" + i
          ).substr(0, 7);
        }
        try {
          (this.canvas = Oe(t, "spine-player-canvas")),
            (this.context = new pe(this.canvas, {
              alpha: e.alpha,
              preserveDrawingBuffer: e.preserveDrawingBuffer,
            })),
            (this.sceneRenderer = new Ji(this.canvas, this.context, !0)),
            e.showLoading && (this.loadingScreen = new Ws(this.sceneRenderer));
        } catch (i) {
          return (
            this.showError(
              `Sorry, your browser does not support WebGL, or you have disabled WebGL in your browser settings.
Please use the latest version of Firefox, Chrome, Edge, or Safari.`,
              i
            ),
            null
          );
        }
        if (
          ((this.assetManager = new zi(this.context, "", e.downloader)),
          e.rawDataURIs)
        )
          for (let i in e.rawDataURIs)
            this.assetManager.setRawDataURI(i, e.rawDataURIs[i]);
        if (
          (e.skeleton.endsWith(".json")
            ? this.assetManager.loadJson(e.skeleton)
            : this.assetManager.loadBinary(e.skeleton),
          this.assetManager.loadTextureAtlas(e.atlas),
          e.backgroundImage &&
            this.assetManager.loadTexture(e.backgroundImage.url),
          this.bg.setFromString(e.backgroundColor),
          this.bgFullscreen.setFromString(e.fullScreenBackgroundColor),
          e.showControls)
        ) {
          this.playerControls = t.children[1];
          let i = this.playerControls.children,
            s = i[0],
            n = i[1].children;
          this.playButton = n[0];
          let d = n[2];
          (this.animationButton = n[3]), (this.skinButton = n[4]);
          let a = n[5],
            r = n[6],
            o = n[7];
          (this.timelineSlider = new zs()),
            s.appendChild(this.timelineSlider.create()),
            (this.timelineSlider.change = (m) => {
              this.pause();
              let b = this.animationState.getCurrent(0).animation.duration * m;
              this.animationState.update(b - this.playTime),
                this.animationState.apply(this.skeleton),
                this.skeleton.update(b - this.playTime),
                this.skeleton.updateWorldTransform(2),
                (this.playTime = b);
            }),
            (this.playButton.onclick = () =>
              this.paused ? this.play() : this.pause()),
            (d.onclick = () => this.showSpeedDialog(d)),
            (this.animationButton.onclick = () =>
              this.showAnimationsDialog(this.animationButton)),
            (this.skinButton.onclick = () =>
              this.showSkinsDialog(this.skinButton)),
            (a.onclick = () => this.showSettingsDialog(a));
          let l = this.canvas.clientWidth,
            h = this.canvas.clientHeight,
            u = this.canvas.style.width,
            c = this.canvas.style.height,
            f = !1;
          (r.onclick = () => {
            let m = () => {
                (f = !f),
                  f ||
                    ((this.canvas.style.width = l + "px"),
                    (this.canvas.style.height = h + "px"),
                    this.drawFrame(!1),
                    requestAnimationFrame(() => {
                      (this.canvas.style.width = u),
                        (this.canvas.style.height = c);
                    }));
              },
              g = t;
            (g.onfullscreenchange = m), (g.onwebkitfullscreenchange = m);
            let b = document;
            b.fullscreenElement ||
            b.webkitFullscreenElement ||
            b.mozFullScreenElement ||
            b.msFullscreenElement
              ? b.exitFullscreen
                ? b.exitFullscreen()
                : b.mozCancelFullScreen
                ? b.mozCancelFullScreen()
                : b.webkitExitFullscreen
                ? b.webkitExitFullscreen()
                : b.msExitFullscreen && b.msExitFullscreen()
              : ((l = this.canvas.clientWidth),
                (h = this.canvas.clientHeight),
                (u = this.canvas.style.width),
                (c = this.canvas.style.height),
                g.requestFullscreen
                  ? g.requestFullscreen()
                  : g.webkitRequestFullScreen
                  ? g.webkitRequestFullScreen()
                  : g.mozRequestFullScreen
                  ? g.mozRequestFullScreen()
                  : g.msRequestFullscreen && g.msRequestFullscreen());
          }),
            (o.onclick = () => window.open("http://esotericsoftware.com"));
        }
        return t;
      }
      loadSkeleton() {
        if (this.error) return;
        this.assetManager.hasErrors() &&
          this.showError(
            `Error: Assets could not be loaded.
` + Qi(this.assetManager.getErrors())
          );
        let e = this.config,
          t = this.assetManager.require(e.atlas),
          i = this.context.gl,
          s = i.getExtension("EXT_texture_filter_anisotropic"),
          n = i.getParameter(i.VERSION).indexOf("WebGL 1.0") != -1;
        for (let h of t.pages) {
          let u = h.minFilter;
          var d = e.mipmaps,
            a = X.isPowerOfTwo(h.width) && X.isPowerOfTwo(h.height);
          n && !a && (d = !1),
            d &&
              (s
                ? (i.texParameterf(
                    i.TEXTURE_2D,
                    s.TEXTURE_MAX_ANISOTROPY_EXT,
                    8
                  ),
                  (u = 9987))
                : (u = 9729),
              h.texture.setFilters(u, 9728)),
            u != 9728 && u != 9729 && h.texture.update(!0);
        }
        let r;
        try {
          let h,
            u,
            c = new As(t);
          if (e.skeleton.endsWith(".json")) {
            if (((u = this.assetManager.remove(e.skeleton)), !u))
              throw new Error("Empty JSON data.");
            if (e.jsonField && ((u = u[e.jsonField]), !u))
              throw new Error("JSON field does not exist: " + e.jsonField);
            h = new Ys(c);
          } else (u = this.assetManager.remove(e.skeleton)), (h = new Ms(c));
          (h.scale = e.scale), (r = h.readSkeletonData(u));
        } catch (h) {
          this.showError(
            `Error: Could not load skeleton data.
${h.message}`,
            h
          );
          return;
        }
        this.skeleton = new ut(r);
        let o = new ps(r);
        (o.defaultMix = e.defaultMix),
          (this.animationState = new Ci(o)),
          e.controlBones.forEach((h) => {
            r.findBone(h) ||
              this.showError(
                `Error: Control bone does not exist in skeleton: ${h}`
              );
          }),
          !e.skin && r.skins.length && (e.skin = r.skins[0].name),
          e.skins &&
            e.skin.length &&
            e.skins.forEach((h) => {
              this.skeleton.data.findSkin(h) ||
                this.showError(
                  `Error: Skin in config list does not exist in skeleton: ${h}`
                );
            }),
          e.skin &&
            (this.skeleton.data.findSkin(e.skin) ||
              this.showError(
                `Error: Skin does not exist in skeleton: ${e.skin}`
              ),
            this.skeleton.setSkinByName(e.skin),
            this.skeleton.setSlotsToSetupPose()),
          Object.getOwnPropertyNames(e.viewport.animations).forEach((h) => {
            r.findAnimation(h) ||
              this.showError(
                `Error: Animation for which a viewport was specified does not exist in skeleton: ${h}`
              );
          }),
          e.animations &&
            e.animations.length &&
            (e.animations.forEach((h) => {
              this.skeleton.data.findAnimation(h) ||
                this.showError(
                  `Error: Animation in config list does not exist in skeleton: ${h}`
                );
            }),
            e.animation || (e.animation = e.animations[0])),
          e.animation &&
            !r.findAnimation(e.animation) &&
            this.showError(
              `Error: Animation does not exist in skeleton: ${e.animation}`
            ),
          this.setupInput(),
          e.showControls &&
            ((r.skins.length == 1 || (e.skins && e.skins.length == 1)) &&
              this.skinButton.classList.add("spine-player-hidden"),
            (r.animations.length == 1 ||
              (e.animations && e.animations.length == 1)) &&
              this.animationButton.classList.add("spine-player-hidden")),
          e.success && e.success(this);
        let l = this.animationState.getCurrent(0);
        l
          ? (this.currentViewport.x === void 0 && this.setViewport(l.animation),
            e.animation || (e.animation = l.animation?.name),
            this.play())
          : e.animation
          ? ((l = this.setAnimation(e.animation)), this.play())
          : ((l = this.animationState.setEmptyAnimation(0)),
            (l.trackEnd = 1e8),
            this.skeleton.updateWorldTransform(2),
            this.setViewport(l.animation),
            this.pause());
      }
      setupInput() {
        let e = this.config,
          t = e.controlBones;
        if (!t.length && !e.showControls) return;
        let i = (this.selectedBones = new Array(t.length)),
          s = this.canvas,
          n = null,
          d = new Me(),
          a = new Se(),
          r = new Se(),
          o = new Me(),
          l = this.skeleton,
          h = this.sceneRenderer,
          u = function (c, f) {
            r.set(c, s.clientHeight - f, 0), (d.x = d.y = 0);
            let m = 24,
              g = 0,
              b = null;
            for (let x = 0; x < t.length; x++) {
              i[x] = null;
              let p = l.findBone(t[x]);
              if (!p) continue;
              let w = h.camera
                .worldToScreen(
                  a.set(p.worldX, p.worldY, 0),
                  s.clientWidth,
                  s.clientHeight
                )
                .distance(r);
              w < m &&
                ((m = w),
                (b = p),
                (g = x),
                (d.x = a.x - r.x),
                (d.y = a.y - r.y));
            }
            return b && (i[g] = b), b;
          };
        if (
          (new mt(s).addListener({
            down: (c, f) => {
              n = u(c, f);
            },
            up: () => {
              n
                ? (n = null)
                : e.showControls && (this.paused ? this.play() : this.pause());
            },
            dragged: (c, f) => {
              n &&
                ((c = X.clamp(c + d.x, 0, s.clientWidth)),
                (f = X.clamp(f - d.y, 0, s.clientHeight)),
                h.camera.screenToWorld(
                  a.set(c, f, 0),
                  s.clientWidth,
                  s.clientHeight
                ),
                n.parent
                  ? (n.parent.worldToLocal(o.set(a.x - l.x, a.y - l.y)),
                    (n.x = o.x),
                    (n.y = o.y))
                  : ((n.x = a.x - l.x), (n.y = a.y - l.y)));
            },
            moved: (c, f) => u(c, f),
          }),
          e.showControls)
        ) {
          this.addEventListener(document, "mousemove", (b) => {
            b instanceof MouseEvent && g(b.clientX, b.clientY);
          }),
            this.addEventListener(document, "touchmove", (b) => {
              if (b instanceof TouchEvent) {
                let x = b.changedTouches;
                if (x.length) {
                  let p = x[0];
                  g(p.clientX, p.clientY);
                }
              }
            });
          let c = (b, x, p) => {
              let w = b - p.left,
                y = x - p.top;
              return w >= 0 && w <= p.width && y >= 0 && y <= p.height;
            },
            f = !0,
            m = !1,
            g = (b, x) => {
              let p = Oe(this.dom, "spine-player-popup");
              (f = c(b, x, this.playerControls.getBoundingClientRect())),
                (m = c(b, x, s.getBoundingClientRect())),
                clearTimeout(this.cancelId),
                !p && !f && !m && !this.paused
                  ? this.playerControls.classList.add(
                      "spine-player-controls-hidden"
                    )
                  : this.playerControls.classList.remove(
                      "spine-player-controls-hidden"
                    ),
                !f &&
                  !p &&
                  !this.paused &&
                  (this.cancelId = setTimeout(() => {
                    this.paused ||
                      this.playerControls.classList.add(
                        "spine-player-controls-hidden"
                      );
                  }, 1e3));
            };
        }
      }
      play() {
        this.paused = !1;
        let e = this.config;
        e.showControls &&
          ((this.cancelId = setTimeout(() => {
            this.paused ||
              this.playerControls.classList.add("spine-player-controls-hidden");
          }, 1e3)),
          this.playButton.classList.remove("spine-player-button-icon-play"),
          this.playButton.classList.add("spine-player-button-icon-pause"),
          e.animation ||
            (e.animations && e.animations.length
              ? (e.animation = e.animations[0])
              : this.skeleton.data.animations.length &&
                (e.animation = this.skeleton.data.animations[0].name),
            e.animation && this.setAnimation(e.animation)));
      }
      pause() {
        (this.paused = !0),
          this.config.showControls &&
            (this.playerControls.classList.remove(
              "spine-player-controls-hidden"
            ),
            clearTimeout(this.cancelId),
            this.playButton.classList.remove("spine-player-button-icon-pause"),
            this.playButton.classList.add("spine-player-button-icon-play"));
      }
      setAnimation(e, t = !0) {
        return (
          (e = this.setViewport(e)),
          this.animationState.setAnimationWith(0, e, t)
        );
      }
      addAnimation(e, t = !0, i = 0) {
        return (
          (e = this.setViewport(e)),
          this.animationState.addAnimationWith(0, e, t, i)
        );
      }
      setViewport(e) {
        if (typeof e == "string") {
          let n = this.skeleton.data.findAnimation(e);
          if (!n) throw new Error("Animation not found: " + e);
          e = n;
        }
        this.previousViewport = this.currentViewport;
        let t = this.config.viewport,
          i = (this.currentViewport = {
            padLeft: t.padLeft !== void 0 ? t.padLeft : "10%",
            padRight: t.padRight !== void 0 ? t.padRight : "10%",
            padTop: t.padTop !== void 0 ? t.padTop : "10%",
            padBottom: t.padBottom !== void 0 ? t.padBottom : "10%",
          });
        t.x !== void 0 && t.y !== void 0 && t.width && t.height
          ? ((i.x = t.x),
            (i.y = t.y),
            (i.width = t.width),
            (i.height = t.height))
          : this.calculateAnimationViewport(e, i);
        let s = this.config.viewport.animations[e.name];
        return (
          s &&
            (s.x !== void 0 &&
              s.y !== void 0 &&
              s.width &&
              s.height &&
              ((i.x = s.x),
              (i.y = s.y),
              (i.width = s.width),
              (i.height = s.height)),
            s.padLeft !== void 0 && (i.padLeft = s.padLeft),
            s.padRight !== void 0 && (i.padRight = s.padRight),
            s.padTop !== void 0 && (i.padTop = s.padTop),
            s.padBottom !== void 0 && (i.padBottom = s.padBottom)),
          (i.padLeft = this.percentageToWorldUnit(i.width, i.padLeft)),
          (i.padRight = this.percentageToWorldUnit(i.width, i.padRight)),
          (i.padBottom = this.percentageToWorldUnit(i.height, i.padBottom)),
          (i.padTop = this.percentageToWorldUnit(i.height, i.padTop)),
          (this.viewportTransitionStart = performance.now()),
          e
        );
      }
      percentageToWorldUnit(e, t) {
        return typeof t == "string"
          ? (e * parseFloat(t.substr(0, t.length - 1))) / 100
          : t;
      }
      calculateAnimationViewport(e, t) {
        this.skeleton.setToSetupPose();
        let i = 100,
          s = e.duration ? e.duration / i : 0,
          n = 0,
          d = 1e8,
          a = -1e8,
          r = 1e8,
          o = -1e8,
          l = new Me(),
          h = new Me();
        const u = new Array(2);
        for (let c = 0; c < i; c++, n += s)
          e.apply(this.skeleton, n, n, !1, [], 1, 0, 0),
            this.skeleton.updateWorldTransform(2),
            this.skeleton.getBounds(
              l,
              h,
              u,
              this.sceneRenderer.skeletonRenderer.getSkeletonClipping()
            ),
            !isNaN(l.x) && !isNaN(l.y) && !isNaN(h.x) && !isNaN(h.y)
              ? ((d = Math.min(l.x, d)),
                (a = Math.max(l.x + h.x, a)),
                (r = Math.min(l.y, r)),
                (o = Math.max(l.y + h.y, o)))
              : this.showError("Animation bounds are invalid: " + e.name);
        (t.x = d), (t.y = r), (t.width = a - d), (t.height = o - r);
      }
      drawFrame(e = !0) {
        try {
          if (this.error || this.disposed) return;
          e &&
            !this.stopRequestAnimationFrame &&
            requestAnimationFrame(() => this.drawFrame());
          let t = document,
            s =
              t.fullscreenElement ||
              t.webkitFullscreenElement ||
              t.mozFullScreenElement ||
              t.msFullscreenElement
                ? this.bgFullscreen
                : this.bg;
          this.time.update();
          let n = this.time.delta,
            d = !this.assetManager.isLoadingComplete();
          !this.skeleton && !d && this.loadSkeleton();
          let a = this.skeleton,
            r = this.config;
          if (a) {
            let o = this.sceneRenderer;
            o.resize(1);
            let l = this.paused ? 0 : n * this.speed;
            if (
              (r.frame && r.frame(this, l),
              !this.paused &&
                (a.update(l),
                this.animationState.update(l),
                this.animationState.apply(a),
                r.updateWorldTransform
                  ? r.updateWorldTransform(this, l)
                  : a.updateWorldTransform(2),
                r.showControls))
            ) {
              this.playTime += l;
              let m = this.animationState.getCurrent(0);
              if (m) {
                let g = m.animation.duration;
                for (; this.playTime >= g && g != 0; ) this.playTime -= g;
                (this.playTime = Math.max(0, Math.min(this.playTime, g))),
                  this.timelineSlider.setValue(this.playTime / g);
              }
            }
            let h = this.viewport;
            if (
              ((h.x = this.currentViewport.x - this.currentViewport.padLeft),
              (h.y = this.currentViewport.y - this.currentViewport.padBottom),
              (h.width =
                this.currentViewport.width +
                this.currentViewport.padLeft +
                this.currentViewport.padRight),
              (h.height =
                this.currentViewport.height +
                this.currentViewport.padBottom +
                this.currentViewport.padTop),
              this.previousViewport)
            ) {
              let m =
                (performance.now() - this.viewportTransitionStart) /
                1e3 /
                r.viewport.transitionTime;
              if (m < 1) {
                let g = this.previousViewport.x - this.previousViewport.padLeft,
                  b = this.previousViewport.y - this.previousViewport.padBottom,
                  x =
                    this.previousViewport.width +
                    this.previousViewport.padLeft +
                    this.previousViewport.padRight,
                  p =
                    this.previousViewport.height +
                    this.previousViewport.padBottom +
                    this.previousViewport.padTop;
                (h.x = g + (h.x - g) * m),
                  (h.y = b + (h.y - b) * m),
                  (h.width = x + (h.width - x) * m),
                  (h.height = p + (h.height - p) * m);
              }
            }
            (o.camera.zoom =
              this.canvas.height / this.canvas.width > h.height / h.width
                ? h.width / this.canvas.width
                : h.height / this.canvas.height),
              (o.camera.position.x = h.x + h.width / 2),
              (o.camera.position.y = h.y + h.height / 2);
            let u = this.context.gl;
            u.clearColor(s.r, s.g, s.b, s.a),
              u.clear(u.COLOR_BUFFER_BIT),
              r.update && r.update(this, l),
              o.begin();
            let c = r.backgroundImage;
            if (c) {
              let m = this.assetManager.require(c.url);
              c.x !== void 0 && c.y !== void 0 && c.width && c.height
                ? o.drawTexture(m, c.x, c.y, c.width, c.height)
                : o.drawTexture(m, h.x, h.y, h.width, h.height);
            }
            o.drawSkeleton(a, r.premultipliedAlpha),
              Number(
                (o.skeletonDebugRenderer.drawBones = r.debug.bones ?? !1)
              ) +
                Number(
                  (o.skeletonDebugRenderer.drawBoundingBoxes =
                    r.debug.bounds ?? !1)
                ) +
                Number(
                  (o.skeletonDebugRenderer.drawClipping =
                    r.debug.clipping ?? !1)
                ) +
                Number(
                  (o.skeletonDebugRenderer.drawMeshHull = r.debug.hulls ?? !1)
                ) +
                Number(
                  (o.skeletonDebugRenderer.drawPaths = r.debug.paths ?? !1)
                ) +
                Number(
                  (o.skeletonDebugRenderer.drawRegionAttachments =
                    r.debug.regions ?? !1)
                ) +
                Number(
                  (o.skeletonDebugRenderer.drawMeshTriangles =
                    r.debug.meshes ?? !1)
                ) >
                0 && o.drawSkeletonDebug(a, r.premultipliedAlpha);
            let f = r.controlBones;
            if (f.length) {
              let m = this.selectedBones;
              u.lineWidth(2);
              for (let g = 0; g < f.length; g++) {
                let b = a.findBone(f[g]);
                if (!b) continue;
                let x = m[g] ? rn : an,
                  p = m[g] ? nn : ln;
                o.circle(!0, a.x + b.worldX, a.y + b.worldY, 20, x),
                  o.circle(!1, a.x + b.worldX, a.y + b.worldY, 20, p);
              }
            }
            r.viewport.debugRender &&
              (u.lineWidth(1),
              o.rect(
                !1,
                this.currentViewport.x,
                this.currentViewport.y,
                this.currentViewport.width,
                this.currentViewport.height,
                V.GREEN
              ),
              o.rect(!1, h.x, h.y, h.width, h.height, V.RED)),
              o.end(),
              r.draw && r.draw(this, l);
          }
          r.showLoading &&
            (this.loadingScreen.backgroundColor.setFromColor(s),
            this.loadingScreen.draw(!d)),
            d && r.loading && r.loading(this, n);
        } catch (t) {
          this.showError(
            `Error: Unable to render skeleton.
${t.message}`,
            t
          );
        }
      }
      startRendering() {
        (this.stopRequestAnimationFrame = !1),
          requestAnimationFrame(() => this.drawFrame());
      }
      stopRendering() {
        this.stopRequestAnimationFrame = !0;
      }
      hidePopup(e) {
        return this.popup != null && this.popup.hide(e);
      }
      showSpeedDialog(e) {
        let t = "speed";
        if (this.hidePopup(t)) return;
        let i = new _t(
            t,
            e,
            this,
            this.playerControls,
            `
<div class="spine-player-popup-title">Speed</div>
<hr>
<div class="spine-player-row" style="align-items:center;padding:8px">
<div class="spine-player-column">
	<div class="spine-player-speed-slider" style="margin-bottom:4px"></div>
	<div class="spine-player-row" style="justify-content:space-between"><div>0.1x</div><div>1x</div><div>2x</div></div>
</div>
</div>`
          ),
          s = new zs(2, 0.1, !0);
        Oe(i.dom, "spine-player-speed-slider").appendChild(s.create()),
          s.setValue(this.speed / 2),
          (s.change = (n) => (this.speed = n * 2)),
          i.show();
      }
      showAnimationsDialog(e) {
        let t = "animations";
        if (
          this.hidePopup(t) ||
          !this.skeleton ||
          !this.skeleton.data.animations.length
        )
          return;
        let i = new _t(
            t,
            e,
            this,
            this.playerControls,
            '<div class="spine-player-popup-title">Animations</div><hr><ul class="spine-player-list"></ul>'
          ),
          s = Oe(i.dom, "spine-player-list");
        this.skeleton.data.animations.forEach((n) => {
          if (
            this.config.animations &&
            this.config.animations.indexOf(n.name) < 0
          )
            return;
          let d = ze(
            '<li class="spine-player-list-item selectable"><div class="selectable-circle"></div><div class="selectable-text"></div></li>'
          );
          n.name == this.config.animation && d.classList.add("selected"),
            (Oe(d, "selectable-text").innerText = n.name),
            s.appendChild(d),
            (d.onclick = () => {
              qs(s.children, "selected"),
                d.classList.add("selected"),
                (this.config.animation = n.name),
                (this.playTime = 0),
                this.setAnimation(n.name),
                this.play();
            });
        }),
          i.show();
      }
      showSkinsDialog(e) {
        let t = "skins";
        if (
          this.hidePopup(t) ||
          !this.skeleton ||
          !this.skeleton.data.animations.length
        )
          return;
        let i = new _t(
            t,
            e,
            this,
            this.playerControls,
            '<div class="spine-player-popup-title">Skins</div><hr><ul class="spine-player-list"></ul>'
          ),
          s = Oe(i.dom, "spine-player-list");
        this.skeleton.data.skins.forEach((n) => {
          if (this.config.skins && this.config.skins.indexOf(n.name) < 0)
            return;
          let d = ze(
            '<li class="spine-player-list-item selectable"><div class="selectable-circle"></div><div class="selectable-text"></div></li>'
          );
          n.name == this.config.skin && d.classList.add("selected"),
            (Oe(d, "selectable-text").innerText = n.name),
            s.appendChild(d),
            (d.onclick = () => {
              qs(s.children, "selected"),
                d.classList.add("selected"),
                (this.config.skin = n.name),
                this.skeleton.setSkinByName(this.config.skin),
                this.skeleton.setSlotsToSetupPose();
            });
        }),
          i.show();
      }
      showSettingsDialog(e) {
        let t = "settings";
        if (
          this.hidePopup(t) ||
          !this.skeleton ||
          !this.skeleton.data.animations.length
        )
          return;
        let i = new _t(
            t,
            e,
            this,
            this.playerControls,
            '<div class="spine-player-popup-title">Debug</div><hr><ul class="spine-player-list"></li>'
          ),
          s = Oe(i.dom, "spine-player-list"),
          n = (d, a) => {
            let r = ze('<li class="spine-player-list-item"></li>'),
              o = new sn(d);
            r.appendChild(o.create());
            let l = this.config.debug;
            o.setEnabled(l[a]),
              (o.change = (h) => (l[a] = h)),
              s.appendChild(r);
          };
        n("Bones", "bones"),
          n("Regions", "regions"),
          n("Meshes", "meshes"),
          n("Bounds", "bounds"),
          n("Paths", "paths"),
          n("Clipping", "clipping"),
          n("Points", "points"),
          n("Hulls", "hulls"),
          i.show();
      }
      showError(e, t) {
        if (this.error) {
          if (t) throw t;
        } else {
          throw (
            ((this.error = !0),
            this.dom.appendChild(
              ze(
                '<div class="spine-player-error" style="background:#000;color:#fff;position:absolute;top:0;width:100%;height:100%;display:flex;justify-content:center;align-items:center;overflow:auto;z-index:999">' +
                  e.replace(
                    `
`,
                    "<br><br>"
                  ) +
                  "</div>"
              )
            ),
            this.config.error && this.config.error(this, e),
            t || new Error(e))
          );
          console.log(t);
        }
      }
    },
    _t = class {
      constructor(e, t, i, s, n) {
        (this.id = e),
          (this.button = t),
          (this.player = i),
          (this.dom = ze(
            '<div class="spine-player-popup spine-player-hidden"></div>'
          )),
          (this.dom.innerHTML = n),
          s.appendChild(this.dom),
          (this.className = "spine-player-button-icon-" + e + "-selected");
      }
      dom;
      className;
      windowClickListener;
      dispose() {}
      hide(e) {
        return (
          this.dom.remove(),
          this.button.classList.remove(this.className),
          this.id == e ? ((this.player.popup = null), !0) : !1
        );
      }
      show() {
        (this.player.popup = this),
          this.button.classList.add(this.className),
          this.dom.classList.remove("spine-player-hidden");
        let e = !1,
          t = () => {
            e || requestAnimationFrame(t);
            let n = this.player.dom,
              d = Math.abs(
                n.getBoundingClientRect().bottom -
                  n.getBoundingClientRect().bottom
              ),
              a = Math.abs(
                n.getBoundingClientRect().right -
                  n.getBoundingClientRect().right
              );
            this.dom.style.maxHeight = n.clientHeight - d - a + "px";
          };
        requestAnimationFrame(t);
        let i = !0,
          s = (n) => {
            if (i || this.player.popup != this) {
              i = !1;
              return;
            }
            this.dom.contains(n.target) ||
              (this.dom.remove(),
              window.removeEventListener("click", s),
              this.button.classList.remove(this.className),
              (this.player.popup = null),
              (e = !0));
          };
        this.player.addEventListener(window, "click", s);
      }
    },
    sn = class {
      constructor(e) {
        this.text = e;
      }
      switch = null;
      enabled = !1;
      change = () => {};
      create() {
        return (
          (this.switch = ze(`
<div class="spine-player-switch">
	<span class="spine-player-switch-text">${this.text}</span>
	<div class="spine-player-switch-knob-area">
		<div class="spine-player-switch-knob"></div>
	</div>
</div>`)),
          this.switch.addEventListener("click", () => {
            this.setEnabled(!this.enabled),
              this.change && this.change(this.enabled);
          }),
          this.switch
        );
      }
      setEnabled(e) {
        e
          ? this.switch?.classList.add("active")
          : this.switch?.classList.remove("active"),
          (this.enabled = e);
      }
      isEnabled() {
        return this.enabled;
      }
    },
    zs = class {
      constructor(e = 0, t = 0.1, i = !1) {
        (this.snaps = e), (this.snapPercentage = t), (this.big = i);
      }
      slider = null;
      value = null;
      knob = null;
      change = () => {};
      create() {
        (this.slider = ze(`
<div class="spine-player-slider ${this.big ? "big" : ""}">
	<div class="spine-player-slider-value"></div>
	<!--<div class="spine-player-slider-knob"></div>-->
</div>`)),
          (this.value = Oe(this.slider, "spine-player-slider-value")),
          this.setValue(0);
        let e = !1;
        return (
          new mt(this.slider).addListener({
            down: (t, i) => {
              (e = !0), this.value?.classList.add("hovering");
            },
            up: (t, i) => {
              (e = !1),
                this.change &&
                  this.change(this.setValue(t / this.slider.clientWidth)),
                this.value?.classList.remove("hovering");
            },
            moved: (t, i) => {
              e &&
                this.change &&
                this.change(this.setValue(t / this.slider.clientWidth));
            },
            dragged: (t, i) => {
              this.change &&
                this.change(this.setValue(t / this.slider.clientWidth));
            },
          }),
          this.slider
        );
      }
      setValue(e) {
        if (((e = Math.max(0, Math.min(1, e))), this.snaps)) {
          let t = 1 / this.snaps,
            i = e % t;
          i < t * this.snapPercentage
            ? (e = e - i)
            : i > t - t * this.snapPercentage && (e = e - i + t),
            (e = Math.max(0, Math.min(1, e)));
        }
        return (this.value.style.width = "" + e * 100 + "%"), e;
      }
    };
  function Oe(e, t) {
    return e.getElementsByClassName(t)[0];
  }
  function ze(e) {
    let t = document.createElement("div");
    return (t.innerHTML = e), t.children[0];
  }
  function qs(e, t) {
    for (let i = 0; i < e.length; i++) e[i].classList.remove(t);
  }
  function Qi(e) {
    return JSON.stringify(e)
      .replace(/&/g, "&amp;")
      .replace(/</g, "&lt;")
      .replace(/>/g, "&gt;")
      .replace(/"/g, "&#34;")
      .replace(/'/g, "&#39;");
  }
  var rn = new V(0.478, 0, 0, 0.25),
    nn = new V(1, 1, 1, 1),
    an = new V(0.478, 0, 0, 0.5),
    ln = new V(1, 0, 0, 0.8);
  function on(e) {
    return new Promise((t, i) => {
      const s = document.createElement("script");
      (s.src = e),
        (s.onload = () => t()),
        (s.onerror = () => i(new Error(`Script load error for ${e}`))),
        document.head.appendChild(s);
    });
  }
  function hn(e) {
    return new Promise((t, i) => {
      const s = document.createElement("link");
      (s.href = e),
        (s.rel = "stylesheet"),
        (s.onload = () => t()),
        (s.onerror = () => i(new Error(`CSS load error for ${e}`))),
        document.head.appendChild(s);
    });
  }
  var Gs = class {
      constructor(e) {
        (this.parent = e), this.load();
      }
      prefix = `<html>
<head>
<style>
body { margin: 0px; }
</style>
</head>
<body>`.trim();
      postfix = "</body>";
      code;
      player;
      async load() {
        await Promise.all([
          on("https://www.unpkg.com/codemirror@5.51.0/lib/codemirror.js"),
          hn("https://www.unpkg.com/codemirror@5.51.0/lib/codemirror.css"),
        ]),
          this.render(this.parent);
      }
      render(e) {
        let t = `
				<div style="display: flex; flex-direction: column; width: 100%; height: 100%;">
					<div style="width: 100%; height: 50%"></div>
					<iframe style="width: 100%; height: 50%; outline: none; border: none;"></iframe>
				</div>
			`;
        e.innerHTML = t;
        let i = e.children[0].children[0];
        (this.player = e.children[0].children[1]),
          requestAnimationFrame(() => {
            (this.code = CodeMirror(i, {
              lineNumbers: !0,
              tabSize: 3,
              indentUnit: 3,
              indentWithTabs: !0,
              scrollBarStyle: "native",
              mode: "htmlmixed",
              theme: "monokai",
            })),
              this.code.on("change", () => {
                this.startPlayer();
              }),
              (i.children[0].style.height = "100%"),
              this.setCode(Gs.DEFAULT_CODE);
          });
      }
      setPreAndPostfix(e, t) {
        (this.prefix = e), (this.postfix = t), this.startPlayer();
      }
      setCode(e) {
        this.code.setValue(e), this.startPlayer();
      }
      timerId = 0;
      startPlayer() {
        clearTimeout(this.timerId),
          (this.timerId = setTimeout(() => {
            let e = this.code.getDoc().getValue();
            (e = this.prefix + e + this.postfix),
              (e = window.btoa(e)),
              (this.player.src = ""),
              (this.player.src = "data:text/html;base64," + e);
          }, 500));
      }
    },
    Hs = Gs;
  return (
    B(
      Hs,
      "DEFAULT_CODE",
      `
<script src="https://esotericsoftware.com/files/spine-player/4.1/spine-player.js"><\/script>
<link rel="stylesheet" href="https://esotericsoftware.com/files/spine-player/4.1/spine-player.css">

<div id="player-container" style="width: 100vw; height: 100vh;"></div>

<script>
new spine.SpinePlayer("player-container", {
	jsonUrl: "https://esotericsoftware.com/files/examples/4.1/spineboy/export/spineboy-pro.json",
	atlasUrl: "https://esotericsoftware.com/files/examples/4.1/spineboy/export/spineboy-pma.atlas"
});
<\/script>
		`.trim()
    ),
    nr(ts)
  );
})();

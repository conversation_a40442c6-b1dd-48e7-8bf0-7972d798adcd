const { sum } = require('lodash')
const moment = require("moment");
var crypto = require('crypto');

/**
 * @typedef {{ratio:number,name:string,quantity:number,awarded:number}} Item
 * @param {Item[]} arrRewards
 * @returns {Item}
 */
const randomWheel_GIFT_BOX_GAMI = (arrRewards) => {
  let start, end;
  console.log(`Start at:${(start = new Date().getTime())}`);

  /* filter rewards total_awarded >= total_quantity */
  let rewards = arrRewards.filter((reward) => {
    reward.total_quantity = reward.total_quantity ?? reward.quantity;
    reward.total_awarded = reward.total_awarded ?? reward.awarded;
    if (
      reward.total_quantity > reward.total_awarded ||
      reward.total_quantity == -1
    ) {
      return true;
    }

    return false;
  });

  /* all loots. value start 0 ... index - 1 */
  let itemArr = [];
  let nItems = 0;

  for (let item of rewards) {
    console.log(`Reward:${item.name} ratio:${item.ratio}`);
    nItems += item.ratio;
    let arr = new Array(item.ratio).fill(item);
    itemArr.push(...arr);
  }

  /* shuffle itemArr */
  for (let i = 0; i < nItems; i++) {
    let randomLoot = crypto.randomInt(0, nItems);

    let temp = itemArr[i];
    itemArr[i] = itemArr[randomLoot];
    itemArr[randomLoot] = temp;
  }

  /*  */
  /* gán 1 số 0-n cho n item */
  let nums = new Array(nItems).fill(0).map((i, _index) => _index);

  /* shuffle nums */
  for (let i = 0; i < nItems; i++) {
    let randomLoot = crypto.randomInt(0, nItems);

    let temp = nums[i];
    nums[i] = nums[randomLoot];
    nums[randomLoot] = temp;
  }

  /* gán 1 số cho 1 item. 1-1 */
  let numFlag = {}; //đánh dấu số đang đc item nào giữ

  for (let i = 0; i < nItems; i++) {
    numFlag[nums[i]] = itemArr[i];
    itemArr[i].num = nums[i];
  }

  /* swap num của các item */
  for (let item of itemArr) {
    /* mỗi item chọn 1 số */
    let randomLoot = crypto.randomInt(0, nItems);

    /* swap số cho item đang giữ */
    let temp = numFlag[randomLoot];
    numFlag[randomLoot] = item;
    numFlag[item.num] = temp;

    temp.num = item.num;

    item.num = randomLoot;
  }

  /* chọn 1 số may mắn */
  let luckyNumber = crypto.randomInt(0, nItems);
  console.log(
    `nItems:${nItems} luckyNumber:${luckyNumber} reward:${numFlag[luckyNumber].name}`
  );

  return numFlag[luckyNumber];
};

const TAG = "[randomWheel_GIFT_BOX_GAMI]";
module.exports.test = function test() {
  let start = moment();
  console.log(TAG, `start at:${start.format("YYYY/MM/DD HH:mm:ss")}`);

  let arr = [
    { ratio: 200, name: "Quà 1" },
    { ratio: 500, name: "Quà 2" },
    { ratio: 1000, name: "Quà 3" },
    { ratio: 1555, name: "Quà 4" },
    { ratio: 230, name: "Quà 5" },
    { ratio: 10, name: "Quà 6" },
  ];

  let totalRatio = sum(arr.map((i) => i.ratio));
  let resultStr = {};

  for (let item of arr) {
    item.expectedRatio = `${(item.ratio / totalRatio) * 100}`;
    resultStr[item.name] = 0;
  }

  let numOfTest = 100000;

  for (let i = 0; i < numOfTest; i++) {
    console.log(TAG, `num test:${i + 1}/${numOfTest}`);

    let luckItem = randomWheel_GIFT_BOX_GAMI(
      arr.map((i) => {
        i.quantity = -1;
        return i;
      })
    );

    resultStr[luckItem.name]++;
  }

  for (let item of arr) {
    console.log(
      `Item:${item.name} expectedRatio:${item.expectedRatio}% resultRation:${
        (resultStr[item.name] / numOfTest) * 100
      }%`
    );
  }
  console.log(`Total of test:${numOfTest}`);

  let end = moment();
  console.log(TAG, `end at:${end.format("YYYY/MM/DD HH:mm:ss")}`);
};

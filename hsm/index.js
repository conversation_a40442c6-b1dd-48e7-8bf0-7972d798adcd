var rs = require("jsrsasign");
var rsu = require("jsrsasign-util");
var axios = require("axios");
const path = require("path");
const fs = require("fs");

const HSM_RELYING_PARTY_DN = "EVG";
const HSM_RELYING_PARTY_USER_DN = "EVG";
const HSM_RELYING_PARTY_PASSWORD_DN = "EyC7T3xv";
const HSM_RELYING_PARTY_KEY_STORE_PASSWORD_DN = "fGxwNpPh";
const HSM_RELYING_PARTY_KEY_STORE_DN =
  "EVG.key.pem";
const HSM_REPLYING_PARTY_SIG_DN =
  "McfuHeY5nLEmWgHFZqu02C9gkXiQIKAtVLdMDdd8lVs//pcv0GumVee0Y66MYOUYdWPcexgcqj2+WvahQO+XUlH5++mhzSrdXGkSf/PIJOPCicoKHvKyd+ynv5hthiyvK89QfKPpaH4dBgFutrrZO7ACWW3mR85D/aIbVViH8IsZtWWDE4ukA+0U9gZuqJkBvQ2NItxzG5b0vdrPYM+2a/FBFQCybMLhBZNRziUYocGy5xTN/84bf5uzu4Ur9MDZZvDQh4N/HNw8DdBUbCiuYjwNpPovJLk0P4Tw+BmE4eHzKKohkdZDF8oyDu8xLxfUgewQc7guOIVNaRAO9wQ5gA==";
const HSM_CERT_PROFILE = "PERS.1D-I-CA";
const HSM_PREPARE_FOR_SIGN_CLOUD_URL =
  "https://rssp.i-ca.vn/eSignCloud/restapi/prepareFileForSignCloud";

function getPKCS1Signature(data2sign, privateKey) {
  var sig = new rs.Signature({ alg: "SHA1withRSA" });
  sig.init(privateKey);
  sig.updateString(data2sign);
  var sigVal = sig.sign();
  var base64String = Buffer.from(sigVal, "hex").toString("base64");
  return base64String;
}

async function testA() {
  try {
    /* TODO */
    const relyingParty = HSM_RELYING_PARTY_DN;
    const relyingPartyUser = HSM_RELYING_PARTY_USER_DN;
    const relyingPartyPassword = HSM_RELYING_PARTY_PASSWORD_DN;
    const relyingPartySignature = HSM_REPLYING_PARTY_SIG_DN;
    const relyingPartyKeyStore = path.resolve(HSM_RELYING_PARTY_KEY_STORE_DN);
    const relyingPartyKeyStorePassword =
      HSM_RELYING_PARTY_KEY_STORE_PASSWORD_DN;
    const agreementUUID = "92313497-4a34-46d4-bdfd-c7a07e165ba7";
    const authorizeCode = "97625733";

    var pem = rsu.readFile(relyingPartyKeyStore);
    var prvKey = rs.KEYUTIL.getKey(pem);
    const timestamp = Date.now();
    var data2sign =
      relyingPartyUser +
      relyingPartyPassword +
      relyingPartySignature +
      timestamp;
    var pkcs1Signature = getPKCS1Signature(data2sign, prvKey);

    const mimeType = "application/pdf";
    const pdfFilePath = "sample.pdf";
    const pdfFileName = path.basename(pdfFilePath);
    const pdfBuffer = fs.readFileSync(pdfFilePath);

    /* send request */
    let payload = {
      relyingParty: relyingParty,
      agreementUUID: agreementUUID,
      credentialData: {
        username: relyingPartyUser,
        password: relyingPartyPassword,
        signature: relyingPartySignature,
        pkcs1Signature: pkcs1Signature,
        timestamp: timestamp,
      },
      messagingMode: 3,
      notificationTemplate:
        "ICORP ICA thong bao: {AuthorizeCode} la ma OTP de ky so.",
      timestampEnabled: true,
      ltvEnabled: false,
      postbackEnabled: false,
      noPadding: false,
      p2pEnabled: false,
      csrRequired: false,
      certificateRequired: false,
      keepOldKeysEnabled: false,
      revokeOldCertificateEnabled: false,
      keepCertificateSerialNumberEnabled: false,
      signerInfoConstraintEnabled: false,
      promotion: 0,
      registeredSigningCounter: 0,
      signDocument: false,
      filesLimit: 0,

      authorizeMethod: 4,
      authorizeCode: authorizeCode,
      multipleSigningFileData: [
        {
          signingFileData: Buffer.from(pdfBuffer).toString("base64"),
          signingFileName: pdfFileName,
          mimeType: mimeType,
          signCloudMetaData: {
            singletonSigning: {
              PAGENO: "1",
              COORDINATE: "100,200,400,500",
              TEXTCOLOR: "black",
              VISUALSTATUS: "False",
              SHOWREASON: "True",
              DATETIMEPREFIX: "Ký ngày:",
              SIGNREASON: "Tôi đồng ý",
              SIGNREASONPREFIX: "Lý do:",
              SHOWSIGNERINFO: "True",
              VISIBLESIGNATURE: "True",
              IMAGEANDTEXT: "False",
              SIGNERINFOPREFIX: "Ký bởi:",
              SHOWDATETIME: "True"
            },
          },
        },
      ],
    };

    let response = await axios.post(HSM_PREPARE_FOR_SIGN_CLOUD_URL, payload, {
      responseType: "json",
    });

    console.log(
      "[prepareFileForSignCloud]",
      { payload: JSON.stringify(payload) },
      { response: response.data }
    );

    let data = response.data;

    var signCloudResp = data;
    if (signCloudResp.responseCode == 1018 || signCloudResp.responseCode == 0) {
      console.log("Code: " + signCloudResp.responseCode);
      console.log("Message: " + signCloudResp.responseMessage);
      console.log("BillCode: " + signCloudResp.billCode);
      if (signCloudResp.multipleSignedFileData) {
        _.toArray(signCloudResp.multipleSignedFileData).map((i) => {
          fs.writeFileSync("signed.pdf", i.signedFileData, "base64");
        });
      } else {
        fs.writeFileSync("signed.pdf", signCloudResp.signedFileData, "base64");
      }

      process.exit(0);
    } /* else if (signCloudResp.responseCode == 1004)
            throw new Error(HSM_ERROR_INVALID_AUTHORIZATION_CODE);
          else if (signCloudResp.responseCode == 1002)
            throw new Error(HSM_ERROR_INVALID_PARAMS);
          else if (signCloudResp.responseCode == 1008)
            throw new Error(HSM_ERROR_AGREEMENT_NOT_FOUND); */
    // throw new Error(signCloudResp.responseCode);
    else if (signCloudResp.responseCode == 1007) {
      console.log("Code: " + signCloudResp.responseCode);
      console.log("Message: " + signCloudResp.responseMessage);
      console.log("BillCode: " + signCloudResp.billCode);

      return { billCode: signCloudResp.billCode };
    } else {
      console.warn("prepareFileForSignCloud [FAILED]", signCloudResp);
      return { errorCode: signCloudResp.responseCode };
    }
  } catch (error) {
    console.warn("prepareFileForSignCloud [FAILED]", error.message);
    return { errorCode: error.message };
  }
}

async function testB() {
    try {
      /* TODO */
      const relyingParty = HSM_RELYING_PARTY_DN;
      const relyingPartyUser = HSM_RELYING_PARTY_USER_DN;
      const relyingPartyPassword = HSM_RELYING_PARTY_PASSWORD_DN;
      const relyingPartySignature = HSM_REPLYING_PARTY_SIG_DN;
      const relyingPartyKeyStore = path.resolve(HSM_RELYING_PARTY_KEY_STORE_DN);
      const relyingPartyKeyStorePassword =
        HSM_RELYING_PARTY_KEY_STORE_PASSWORD_DN;
      const agreementUUID = "c8017c6c-217e-4e89-95da-19e1ba043771 ";
      const authorizeCode = "44929889";
  
      var pem = rsu.readFile(relyingPartyKeyStore);
      var prvKey = rs.KEYUTIL.getKey(pem);
      const timestamp = Date.now();
      var data2sign =
        relyingPartyUser +
        relyingPartyPassword +
        relyingPartySignature +
        timestamp;
      var pkcs1Signature = getPKCS1Signature(data2sign, prvKey);
  
      const mimeType = "application/pdf";
      const pdfFilePath = "/home/<USER>/Documents/be-playground/hsm/sample.pdf";
      const pdfFileName = path.basename(pdfFilePath);
      const pdfBuffer = fs.readFileSync(pdfFilePath);
  
      /* send request */
      let payload = {
        relyingParty: relyingParty,
        agreementUUID: agreementUUID,
        credentialData: {
          username: relyingPartyUser,
          password: relyingPartyPassword,
          signature: relyingPartySignature,
          pkcs1Signature: pkcs1Signature,
          timestamp: timestamp,
        },
        messagingMode: 3,
        notificationTemplate:
          "ICORP ICA thong bao: {AuthorizeCode} la ma OTP de ky so.",
        timestampEnabled: true,
        ltvEnabled: false,
        postbackEnabled: false,
        noPadding: false,
        p2pEnabled: false,
        csrRequired: false,
        certificateRequired: false,
        keepOldKeysEnabled: false,
        revokeOldCertificateEnabled: false,
        keepCertificateSerialNumberEnabled: false,
        signerInfoConstraintEnabled: false,
        promotion: 0,
        registeredSigningCounter: 0,
        signDocument: false,
        filesLimit: 0,
  
        authorizeMethod: 4,
        authorizeCode: authorizeCode,
        multipleSigningFileData: [
          {
            signingFileData: Buffer.from(pdfBuffer).toString("base64"),
            signingFileName: pdfFileName,
            mimeType: mimeType,
            signCloudMetaData: {
              singletonSigning: {
                PAGENO: "1",
                COORDINATE: "400,200,800,500",
                TEXTCOLOR: "black",
                VISUALSTATUS: "False",
                SHOWREASON: "True",
                DATETIMEPREFIX: "Ký ngày:",
                SIGNREASON: "Tôi đồng ý",
                SIGNREASONPREFIX: "Lý do:",
                SHOWSIGNERINFO: "True",
                VISIBLESIGNATURE: "True",
                IMAGEANDTEXT: "False",
                SIGNERINFOPREFIX: "Ký bởi:",
                SHOWDATETIME: "True"
              },
            },
          },
        ],
      };
  
      let response = await axios.post(HSM_PREPARE_FOR_SIGN_CLOUD_URL, payload, {
        responseType: "json",
      });
  
      console.log(
        "[prepareFileForSignCloud]",
        { payload: JSON.stringify(payload) },
        { response: response.data }
      );
  
      let data = response.data;
  
      var signCloudResp = data;
      if (signCloudResp.responseCode == 1018 || signCloudResp.responseCode == 0) {
        console.log("Code: " + signCloudResp.responseCode);
        console.log("Message: " + signCloudResp.responseMessage);
        console.log("BillCode: " + signCloudResp.billCode);
        if (signCloudResp.multipleSignedFileData) {
          _.toArray(signCloudResp.multipleSignedFileData).map((i) => {
            fs.writeFileSync("signed.pdf", i.signedFileData, "base64");
          });
        } else {
          fs.writeFileSync("signed.pdf", signCloudResp.signedFileData, "base64");
        }
  
        process.exit(0);
      } /* else if (signCloudResp.responseCode == 1004)
              throw new Error(HSM_ERROR_INVALID_AUTHORIZATION_CODE);
            else if (signCloudResp.responseCode == 1002)
              throw new Error(HSM_ERROR_INVALID_PARAMS);
            else if (signCloudResp.responseCode == 1008)
              throw new Error(HSM_ERROR_AGREEMENT_NOT_FOUND); */
      // throw new Error(signCloudResp.responseCode);
      else if (signCloudResp.responseCode == 1007) {
        console.log("Code: " + signCloudResp.responseCode);
        console.log("Message: " + signCloudResp.responseMessage);
        console.log("BillCode: " + signCloudResp.billCode);
  
        return { billCode: signCloudResp.billCode };
      } else {
        console.warn("prepareFileForSignCloud [FAILED]", signCloudResp);
        return { errorCode: signCloudResp.responseCode };
      }
    } catch (error) {
      console.warn("prepareFileForSignCloud [FAILED]", error.message);
      return { errorCode: error.message };
    }
  }

// testA();
testB();